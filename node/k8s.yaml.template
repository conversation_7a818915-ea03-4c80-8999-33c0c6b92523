apiVersion: v1
kind: Namespace
metadata:
  name: dev

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: node-anytxn-${PROJECT_NAME}-front-${ENV}
  namespace: dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: node-anytxn-${PROJECT_NAME}-front-${ENV}
  template:
    metadata:
      labels:
        app: node-anytxn-${PROJECT_NAME}-front-${ENV}
    spec:
      containers:
      - name: node-anytxn-${PROJECT_NAME}-front-${ENV}
        image: k8s.jrx.com/cub/anytxn-node:latest
        resources:
          requests:
            memory: "256Mi"
          limits:
            memory: "4Gi"
        ports:
        - containerPort: 3000
        volumeMounts:
        - mountPath: /app
          name: app-volume
        - mountPath: /node
          name: node
      volumes:
      - name: app-volume
        hostPath:
          path: /data/file/nfs/node/${ENV}/anytxn-${PROJECT_NAME}-front/
          type: Directory
      - name: node
        hostPath:
          path: /data/file/nfs/node/
          type: Directory