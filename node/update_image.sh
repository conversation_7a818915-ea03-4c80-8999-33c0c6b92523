#!/usr/bin/env bash

# 接收项目名称和环境参数
PROJECT_NAME=${1:-"sales"} # 默认为sales
ENV=${2:-"dev"}           # 默认为dev

# 验证输入参数
if [[ ! "$PROJECT_NAME" =~ ^(sales|tech|collect)$ ]]; then
    echo "Error: PROJECT_NAME must be either 'sales' or 'tech'"
    exit 1
fi

# 设置环境变量
export PROJECT_NAME
export ENV

# 生成目标yaml文件名
YAML_FILE="k8s_${PROJECT_NAME}_${ENV}.yaml"

# 使用模板生成配置文件
envsubst < k8s.yaml.template > $YAML_FILE

echo "Generated $YAML_FILE successfully"

# 应用到kubernetes
kubectl apply -f $YAML_FILE -n dev --record

echo "Deployment updated successfully"

kubectl rollout restart deployment/node-anytxn-${PROJECT_NAME}-front-${ENV} -n dev

echo "kubectl rollout restart successfully"






