# 使用最新版本的Node.js作为基础镜像
FROM k8s.jrx.com/library/node:22.7.0
# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置工作目录为/app
WORKDIR /app

# 暴露端口
EXPOSE 3000

# 启动应用程序
CMD ["sh", "-c", "\
    echo '开始执行应用程序启动流程...' && \
    echo '当前目录文件列表:' && \
    ls -la && \
    date && \
    echo '设置npm阿里巴巴镜像源...' && \
    npm config set -g registry https://registry.npmmirror.com || true && \
    echo '开始安装npm依赖...' && \
    npm install --verbose || true && \
    echo 'npm依赖安装完成' && \
    echo '开始构建应用...' && \
    npm run build --verbose || true && \
    echo '应用构建完成' && \
    sleep 12000000; \
"]
