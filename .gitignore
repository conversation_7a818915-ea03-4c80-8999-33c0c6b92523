target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### IntelliJ IDEA ###
.idea/modules.xml
.idea/jarRepositories.xml
.idea/compiler.xml
.idea/libraries/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store!/logs/
/.idea/
/logs/
*/.idea
*/logs


# 前端工程忽略文件：开始
# dependencies
/anytxn-collect-front/node_modules
# testing
/anytxn-collect-front/coverage
# production
/anytxn-collect-front/build
# misc
.DS_Store
*.swp
*.dia~
# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
# local env files
.env*.local
# tmp
/anytxn-collect-front/.ice
# cache
/anytxn-collect-front/.eslintcache
/anytxn-collect-front/.stylelintcache
/anytxn-collect-front/yarn.lock
# 前端工程忽略文件：结束