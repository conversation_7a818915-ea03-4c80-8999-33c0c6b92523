import { defineConfig } from "@ice/app";
import store from "@ice/plugin-store";

// The project config, see https://v3.ice.work/docs/guide/basic/config
const minify = process.env.NODE_ENV === "production" ? "swc" : false;
export default defineConfig(() => ({
  // Set your configs here.
  ssg: false,
  minify,
  server: {
    onDemand: true,
    format: "esm",
  },
  plugins: [store()],
  // proxy: {
  //   "/api": {
  //     // 测试环境
  //     // target: 'http://10.0.30.2:30611',
  //     // k8s环境
  //     target: "http://10.0.30.2:30611",
  //     // target: 'http://10.0.30.2:30611',
  //     // 黄伟
  //     // target: 'http://192.168.1.15:8080',
  //     // 刘欣
  //     // target: "http://192.168.1.54:8080",

  //     changeOrigin: true,
  //     pathRewrite: { "^/api": "" },
  //     timeout: 30000, // 增加超时时间到30秒
  //   },
  // },
}));
