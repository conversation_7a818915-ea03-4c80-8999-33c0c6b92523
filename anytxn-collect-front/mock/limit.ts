export default {
  // 通用参数查询-分页
  'POST /api/nq/param/pageQuery': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '请求成功',
      success: true,
    },
    data: [
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '9999999999999.99',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '1',
        fixedLimitCalcFmla: 'sum(CUST_06_901*cashLimitPercent,CUST_07_901*1.0)',
        fixedLimitCnstnCond: 'sum(CUST_06_901*cashLimitPercent,CUST_07_901*1.0)',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '1111',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '9',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '1',
        fixedLimitCalcFmla: 'CUST_06_901*1.0',
        fixedLimitCnstnCond: 'CUST_06_901*1.0',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '3',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '1',
        fixedLimitCalcFmla: 'min(ACCT_06_901*cashLimitPercent,limit)',
        fixedLimitCnstnCond: 'min(ACCT_06_901*cashLimitPercent,limit)',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '4',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '0',
        fixedLimitCalcFmla: 'max(ACCT_06_901*1.0,limit)',
        fixedLimitCnstnCond: 'max(ACCT_06_901*1.0,limit)',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '6',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '3',
        fixedLimitCalcFmla: '1+1',
        fixedLimitCnstnCond: 'djkx',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '9',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '3',
        fixedLimitCalcFmla: '1+1',
        fixedLimitCnstnCond: 'djkx',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '787',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '3',
        fixedLimitCalcFmla: '1+1',
        fixedLimitCnstnCond: 'djkx',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '444',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '3',
        fixedLimitCalcFmla: '1+1',
        fixedLimitCnstnCond: 'djkx',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '11112',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '3',
        fixedLimitCalcFmla: '1+1',
        fixedLimitCnstnCond: 'djkx',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '965',
      },
      {
        incId: 1,
        checkItemPortfNo: 'NP01',
        updTlrNo: ' ',
        checkMethod: '1',
        createTlrNo: ' ',
        updTlrOrgNo: ' ',
        updateTime: 1729136384842,
        crcdOrgNo: '999',
        version: 1,
        createTime: 1729065550710,
        paramSts: '1',
        checkItemDesc: '修改描述AAAAA',
        createTlrOrgNo: ' ',
        limitNodeNo: '11',
        limitnodeCallName: 'djaklfjsd',
        limitLevel: 'ACCT',
        ccy: 'rmb',
        recoverFlag: '1',
        useFlag: '1',
        fixedLimitCrMethod: '3',
        fixedLimitCalcFmla: '1+1',
        fixedLimitCnstnCond: 'djkx',
        tempLimitUseFlag: 'N',
        limitNodeTolPct: 1,
        limitNodeTolAmt: 555,
        maxCrLimit: 456,
        id: '3333',
      },
    ],
  },
  // 通用参数维护
  'POST /api/nm/param/maintain': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: null,
  },
  // 通用参数检查
  'POST /api/nm/param/check': {
    header: {
      requestId: '99999999',
      gid: '888888',
    },
    seqNo: '99999',
    paramList: [
      {
        tableName: 'auth_check_def_param',
        bizKey: ['checkItemPortfNo', 'checkMethod'],
        paramData: [
          {
            checkItemPortfNo: 'NP0101',
            checkMethod: '1',
            checkItemDesc: '检查项描述19089889',
            crcdOrgNo: '100',
            version: '1',
            _opType_: 'U',
          },
        ],
      },
    ],
  },
  'POST /api/test': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      { key: 1, value: '正常' },
      { key: 0, value: '异常' },
    ],
  },
  'POST /api/test1': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      { key: 1, value: '正常222' },
      { key: 0, value: '异常1111' },
    ],
  },
  'POST /api/rule': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: {
      ruleType: 'test_001',
      rulePri: '1000',
      ruleId: 'test_001',
      ruleName: 'test_001',
      credOrgNo: '156',
      rule_sts: '1',
      ruleCondField: {
        stsfCondJudge: 'ALL',
        nodeId: 7963185,
        nodeDim: 1,
        nodeNo: '',
        subNode: [
          {
            fieldBid: 'trxChan',
            nodeId: 2927668,
            nodeDim: 2,
            nodeCtx: '交易渠道 （字符型）等于 UPP',
            nodeType: '',
            nodeNo: '1.1',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'STRING',
            value: ['UPP', 'ADD'],
          },
          {
            fieldBid: 'trxSrc',
            fieldName: '交易来源（字符型）',
            nodeId: 1555165,
            nodeDim: 2,
            nodeCtx: '交易来源 （字符型）等于 #CC',
            nodeType: 1,
            nodeNo: '1.2',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'STRING',
            value: ['#CC'],
          },
          {
            fieldBid: 'bizScene',
            fieldName: '业务场景（字符型）',
            nodeId: 7950955,
            nodeDim: 2,
            nodeCtx: '业务场景（字符型）等于 01',
            nodeType: 1,
            nodeNo: '1.3',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'STRING',
            value: ['01'],
          },
          {
            fieldBid: 'msgType',
            fieldName: '报文类型（字符型）',
            nodeId: 4958955,
            nodeDim: 2,
            nodeCtx: '报文类型（字符型）等于 0200',
            nodeType: 1,
            nodeNo: '1.1',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'STRING',
            value: ['0200'],
          },
          {
            fieldBid: 'procCode',
            fieldName: '处理代码（字符型）',
            nodeId: 3984135,
            nodeDim: 2,
            nodeCtx: '处理代码 （字符型）以字符串开头 47',
            nodeType: 1,
            nodeNo: '1.5',
            operateType: 'STARTSWITH',
            opName: '以字符串开头',
            valueType: 'STRING',
            value: ['47'],
          },
        ],
        nodeType: 2,
      },
      ruleFacField: 'trxChan,trxSrc,bizScene,msgType,procCode',
      ruleResultField: [
        {
          trxSeqCode: 'UPP621160',
        },
      ],
      opExpSField: {
        opExps: '565+MIN(233,1)',
      },
    },
  },
  'POST /api/limitUnit': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        id: '1',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元118',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
        rulesTable: [
          {
            id: '1',
            checkOrder: '1',
            ruleName: 'ruleName11',
            limitLevel: 'CUST',
            returnCreditNode: '1',
          },
          {
            id: '2',
            checkOrder: '2',
            ruleName: 'ruleName12',
            limitLevel: 'ACCT',
            returnCreditNode: '2',
          },
        ],
        crUsageTable: [
          {
            id: '1',
            usageGroupCredit: 1,
            node: '节点1节点2节点',
            occupationOder: 2,
            recoveryOrder: 3,
            save: true,
          },
          {
            id: '2',
            usageGroupCredit: 3,
            node: '节点1节点2节点1111',
            occupationOder: 3,
            recoveryOrder: 4,
            save: true,
          },
        ],
      },
      {
        id: '412',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '6',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '7',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '5',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '789',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '8451',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '78956413',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '122',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
      {
        id: '3242',
        limitCtrlUnitNo: '1234',
        limitCtrlUnitName: '管控单元11',
        crcdOrgNo: '101',
        paramSts: '0',
        limitCtrlUnitAppVerNo: 2,
      },
    ],
  },
  'POST /api/dictionary': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: {
      limit: [
        {
          key: '1',
          label: '1234',
        },
        {
          key: '0',
          label: '5795415321',
        },
      ],
      PARAM_STATUS: [
        { key: '0', label: 'invalid' },
        { key: '1', label: 'effective' },
        { key: '9', label: 'delete' },
      ],
    },
  },
  'POST /api/getLimitHistory': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: {
      idNo: '证件号A1000000025',
      crcdCardholderNo: '持卡人号0000100000000001',
      crcdNo: '卡号111',
      crcdAcctNo: '100000010000000001',
      crcdAcctSeqNo: '001',
      crcdAcctCcy: '901',
      data: [
        {
          id: '1',
          paramIndex: '1',
          updTs: '1734077006132',
          limitLevel: 'CUST',
          limitAdjType: 'B',
          limitAdjChanSrc: '调额管道来源',
          limitAdjRsn: '新卡开户',
          updTlrNo: 'user01',
          limitNodeData: [
            {
              limitNodeNo: '01',
              ccy: '901',
              befLimitAdjFixedLimit: 10000,
              afterLimitAdjFixedLimit: 2000,
              fixedLimitAdjFlag: 'PU',
              fixedLimitEffDateBefLimitAdj: '20241214',
              fixedLimitEffDateAfterLimitAdj: '20241231',
              fixedLimitExpireDateBefLimitAdj: '20241216',
              fixedLimitExpireDateAfterLimitAdj: '20241225',
              tempLimitBefLimitAdj: 30000,
              tempLimitAfterLimitAdj: 40000,
              tempLimitAdjFlag: 'TD',
              tempLimitEffDateBefLimitAdj: '20250101',
              tempLimitEffDateAfterLimitAdj: '20250130',
              tempLimitExpireDateBefLimitAdj: '20250201',
              tempLimitExpireDateAfterLimitAdj: '20250228',
              befYearAdjCode: 'A',
              afterYearAdjCode: 'B',
              befCashAdvLimit: 10000,
              afterCashAdvLimit: 2000,
              taxType: 'A',
              everMark: '',
              dbrLimit: 123,
            },
          ],
        },
        {
          id: '2',
          paramIndex: '2',
          updTs: '1734077006132',
          limitLevel: 'ACCT',
          limitNodeNo: '01',
          limitAdjType: 'N',
          limitAdjChanSrc: '调额管道来源1',
          limitAdjRsn: '新卡开户1',
          updTlrNo: 'user01',
        },
      ],
    },
  },
  'POST /api/anytxn-collect-web/business/limit/limitCalculation/query': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: {
      idNo: 'A1000000025',
      crcdCardholderNo: '000000001000000000001',
      crLimitOrig: 12345,
      creditLimitPerm: 145612,
      creditLimitTemp: 7841423,
      crLimitPerm: 213456,
      crLimitTemp: 561566,
      message: 'message123426',
      crcdlimitCalcInfoList: [
        {
          crcdNo: '1234560',
          cmCrLimitPerm: 1234444,
          cmCrLimitTemp: 454121234,
          revolveFlag: 'Y',
          over70Cards: 'Y',
          tempCrLimitFlag: 'Y',
          monthIncome: 45654,
          newLimit: 7777,
          cashAdvFlag: 'N',
          emFlag: 'N',
        },
      ],
    },
  },
};
