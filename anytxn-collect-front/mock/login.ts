export default {
    'POST /api/anytxn-collect-web/login': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: 'token',
    },
    'POST /api/anytxn-collect-web/logout': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: null,
    },
    'POST /api/anytxn-collect-web/user/getUserInfo': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: {
            id: 1,
            userCode: 'caoshun',
            userName: '李文海',
            mobile: '18617320550',
            email: '<EMAIL>',
            createTime: '2024-6-20 12:12:12',
            updateTime: '2024-6-21 13:13:13',
            empName: '研发一部',
            branchName: '广州分行',
        },
    },
    // 按照需求文档最新的接口报文结构编写的菜单mock数据,菜单管理页面获取表格数据
    'POST /api/anytxn-collect-web/user/getUserPermission': {
        header: {
            requestId: '99999999',
            gid: '888888',
            errorCode: '000000',
            errorMsg: '操作成功',
            success: true,
        },
        data: [
            { menuId: 1, parentMenuId: 0, menuName: '用户中心', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
            { menuId: 1001, parentMenuId: 1, menuName: '工作台', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011001, parentMenuId: 1001, menuName: '工作台配置', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/workbenck', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 10011002, parentMenuId: 1001, menuName: '案件监控台', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011003, parentMenuId: 1001, menuName: '电话催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/phoneBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011004, parentMenuId: 1001, menuName: '短信催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/messageBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011005, parentMenuId: 1001, menuName: '专案工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/projectBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011006, parentMenuId: 1001, menuName: '外访催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/visitBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011007, parentMenuId: 1001, menuName: '委外催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/outsourcingBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011008, parentMenuId: 1001, menuName: '法诉催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/legalActionBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10011009, parentMenuId: 1001, menuName: '审批管理', menuType: '0', menuRouteUrl: '/userCenter/baseFunc/aduitManage', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110010, parentMenuId: 1001, menuName: '消息中心', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110011, parentMenuId: 1001, menuName: '任务管理', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110012, parentMenuId: 1001, menuName: '通知与提醒', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110013, parentMenuId: 1001, menuName: '案件跟踪', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110014, parentMenuId: 1001, menuName: '统计与分析', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110015, parentMenuId: 1001, menuName: '培训与学习', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 100110016, parentMenuId: 1001, menuName: '核销工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBenchUser', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
        
            { menuId: 1002, parentMenuId: 1, menuName: '基本功能', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10021001, parentMenuId: 1002, menuName: '用户管理', menuType: '0', menuRouteUrl: '/userCenter/baseFunc/workBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10021002, parentMenuId: 1002, menuName: '角色管理', menuType: '0', menuRouteUrl: '/userCenter/baseFunc/baseFunc', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10021003, parentMenuId: 1002, menuName: '菜单管理', menuType: '0', menuRouteUrl: '/userCenter/baseFunc/baseFunc', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10021004, parentMenuId: 1002, menuName: '权限管理', menuType: '0', menuRouteUrl: '/userCenter/baseFunc/baseFunc', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },

            { menuId: 1003, parentMenuId: 1, menuName: '设置与反馈', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10031001, parentMenuId: 1003, menuName: '个性化设置', menuType: '0', menuRouteUrl: '/userCenter/feedback/feedback', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10031002, parentMenuId: 1003, menuName: '操作日志', menuType: '0', menuRouteUrl: '/userCenter/feedback/feedback', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 10031003, parentMenuId: 1003, menuName: '反馈与投诉', menuType: '0', menuRouteUrl: '/userCenter/feedback/feedback', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },

            { menuId: 2, parentMenuId: 0, menuName: '运营中心', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
            { menuId: 2001, parentMenuId: 2, menuName: '规则管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 2001001, parentMenuId: 2001, menuName: '规则因子管理', menuType: '0', menuRouteUrl: '/operationCenter/rules/ruleFactor', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-ruleF' },
            // { menuId: 2001002, parentMenuId: 2001, menuName: '规则配置', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules' },
            { menuId: 2001002, parentMenuId: 2001, menuName: '规则配置', menuType: '0', menuRouteUrl: '/operationCenter/rules/rules/auth', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules-limit' },
            // { menuId: 2001002001001, parentMenuId: 2001002001, menuName: '查看', menuType: '1', menuRouteUrl: ' ', iconId: '', level: 5, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:rules:rules:limit:check' },
            // { menuId: 2001002002, parentMenuId: 2001002, menuName: '授权', menuType: '0', menuRouteUrl: '/operationCenter/rules/rules/auth', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules-auth' },

            { menuId: 2002, parentMenuId: 2, menuName: '流程管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
            { menuId: 2002001, parentMenuId: 2002, menuName: '案件管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/caseManage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2002002, parentMenuId: 2002, menuName: '节点管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/nodeConfig', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2002003, parentMenuId: 2002, menuName: '标签管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/labelManage', iconId: 'TeamOutlined', level: 4, orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            // { menuId: 2002004, parentMenuId: 2002, menuName: '处理程序管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/nodeFlowManage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            // { menuId: 2002004, parentMenuId: 2002, menuName: '节点流程', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/dispatchManage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2002005, parentMenuId: 2002, menuName: '处理程序管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/attribute', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },

            { menuId: 2003, parentMenuId: 2, menuName: '综合管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
            { menuId: 2003001, parentMenuId: 2003, menuName: '工作台配置', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/workbenck', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003002, parentMenuId: 2003, menuName: '催记管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003003, parentMenuId: 2003, menuName: '质检管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003004, parentMenuId: 2003, menuName: '报表管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003005, parentMenuId: 2003, menuName: '机构管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003006, parentMenuId: 2003, menuName: '绩效管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '5', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003007, parentMenuId: 2003, menuName: '佣金管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '6', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003008, parentMenuId: 2003, menuName: '属地管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '7', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2003009, parentMenuId: 2003, menuName: '资源管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '8', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 20030010, parentMenuId: 2003, menuName: '核销管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '9', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 20030011, parentMenuId: 2003, menuName: '监控管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '10', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },

            { menuId: 2004, parentMenuId: 2, menuName: '智能辅助', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
            { menuId: 2004001, parentMenuId: 2004, menuName: '智能规则辅助', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/aiRuels', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2004002, parentMenuId: 2004, menuName: '会话洞察', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2004003, parentMenuId: 2004, menuName: '智能质检', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
            { menuId: 2004004, parentMenuId: 2004, menuName: '沟通辅助', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },

            { menuId: 3, parentMenuId: 0, menuName: '数据管理', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
            { menuId: 3001, parentMenuId: 3, menuName: '案件数据', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011001, parentMenuId: 3001, menuName: '基本信息数据', menuType: '0', menuRouteUrl: '/sourceManage/case/caseInfo', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011002, parentMenuId: 3001, menuName: '账户信息数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011003, parentMenuId: 3001, menuName: '信贷交易数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011004, parentMenuId: 3001, menuName: '还款记录数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011005, parentMenuId: 3001, menuName: '信用评级数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011006, parentMenuId: 3001, menuName: '消费行为数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011007, parentMenuId: 3001, menuName: '担保信息数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011008, parentMenuId: 3001, menuName: '风险预警数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30011009, parentMenuId: 3001, menuName: '历史催记数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },

            { menuId: 3002, parentMenuId: 3, menuName: '支持数据', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30021001, parentMenuId: 3002, menuName: '风险决策数据', menuType: '0', menuRouteUrl: '/sourceManage/holdOut/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30021002, parentMenuId: 3002, menuName: '特殊标签数据', menuType: '0', menuRouteUrl: '/sourceManage/holdOut/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30021003, parentMenuId: 3002, menuName: '催收话术数据', menuType: '0', menuRouteUrl: '/sourceManage/holdOut/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },

            { menuId: 3003, parentMenuId: 3, menuName: '第三方数据', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031001, parentMenuId: 3003, menuName: '人行征信数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031002, parentMenuId: 3003, menuName: '运营商数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031003, parentMenuId: 3003, menuName: '行外风控数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031004, parentMenuId: 3003, menuName: '裁判文书数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031005, parentMenuId: 3003, menuName: '不动产数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031006, parentMenuId: 3003, menuName: '车辆登记数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031007, parentMenuId: 3003, menuName: '税务数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031008, parentMenuId: 3003, menuName: '法律法规', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            { menuId: 30031009, parentMenuId: 3003, menuName: '行业规范', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
            ]
    },
};
