export default {
  // 额度规则查询页面
  // 'POST /api/nq/getLimitRuleList': {
  //   header: {
  //     requestId: '99999999',
  //     gid: '888888',
  //     errorCode: '000000',
  //     errorMsg: '请求成功',
  //     success: true,
  //   },
  //   data: [
  //     {
  //       incId: 1,
  //       updateTime: '2024/11/100 16:47:35',
  //       crcdOrgNo: '999',
  //       version: 1,
  //       createTime: 1729065550710,
  //       paramSts: '1',
  //       ruleType: 'test_001',
  //       rulePri: '1000',
  //       ruleId: 'test_001',
  //       ruleName: 'test_001',
  //       credOrgNo: '156',
  //       rule_sts: '1',
  //       ruleCondField: {
  //         stsfCondJudge: 'ALL',
  //         nodeId: 7963185,
  //         nodeDim: 1,
  //         nodeNo: '',
  //         subNode: [
  //           {
  //             fieldBid: 'trxChan',
  //             fieldName: '交易渠道 （字符型）',
  //             nodeId: 2927668,
  //             nodeDim: 2,
  //             nodeCtx: '交易渠道 （字符型）等于 UPP',
  //             nodeType: 1,
  //             nodeNo: '1.1',
  //             operateType: 'EQ',
  //             opName: '等于',
  //             valueType: 'CHARACTER_X',
  //             value: ['UPP', 'ADD'],
  //           },
  //           {
  //             fieldBid: 'trxSrc',
  //             fieldName: '交易来源（字符型）',
  //             nodeId: 1555165,
  //             nodeDim: 2,
  //             nodeCtx: '交易来源 （字符型）等于 #CC',
  //             nodeType: 1,
  //             nodeNo: '1.2',
  //             operateType: 'EQ',
  //             opName: '等于',
  //             valueType: 'CHARACTER_X',
  //             value: ['#CC'],
  //           },
  //           {
  //             fieldBid: 'mock2',
  //             fieldName: 'mock （整数型）',
  //             nodeId: 7950955,
  //             nodeDim: 2,
  //             nodeCtx: 'mock （整数型）等于 01',
  //             nodeType: 1,
  //             nodeNo: '1.3',
  //             operateType: 'EQ',
  //             opName: '等于',
  //             valueType: 'CHARACTER_X',
  //             value: ['01'],
  //           },
  //           {
  //             fieldBid: 'mock3',
  //             fieldName: 'mock （布尔型）',
  //             nodeId: 4958955,
  //             nodeDim: 2,
  //             nodeCtx: 'mock （布尔型）等于 是',
  //             nodeType: 1,
  //             nodeNo: '1.1',
  //             operateType: 'EQ',
  //             opName: '等于',
  //             valueType: 'BOOLEAN',
  //             value: ['true'],
  //           },
  //           {
  //             fieldBid: 'mock4',
  //             fieldName: 'mock （数组型）',
  //             nodeId: 3984135,
  //             nodeDim: 2,
  //             nodeCtx: 'mock （数组型）以不在 47',
  //             nodeType: 1,
  //             nodeNo: '1.5',
  //             operateType: 'STARTSWITH',
  //             opName: '不在',
  //             valueType: 'NOTIN',
  //             value: ['47'],
  //           },
  //         ],
  //         nodeType: 2,
  //       },
  //       ruleFacField: 'trxChan,trxSrc,bizScene,msgType,procCode',
  //       ruleResultField: [
  //         {
  //           trxSrc: 'UPP621160',
  //         },
  //       ],
  //       opExpSField: {
  //         opExps: 'MAX(565+MIN(233,1), 1)',
  //         // opExps: 'MAX(565 + 222, 1)',
  //       },
  //     },
  //   ],
  // },
  'POST /api/nq/getLimitRuleList': {
    header: {
      requestId: null,
      gid: null,
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        ruleVerNo: '1.0',
        incId: 2,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'TRX_AMT,ACCT_01_9',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: '{"opExps":"ACCT_01_9+0"}',
        ruleResultField: ' [{"trxSeqCode": "CUP11010"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '帐户层普通卡消費',
        ruleType: 'Limit_ACCT',
        paramSts: '1',
        ruleSts: '1',
        ruleName: 'ANP01',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          ' {"subNode": [{"fieldBid": "TRX_AMT","fieldName": "交易金额","nodeId": 2927668,"nodeDim": 2,"nodeCtx": "交易金额小于等于表达式","nodeType": 1,"nodeNo": "1","operateType": "LE","opName": "小于等于","valueWorldSegType": "OOEXPS","wordSegBid":"opExps"}],"stsCondJudge":"all","nodeId": 195271,"nodeDim": 1,"nodeType": 2,"nodeNo": "1"}',
        execType: '0',
        ruleId: '1',
      },
      {
        ruleVerNo: '1',
        incId: 4,
        rulePri: 111,
        updateUser: 'system',
        ruleFacField: 'trxChan,msgType,procCode,mchCode',
        updateTime: '20241207154143',
        crcdOrgNo: '013',
        version: 1,
        opExpsField: '{"opExps":"MAX(111, 222222)"}',
        ruleResultField: '[]',
        updateOrgNo: '013',
        createTime: '20241105144945',
        ruleTypeSub: ' ',
        ruleDesc: '数据所开卡交易(语音开卡)testtesttest',
        ruleType: 'auth_trx_recoginize_rule',
        paramSts: '0',
        ruleSts: '1',
        ruleName: '数据所开卡交易(语音开卡)',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          '{"nodeId":7963185,"nodeDim":1,"nodeNo":"","nodeType":2,"stsfCondJudge":"ONE","subNode":[{"nodeId":2927668,"nodeDim":2,"nodeNo":"1.1","nodeType":"","operateType":"EQ","opName":"等于","fieldBid":"trxChan","nodeCtx":"交易渠道 （字符型）等于 NCC","valueType":"CHARACTER_X","value":["NCC"]},{"nodeId":4958955,"nodeDim":2,"nodeNo":"1.2","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"msgType","fieldName":"报文类型（字符型）","nodeCtx":"报文类型（字符型）等于 0200","valueType":"CHARACTER_X","value":["0200"]},{"nodeId":3984135,"nodeDim":2,"nodeNo":"1.3","nodeType":1,"operateType":"STARTSWITH","opName":"以字符串开头","fieldBid":"procCode","fieldName":"处理代码（字符型）","nodeCtx":"处理代码 （字符型）以字符串00开头","valueType":"CHARACTER_X","value":["00"]},{"nodeId":3984122,"nodeDim":2,"nodeNo":"1.4","nodeType":1,"operateType":"EQ","opName":"以字符串开头","fieldBid":"mchCode","fieldName":"特店号（字符型）","nodeCtx":"特店号（字符型）以字符串开头 01229004101111","valueType":"CHARACTER_X","value":["01229004101111"]}]}',
        execType: '0',
        ruleId: 'AUTH_TRX_RECG_00001',
      },
      {
        ruleVerNo: '1',
        incId: 5,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'trxSrc,msgType,procCode,mchCode',
        updateTime: '20241105144945',
        crcdOrgNo: '013',
        version: 1,
        opExpsField: ' ',
        ruleResultField: '[{"trxSeqCode":"NCC811020"}]',
        updateOrgNo: '013',
        createTime: '20241105144945',
        ruleTypeSub: ' ',
        ruleDesc: 'PID開卡交易',
        ruleType: 'auth_trx_recoginize_rule',
        paramSts: '1',
        ruleSts: '1',
        ruleName: 'PID開卡交易',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          '{"stsfCondJudge":"ALL","nodeId":7963186,"nodeDim":1,"nodeNo":"","subNode":[{"fieldBid":"trxChan","nodeId":2927667,"nodeDim":2,"nodeCtx":"交易渠道 （字符型）等于 NCC","nodeType":"","nodeNo":"1.1","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["NCC"]},{"fieldBid":"msgType","fieldName":"报文类型（字符型）","nodeId":4958956,"nodeDim":2,"nodeCtx":"报文类型（字符型）等于 0200","nodeType":1,"nodeNo":"1.2","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["0200"]},{"fieldBid":"procCode","fieldName":"处理代码（字符型）","nodeId":3984136,"nodeDim":2,"nodeCtx":"处理代码 （字符型）以字符串00开头","nodeType":1,"nodeNo":"1.3","operateType":"STARTSWITH","opName":"以字符串开头","valueType":"CHARACTER_X","value":["00"]},{"fieldBid":"mchCode","fieldName":"特店号（字符型）","nodeId":3984122,"nodeDim":2,"nodeCtx":"特店号等于","nodeType":1,"nodeNo":"1.4","operateType":"EQ","opName":"以字符串开头","valueType":"CHARACTER_X","value":["8103990"]}],"nodeType":2}',
        execType: '0',
        ruleId: 'AUTH_TRX_RECG_00002',
      },
      {
        ruleVerNo: '1',
        incId: 6,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'trxSrc,msgType,procCode,mchCode',
        updateTime: '20241105144945',
        crcdOrgNo: '013',
        version: 1,
        opExpsField: ' ',
        ruleResultField: '[{"trxSeqCode":"NCC111010"}]',
        updateOrgNo: '013',
        createTime: '20241105144945',
        ruleTypeSub: ' ',
        ruleDesc: 'NCCC一般消費交易',
        ruleType: 'auth_trx_recoginize_rule',
        paramSts: '1',
        ruleSts: '1',
        ruleName: 'NCCC一般消費交易',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          '{"stsfCondJudge":"ALL","nodeId":7963187,"nodeDim":1,"nodeNo":"","subNode":[{"fieldBid":"trxChan","nodeId":2927670,"nodeDim":2,"nodeCtx":"交易渠道 （字符型）等于 NCC","nodeType":"","nodeNo":"1.1","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["NCC"]},{"fieldBid":"msgType","fieldName":"报文类型（字符型）","nodeId":4958957,"nodeDim":2,"nodeCtx":"报文类型（字符型）等于 0200","nodeType":1,"nodeNo":"1.2","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["0200"]},{"fieldBid":"procCode","fieldName":"处理代码（字符型）","nodeId":3984137,"nodeDim":2,"nodeCtx":"处理代码 （字符型）以字符串00开头","nodeType":1,"nodeNo":"1.3","operateType":"STARTSWITH","opName":"以字符串开头","valueType":"CHARACTER_X","value":["00"]}],"nodeType":2}',
        execType: '0',
        ruleId: 'AUTH_TRX_RECG_00003',
      },
      {
        ruleVerNo: '1.0',
        incId: 7,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'trxType,trxSubType',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: ' ',
        ruleResultField: '[{"limitCtrlUnit": "ANP01"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '额度管控单元匹配规则',
        ruleType: 'auth_limit_unit_match_rule',
        paramSts: '1',
        ruleSts: '1',
        ruleName: '消费、预授权额度管控单元匹配规则',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          '{"stsfCondJudge":"ALL","nodeId":2963187,"nodeDim":1,"nodeNo":"","subNode":[{"fieldBid":"trxType","fieldName":"交易类型（字符型）","nodeId":2958957,"nodeDim":2,"nodeCtx":"交易类型（字符型）等于 NP","nodeType":1,"nodeNo":"1.2","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["NP"]}],"nodeType":2}',
        execType: '0',
        ruleId: 'AUTH_LIMIT_UNIT_0001',
      },
      {
        ruleVerNo: '1.0',
        incId: 8,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'trxType,trxChan',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: ' ',
        ruleResultField: '[{"checkItemPortfNo": "GNP101"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '检查组匹配规则',
        ruleType: 'auth_check_ctrl_match_rule',
        paramSts: '1',
        ruleSts: '1',
        ruleName: '消费交易检查组匹配规则',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          '{"stsfCondJudge":"ALL","nodeId":1963187,"nodeDim":1,"nodeNo":"","subNode":[{"fieldBid":"trxChan","nodeId":1927670,"nodeDim":2,"nodeCtx":"交易渠道 （字符型）等于 NCC","nodeType":"","nodeNo":"1.1","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["NCC"]},{"fieldBid":"trxType","fieldName":"交易类型（字符型）","nodeId":1958957,"nodeDim":2,"nodeCtx":"交易类型（字符型）等于 NP","nodeType":1,"nodeNo":"1.2","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["NP"]}],"nodeType":2}\n',
        execType: '0',
        ruleId: 'AUTH_CHECK_CTRL_0001',
      },
      {
        ruleVerNo: '1.0',
        incId: 9,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'trxType,trxChan',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: ' ',
        ruleResultField: '[{"checkItemPortfNo": "GAT101"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '检查组匹配规则',
        ruleType: 'auth_check_ctrl_match_rule',
        paramSts: '1',
        ruleSts: '1',
        ruleName: '开卡交易检查组匹配规则',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          '{"stsfCondJudge":"ALL","nodeId":1963187,"nodeDim":1,"nodeNo":"","subNode":[{"fieldBid":"trxChan","nodeId":1927670,"nodeDim":2,"nodeCtx":"交易渠道 （字符型）等于 NCC","nodeType":"","nodeNo":"1.1","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["NCC"]},{"fieldBid":"trxType","fieldName":"交易类型（字符型）","nodeId":1958957,"nodeDim":2,"nodeCtx":"交易类型（字符型）等于 AT","nodeType":1,"nodeNo":"1.2","operateType":"EQ","opName":"等于","valueType":"CHARACTER_X","value":["AT"]}],"nodeType":2}',
        execType: '0',
        ruleId: 'AUTH_CHECK_CTRL_0002',
      },
      {
        ruleVerNo: '1.0',
        incId: 12,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'TRX_AMT,ACCT_07_9',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: '{"opExps":"ACCT_07_9+0"}',
        ruleResultField: ' [{"trxSeqCode": "CUP11010"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '帐户层普通卡消費',
        ruleType: 'Limit_ACCT',
        paramSts: '1',
        ruleSts: '1',
        ruleName: 'ANP01',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          ' {"subNode": [{"fieldBid": "TRX_AMT","fieldName": "交易金额","nodeId": 2927668,"nodeDim": 2,"nodeCtx": "交易金额小于等于表达式","nodeType": 1,"nodeNo": "1","operateType": "LE","opName": "小于等于","valueWorldSegType": "OOEXPS","wordSegBid":"opExps"}],"stsCondJudge":"all","nodeId": 195271,"nodeDim": 1,"nodeType": 2,"nodeNo": "1"}',
        execType: '0',
        ruleId: '2',
      },
      {
        ruleVerNo: '1.0',
        incId: 100,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'TRX_AMT,CUST_07_9',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: '{"opExps":"CUST_07_9+0"}',
        ruleResultField: ' [{"trxSeqCode": "CUP11010"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '人层普通卡消費',
        ruleType: 'Limit_CUST',
        paramSts: '1',
        ruleSts: '1',
        ruleName: 'ANP01',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          ' {"subNode": [{"fieldBid": "TRX_AMT","fieldName": "交易金额","nodeId": 2927668,"nodeDim": 2,"nodeCtx": "交易金额小于等于表达式","nodeType": 1,"nodeNo": "1","operateType": "LE","opName": "小于等于","valueWorldSegType": "OOEXPS","wordSegBid":"opExps"}],"stsCondJudge":"all","nodeId": 195271,"nodeDim": 1,"nodeType": 2,"nodeNo": "1"}',
        execType: '0',
        ruleId: '3',
      },
      {
        ruleVerNo: '1.0',
        incId: 11,
        rulePri: 100,
        updateUser: 'system',
        ruleFacField: 'TRX_AMT,CUST_01_9',
        updateTime: '20241115104321',
        crcdOrgNo: '013',
        version: 0,
        opExpsField: '{"opExps":"CUST_01_9+0"}',
        ruleResultField: ' [{"trxSeqCode": "CUP11010"}]',
        updateOrgNo: '013',
        createTime: '20241115104321',
        ruleTypeSub: ' ',
        ruleDesc: '人层普通卡消費',
        ruleType: 'Limit_CUST',
        paramSts: '1',
        ruleSts: '1',
        ruleName: 'ANP01',
        createUser: 'system',
        createOrgNo: '013',
        ruleCondField:
          ' {"subNode": [{"fieldBid": "TRX_AMT","fieldName": "交易金额","nodeId": 2927668,"nodeDim": 2,"nodeCtx": "交易金额小于等于表达式","nodeType": 1,"nodeNo": "1","operateType": "LE","opName": "小于等于","valueWorldSegType": "OOEXPS","wordSegBid":"opExps"}],"stsCondJudge":"all","nodeId": 195271,"nodeDim": 1,"nodeType": 2,"nodeNo": "1"}',
        execType: '0',
        ruleId: '4',
      },
    ],
  },
  'POST /api/nq/getRulesData': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '请求成功',
      success: true,
    },
    data: {
      ruleType: 'test_001',
      rulePri: '1000',
      ruleId: 'test_001',
      ruleName: 'test_001',
      credOrgNo: '156',
      rule_sts: '1',
      ruleCondField: {
        stsfCondJudge: 'ALL',
        nodeId: 7963185,
        nodeDim: 1,
        nodeNo: '',
        subNode: [
          {
            fieldBid: 'trxChan',
            fieldName: '交易渠道 （字符型）',
            nodeId: 2927668,
            nodeDim: 2,
            nodeCtx: '交易渠道 （字符型）等于 UPP',
            nodeType: 1,
            nodeNo: '1.1',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'CHARACTER_X',
            value: ['UPP', 'ADD'],
          },
          {
            fieldBid: 'trxSrc',
            fieldName: '交易来源（字符型）',
            nodeId: 1555165,
            nodeDim: 2,
            nodeCtx: '交易来源 （字符型）等于 #CC',
            nodeType: 1,
            nodeNo: '1.2',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'CHARACTER_X',
            value: ['#CC'],
          },
          {
            fieldBid: 'mock2',
            fieldName: 'mock （整数型）',
            nodeId: 7950955,
            nodeDim: 2,
            nodeCtx: 'mock （整数型）等于 01',
            nodeType: 1,
            nodeNo: '1.3',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'CHARACTER_X',
            value: ['01'],
          },
          {
            fieldBid: 'mock3',
            fieldName: 'mock （布尔型）',
            nodeId: 4958955,
            nodeDim: 2,
            nodeCtx: 'mock （布尔型）等于 是',
            nodeType: 1,
            nodeNo: '1.1',
            operateType: 'EQ',
            opName: '等于',
            valueType: 'BOOLEAN',
            value: ['true'],
          },
          {
            fieldBid: 'mock4',
            fieldName: 'mock （数组型）',
            nodeId: 3984135,
            nodeDim: 2,
            nodeCtx: 'mock （数组型）以不在 47',
            nodeType: 1,
            nodeNo: '1.5',
            operateType: 'STARTSWITH',
            opName: '不在',
            valueType: 'NOTIN',
            value: ['47'],
          },
        ],
        nodeType: 2,
      },
      ruleFacField: 'trxChan,trxSrc,bizScene,msgType,procCode',
      ruleResultField: [
        {
          trxSrc: 'UPP621160',
        },
      ],
      opExpSField: {
        opExps: 'MAX(565+MIN(233,1), 1)',
        // opExps: 'MAX(565 + 222, 1)',
      },
    },
  },
  'POST /api/nq/getRuleFac': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '请求成功',
      success: true,
    },
    data: [
      {
        incId: 1,
        ruleFacCode: 'ACCT_01_2',
        ruleFacName: '帐户层01节点',
      },
    ],
  },
  'POST /api/nq/getRuleFacDetail': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '请求成功',
      success: true,
    },
    data: [
      {
        incId: 1,
        ruleFacCode: 'ACCT_01_2',
        ruleFacName: '帐户层01节点',
        ruleType: '1',
        updateTime: '2021-01-01 11:11:11',
      },
    ],
  },
  'POST /api/nq/getRuleFacByRuleType': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '请求成功',
      success: true,
    },
    data: [
      {
        ruleCfgType: '1,3', // 规则配置类型
        ruleFacEnName: 'trxSrc',
        ruleFacCnName: '交易来源（字符型）',
        ruleFacValueType: 5, // 因子值类型
        ruleFacCharType: '1,2,3,4', // 字符限制能输入的
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'trxChan',
        ruleFacCnName: '交易渠道 （字符型）',
        ruleFacValueType: 5, // 因子值类型
        ruleFacMax: 5,
        ruleFacCharType: '1,2,3,4', // 字符限制能输入的
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock1',
        ruleFacCnName: 'mock （字典型）',
        ruleFacValueType: 7, // 因子值类型
        ruleFacRelFieldName: 'trx_seq_code', // 字典
        ruleFacRelParamTableName: 'auth_trx_code_param', // 字典
        ruleFacShowFieldName: 'trx_seq_code', // 字典
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock2',
        ruleFacCnName: 'mock （整数型）',
        ruleFacValueType: 1, // 因子值类型
        ruleFacMax: 100, // 某些类型的最大值
        ruleFacMin: 0, // 最小值
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock3',
        ruleFacCnName: 'mock （布尔型）',
        ruleFacValueType: 9, // 因子值类型
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock4',
        ruleFacCnName: 'mock （数组型）',
        ruleFacValueType: 8, // 因子值类型
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock5',
        ruleFacCnName: 'mock （浮点型）',
        ruleFacValueType: 3, // 因子值类型
        ruleFacMax: 100, // 某些类型的最大值
        ruleFacMin: 0, // 最小值
        ruleFacFloat: 3, // 可输入的小数
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock6',
        ruleFacCnName: 'mock （参数型）',
        ruleFacValueType: 6, // 因子值类型
      },
      {
        ruleCfgType: '2,3', // 规则配置类型
        ruleFacEnName: 'mock7',
        ruleFacCnName: 'mock （字典型）',
        ruleFacValueType: 7, // 因子值类型
      },
    ],
  },
  // 规则因子列表
  'POST /api/anytxn-collect-web/rule/variable/queryRuleVariable': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '请求成功',
      success: true,
    },
    data: [
      {
        key: 'key1',
        name: '欠款金额',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:15',
      },
      {
        key: 'key2',
        name: '延滞天数',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:16',
      },
      {
        key: 'key3',
        name: '不良标识',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:17',
      },
      {
        key: 'key4',
        name: 'VIP标识域',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:18',
      },
      {
        key: 'key5',
        name: '卡产品',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:19',
      },
      {
        key: 'key6',
        name: '当前节点ID',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:100',
      },
      {
        key: 'key7',
        name: '标签ID',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:21',
      },
      {
        key: 'key8',
        name: '证件有效期',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:22',
      },
      {
        key: 'key9',
        name: '学历',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:23',
      },
      {
        key: 'key10',
        name: '年薪',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:24',
      },
      {
        key: 'key11',
        name: '持卡人封锁原因码',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:100',
      },
      {
        key: 'key12',
        name: '账户封锁原因码',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:26',
      },
      {
        key: 'key13',
        name: '卡片封锁原因码',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:27',
      },
      {
        key: 'key14',
        name: '卡产品',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:28',
      },
      {
        key: 'key15',
        name: '卡等级',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:29',
      },
      {
        key: 'key16',
        name: '下一节点ID',
        ruleType: 'node',
        updateTime: '2024-06-21 13:13:30',
      },
    ],
  },
  // 规则列表
  'POST /api/anytxn-collect-web/param/rules/pageQuery': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        ruleId: 'node',
        ruleName: '预处理',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849765,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 预处理","valueType":"STRING","value":["预处理"]}]}',
        ruleResultField: '[{"key16":"决策数据加载"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'label',
        ruleName: '决策数据加载',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849761,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 决策数据加载","valueType":"STRING","value":["决策数据加载"]}]}',
        ruleResultField: '[{"key16":"核销规则"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'rule3',
        ruleName: '核销规则',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849760,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 核销规则","valueType":"STRING","value":["核销规则"]}]}',
        ruleResultField: '[{"key16":"客户标签处理"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'rule4',
        ruleName: '客户标签处理',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849710,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 客户标签处理","valueType":"STRING","value":["客户标签处理"]}]}',
        ruleResultField: '[{"key16":"客户催收标识处理"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'rule5',
        ruleName: '客户催收标识处理',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849711,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 客户催收标识处理","valueType":"STRING","value":["客户催收标识处理"]}]}',
        ruleResultField: '[{"key16":"分板块规则"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'rule6',
        ruleName: '分板块规则',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849712,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 分板块规则","valueType":"STRING","value":["分板块规则"]}]}',
        ruleResultField: '[{"key16":"短信催收队列"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'rule7',
        ruleName: '短信催收队列',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849712,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 短信催收队列","valueType":"STRING","value":["短信催收队列"]}]}',
        ruleResultField: '[{"key16":"短信催收工作台"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      {
        ruleId: 'rule8',
        ruleName: '短信催收工作台',
        ruleType: '节点流程规则',
        paramSts: 'Y-生效',
        createUser: '张得高',
        updateUser: '张得高',
        auditUser: '张得高',
        ruleCondField:
          '{"nodeId":1741250849713,"nodeDim":1,"nodeNo":"1","nodeType":2,"stsfCondJudge":"ALL","subNode":[{"nodeId":"1741250849765-1741250852579","nodeDim":2,"nodeNo":"1.1","nodeType":1,"operateType":"EQ","opName":"等于","fieldBid":"key6","fieldName":"当前节点ID (字符型)","nodeCtx":"当前节点ID (字符型)等于 短信催收工作台","valueType":"STRING","value":["短信催收工作台"]}]}',
        ruleResultField: '[{"key16":"下一节点"}]',
        auditTime: '2023-3-22 15:22:37',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 15:22:37',
      },
      // {
      //   ruleId: 'rule9',
      //   ruleName: '还款意愿判断规则',
      //   ruleType: '标签判断规则',
      //   paramSts: 'Y-生效',
      //   createUser: '张得高',
      //   updateUser: '张得高',
      //   auditUser: '张得高',
      //   auditTime: '2023-3-22 15:22:37',
      //   createTime: '2023-3-22 15:22:37',
      //   updateTime: '2023-3-22 15:22:37',
      // },
      // {
      //   ruleId: 'rule10',
      //   ruleName: '还款能力判断规则',
      //   ruleType: '标签判断规则',
      //   paramSts: 'Y-生效',
      //   createUser: '张得高',
      //   updateUser: '张得高',
      //   auditUser: '张得高',
      //   auditTime: '2023-3-22 15:22:37',
      //   createTime: '2023-3-22 15:22:37',
      //   updateTime: '2023-3-22 15:22:37',
      // },
      // {
      //   ruleId: 'rule11',
      //   ruleName: '蚂蚁智能数据判断',
      //   ruleType: '第三方数据判断规则',
      //   paramSts: 'Y-生效',
      //   createUser: '张得高',
      //   updateUser: '张得高',
      //   auditUser: '张得高',
      //   auditTime: '2023-3-22 15:22:37',
      //   createTime: '2023-3-22 15:22:37',
      //   updateTime: '2023-3-22 15:22:37',
      // },
      // {
      //   ruleId: 'rule12',
      //   ruleName: '中国人民银行数据判断',
      //   ruleType: '第三方数据判断规则',
      //   paramSts: 'Y-生效',
      //   createUser: '张得高',
      //   updateUser: '张得高',
      //   auditUser: '张得高',
      //   auditTime: '2023-3-22 15:22:37',
      //   createTime: '2023-3-22 15:22:37',
      //   updateTime: '2023-3-22 15:22:37',
      // },
      // {
      //   ruleId: 'rule13',
      //   ruleName: '鹏元数据判断',
      //   ruleType: '第三方数据判断规则',
      //   paramSts: 'N-停用',
      //   createUser: '张得高',
      //   updateUser: '张得高',
      //   auditUser: '张得高',
      //   auditTime: '2023-3-22 15:22:37',
      //   createTime: '2023-3-22 15:22:37',
      //   updateTime: '2023-3-22 15:22:37',
      // },
    ],
  },
  // 规则因子
  'POST /api/anytxn-collect-web/rule/variable/queryByRuleType': {
    header: {
      requestId: '99999999',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        key: 'key1',
        name: '欠款金额',
        ruleType: 'node',
        ruleFacCnName: '欠款金额',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key2',
        name: '延滞天数',
        ruleType: 'label',
        ruleFacCnName: '延滞天数',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key3',
        name: '不良标识',
        ruleType: 'rule3',
        ruleFacCnName: '不良标识',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key4',
        name: 'VIP标识域',
        ruleType: 'rule4',
        ruleFacCnName: 'VIP标识域',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key5',
        name: '卡产品',
        ruleType: 'rule5',
        ruleFacCnName: '卡产品',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key6',
        name: '当前节点ID',
        ruleType: 'rule6',
        ruleFacCnName: '当前节点ID',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key7',
        name: '标签ID',
        ruleType: 'rule7',
        ruleFacCnName: '标签ID',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key8',
        name: '证件有效期',
        ruleType: 'rule8',
        ruleFacCnName: '证件有效期',
        valueType: 'CHARACTER_X',
        maxLength: 120,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key9',
        name: '学历',
        ruleType: 'rule9',
        ruleFacCnName: '学历',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key10',
        name: '年薪',
        ruleType: 'rule10',
        ruleFacCnName: '年薪',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key11',
        name: '持卡人封锁原因码',
        ruleType: 'rule11',
        ruleFacCnName: '持卡人封锁原因码',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key12',
        name: '账户封锁原因码',
        ruleType: 'rule12',
        ruleFacCnName: '账户封锁原因码',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key13',
        name: '卡片封锁原因码',
        ruleType: 'rule13',
        ruleFacCnName: '卡片封锁原因码',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key14',
        name: '卡产品',
        ruleType: 'rule14',
        ruleFacCnName: '卡产品',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key15',
        name: '卡等级',
        ruleType: 'rule15',
        ruleFacCnName: '卡等级',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
      {
        key: 'key16',
        name: '下一节点ID',
        ruleType: 'rule16',
        ruleFacCnName: '下一节点ID',
        valueType: 'CHARACTER_X',
        maxLength: 100,
        minLength: 1,
        updateTime: '2024-6-21 13:13:15',
        ruleExecuteType: ['JUDGE', 'OPERATOR', 'RESULT'],
      },
    ],
  },
};
