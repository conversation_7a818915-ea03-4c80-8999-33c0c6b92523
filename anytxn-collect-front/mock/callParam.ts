export default {
  // 获取客群管理列表
  'POST /api/anytxn-collect-web/param/dimensionalManage/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        custName: '案件1',
        custPerson: '111',
        custDesc: '案件描述',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
      {
        custName: '案件2',
        custPerson: '222',
        custDesc: '案件描述',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 编辑客群管理
  'POST /api/anytxn-collect-web/param/dimensionalManage/update': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        custName: '案件1',
        custPerson: '111',
        custDesc: '案件描述',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 新建客群管理
  'POST /api/anytxn-collect-web/param/dimensionalManage/create': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        custName: '案件1',
        custPerson: '111',
        custDesc: '案件描述',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 获取标签管理列表
  'POST /api/anytxn-collect-web/api/tags/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        name: '还款意愿-积极还款',
        tagType: '还款意愿',
        tagAttribute: '常规标签',
        labelDesc: '客户还款意愿积极',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:31',
      },
      {
        name: '还款意愿-消极还款',
        tagType: '还款意愿',
        tagAttribute: '常规标签',
        labelDesc: '客户还款意愿消极',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:31',
      },
      {
        name: '还款能力-充足',
        tagType: '还款能力',
        tagAttribute: '常规标签',
        labelDesc: '客户还款能力充足',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:34',
      },
      {
        name: '还款能力-不足',
        tagType: '还款能力',
        tagAttribute: '常规标签',
        labelDesc: '客户还款能力不足',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:35',
      },
      {
        name: '逾期阶段-M1',
        tagType: '逾期阶段',
        tagAttribute: '常规标签',
        labelDesc: '客户逾期1-30天',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:38',
      },
      {
        name: '逾期阶段-M2',
        tagType: '逾期阶段',
        tagAttribute: '常规标签',
        labelDesc: '客户逾期31-60天',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:39',
      },
      {
        name: '逾期阶段-M3',
        tagType: '逾期阶段',
        tagAttribute: '常规标签',
        labelDesc: '客户逾期61-90天',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:40',
      },
      {
        name: '逾期阶段-M3+',
        tagType: '逾期阶段',
        tagAttribute: '常规标签',
        labelDesc: '客户逾期90天以上',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:41',
      },
      {
        name: '风险等级-A',
        tagType: '风险等级',
        tagAttribute: '常规标签',
        labelDesc: '客户的风险等级高等级',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:42',
      },
      {
        name: '风险等级-B',
        tagType: '风险等级',
        tagAttribute: '常规标签',
        labelDesc: '客户的风险等级中等级',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:43',
      },
      {
        name: '风险等级-C',
        tagType: '风险等级',
        tagAttribute: '常规标签',
        labelDesc: '客户的风险等级低等级',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:44',
      },
      {
        name: '其他标记-0',
        tagType: '其他情况',
        tagAttribute: '常规标签',
        labelDesc: '无其他情况',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:45',
      },
      {
        name: '其他标记-1',
        tagType: '其他情况',
        tagAttribute: '常规标签',
        labelDesc: '失联客户',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:46',
      },
      {
        name: '其他标记-2',
        tagType: '其他情况',
        tagAttribute: '常规标签',
        labelDesc: '争议客户',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:47',
      },
      {
        name: '其他标记-3',
        tagType: '其他情况',
        tagAttribute: '常规标签',
        labelDesc: '困难客户',
        status: '1：生效中',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:48',
      },
      {
        name: '特殊标记-0',
        tagType: '特殊标记',
        tagAttribute: '常规标签',
        labelDesc: '无特殊标记',
        status: '2：已停用',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:49',
      },
      {
        name: '特殊标记-1',
        tagType: '特殊标记',
        tagAttribute: '常规标签',
        labelDesc: '客户公检法案件',
        status: '2：已停用',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:50',
      },
      {
        name: '特殊标记-2',
        tagType: '特殊标记',
        tagAttribute: '常规标签',
        labelDesc: '客户属特殊部门',
        status: '2：已停用',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:51',
      },
      {
        name: '特殊标记-3',
        tagType: '特殊标记',
        tagAttribute: '常规标签',
        labelDesc: '客户属特殊组织',
        status: '2：已停用',
        createUser: 'admin',
        updateUser: '牛三胖',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2023/3/19 16:22:52',
      },
    ],
  },
  // 编辑标签管理
  'POST /api/anytxn-collect-web/api/tags/update': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        custName: '案件1',
        custPerson: '111',
        custDesc: '案件描述',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 新建标签管理
  'POST /api/anytxn-collect-web/api/tags/create': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        custName: '案件1',
        custPerson: '111',
        custDesc: '案件描述',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 获取节点管理列表
  'POST /api/anytxn-collect-web/param/nodeManage/pageQuery': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeCode: '1.1',
        nodeCallName: '预处理',
        nodeFuncDesc: '同步核心的4档（卡/户/人/交易）数据，更新案件状态和信息',
        nodeType: '技术节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        nodeManageList: [
          {
            nodeAttriId: '1',
            nodePriority: '1',
          },
        ],
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:31',
        updateTime: '2023-3-22 14:13:30',
      },
      {
        nodeCode: '2.1',
        nodeCallName: '委外数据同步',
        nodeFuncDesc: '外部结果数据同步，更新案件信息',
        nodeType: '技术节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:32',
        updateTime: '2023-3-22 14:13:31',
      },
      {
        nodeCode: '2.2',
        nodeCallName: '外部结果数据同步',
        nodeFuncDesc: '委外呼叫系统收集结果，更新案件信息',
        nodeType: '技术节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:33',
        updateTime: '2023-3-22 14:13:32',
      },
      {
        nodeCode: '2.3',
        nodeCallName: '状态数据同步',
        nodeFuncDesc: '同步状态数据，更新案件信息',
        nodeType: '技术节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:34',
        updateTime: '2023-3-22 14:13:33',
      },
      {
        nodeCode: '2.4',
        nodeCallName: '大数据模型数据同步',
        nodeFuncDesc: '同步大数据模型数据，更新案件信息',
        nodeType: '技术节点',
        nodeStatus: '2：审核中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:35',
        updateTime: '2023-3-22 14:13:34',
      },
      {
        nodeCode: '2.5.1',
        nodeCallName: '第三方数据同步-A',
        nodeFuncDesc: '同步第三方（人行征信、运营商数据）数据',
        nodeType: '技术节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:36',
        updateTime: '2023-3-22 14:13:35',
      },
      {
        nodeCode: '2.5.2',
        nodeCallName: '第三方数据同步-B',
        nodeFuncDesc: '同步第三方（人行征信、运营商数据）数据',
        nodeType: '技术节点',
        nodeStatus: '2：审核中',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:37',
        updateTime: '2023-3-22 14:13:36',
      },
      {
        nodeCode: '2.5.3',
        nodeCallName: '第三方数据同步-C',
        nodeFuncDesc: '同步第三方（不动产信息、车辆信息等）数据',
        nodeType: '技术节点',
        nodeStatus: '3：停用/失效',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:38',
        updateTime: '2023-3-22 14:13:37',
      },
      {
        nodeCode: '2.5.4',
        nodeCallName: '第三方数据同步-D',
        nodeFuncDesc: '同步第三方（不动产信息、车辆信息等）数据',
        nodeType: '技术节点',
        nodeStatus: '3：停用/失效',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:39',
        updateTime: '2023-3-22 14:13:38',
      },
      {
        nodeCode: '2.5.5',
        nodeCallName: '第三方数据同步-E',
        nodeFuncDesc: '同步第三方（人行征信、运营商数据）数据',
        nodeType: '技术节点',
        nodeStatus: '3：停用/失效',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:40',
        updateTime: '2023-3-22 14:13:39',
      },
      {
        nodeCode: '2.6.1',
        nodeCallName: '标签数据同步-A',
        nodeFuncDesc: '标签签名（公法法签等）',
        nodeType: '业务节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:41',
        updateTime: '2023-3-22 14:13:40',
      },
      {
        nodeCode: '2.6.2',
        nodeCallName: '标签数据同步-B',
        nodeFuncDesc: '特殊职业签名',
        nodeType: '业务节点',
        nodeStatus: '2：审核中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:42',
        updateTime: '2023-3-22 14:13:41',
      },
      {
        nodeCode: '2.6.3',
        nodeCallName: '标签数据同步-C',
        nodeFuncDesc: '特殊信函等',
        nodeType: '业务节点',
        nodeStatus: '3：停用/失效',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:43',
        updateTime: '2023-3-22 14:13:42',
      },
      {
        nodeCode: '2.6.4',
        nodeCallName: '标签数据同步-D',
        nodeFuncDesc: '特殊签名等',
        nodeType: '业务节点',
        nodeStatus: '3：停用/失效',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:44',
        updateTime: '2023-3-22 14:13:43',
      },
      {
        nodeCode: '3.1',
        nodeCallName: '短信催收',
        nodeFuncDesc: '短信催收工作合处理',
        nodeType: '业务节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:45',
        updateTime: '2023-3-22 14:13:44',
      },
      {
        nodeCode: '3.2',
        nodeCallName: '电话催收',
        nodeFuncDesc: '电话催收工作合处理',
        nodeType: '业务节点',
        nodeStatus: '1：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:46',
        updateTime: '2023-3-22 14:13:45',
      },
      {
        nodeCode: '3.3',
        nodeCallName: '信函催收',
        nodeFuncDesc: '信函催收工作合处理',
        nodeType: '业务节点',
        nodeStatus: '2：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:47',
        updateTime: '2023-3-22 14:13:46',
      },
      {
        nodeCode: '3.4',
        nodeCallName: '法论催收',
        nodeFuncDesc: '法论催收工作合处理',
        nodeType: '业务节点',
        nodeStatus: '3：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:48',
        updateTime: '2023-3-22 14:13:47',
      },
      {
        nodeCode: '3.5',
        nodeCallName: '核销催收',
        nodeFuncDesc: '核销催收工作合处理',
        nodeType: '业务节点',
        nodeStatus: '4：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:49',
        updateTime: '2023-3-22 14:13:48',
      },
      {
        nodeCode: '3.6',
        nodeCallName: '委外催收',
        nodeFuncDesc: '委外催收工作合处理',
        nodeType: '业务节点',
        nodeStatus: '5：生效中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:50',
        updateTime: '2023-3-22 14:13:49',
      },
      {
        nodeCode: '4.1',
        nodeCallName: '核心处理',
        nodeFuncDesc: '核心处理工作合处理',
        nodeType: '业务节点',
        nodeStatus: '0：编辑中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:51',
        updateTime: '2023-3-22 14:13:50',
      },
      {
        nodeCode: '5.1',
        nodeCallName: '人工处理',
        nodeFuncDesc: '人工处理工作合处理',
        nodeType: '业务节点',
        nodeStatus: '0：编辑中',
        nodeAuth: 'A:业务员 B:组长',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:52',
        updateTime: '2023-3-22 14:13:51',
      },
      {
        nodeCode: '6.1',
        nodeCallName: '异常处理节点',
        nodeFuncDesc: '处理异常时统一',
        nodeType: '技术节点',
        nodeStatus: '3：停用/失效',
        nodeAuth: 'D:管理员',
        nodePriority: '优先级',
        nodeAttriId: '程序',
        createUser: '蔡虚困',
        updateUser: '蔡虚困',
        aduitUser: '薛腚屙',
        aduitTime: 'admin',
        createTime: '2023-3-22 15:22:53',
        updateTime: '2023-3-22 14:13:52',
      },
    ],
  },
  // 编辑节点管理
  'POST /api/anytxn-collect-web/param/nodeManage/update': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeCallName: '节点1',
        nodeCode: '1',
        nodeType: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 新建节点管理
  'POST /api/anytxn-collect-web/param/nodeManage/create': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeCallName: '节点1',
        nodeCode: '1',
        nodeType: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 获取处理程序管理
  'POST /api/anytxn-collect-web/param/nodeFlowManage/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeFlowId: 'AccountDataSycBatchProcessing',
        nodeFlowName: '账户数据同步批处理',
        nodeFlowDesc: '批量处理信用卡核心下发的账户数好据居文件',
        nodeFlowCode: '1.1',
        nodeFlowUp: '100',
        status: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
      {
        nodeFlowId: 'CustomerAddTagProcessing',
        nodeFlowName: '上客户标签处理',
        nodeFlowDesc: '根据从信用卡核心及各外围渠道获取到的信息，给客户标记标签',
        nodeFlowCode: '5.2,9.1,9. 2, 9.3',
        nodeFlowUp: '100',
        status: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
      {
        nodeFlowId: 'ZHFundAPIIngProcess',
        nodeFlowName: '总行公积金查询接口调用处理',
        nodeFlowDesc: '调用总行公积金查询接口，获取鹅湖公积金相关接口',
        nodeFlowCode: '3,5.2,9.1,9.2,9.3',
        nodeFlowUp: '100',
        status: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
      {
        nodeFlowId: 'ManualContactQueryProcessing',
        nodeFlowName: '人工联系次数查询处理',
        nodeFlowDesc: '查询本案件拨打客户电话的次数',
        nodeFlowCode: '3,5.2,9.1,9.2,9.3',
        nodeFlowUp: '100',
        status: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 编辑处理程序管理
  'POST /api/anytxn-collect-web/param/nodeFlowManage/update': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeCallName: '节点1',
        nodeCode: '1',
        nodeType: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 新建处理程序管理
  'POST /api/anytxn-collect-web/param/nodeFlowManage/create': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeCallName: '节点1',
        nodeCode: '1',
        nodeType: '1',
        createUser: 'admin',
        updateUser: '蔡虚困',
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
      },
    ],
  },
  // 获取程序管理
  'POST /api/anytxn-collect-web/api/processingProgram/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        nodeAttriName: '核心数据同步处理',
        nodeAttriType: '数据同步',
        nodeAttriDesc: '批量处理信用卡核心下发的数据文件',
        nodeAttriCode: '1.1',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '上客户标签处理',
        nodeAttriType: '标签处理',
        nodeAttriDesc: '根据信用卡核心及外围渠道获取的信息，给客户标记',
        nodeAttriCode: '5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '下客户标签处理',
        nodeAttriType: '标签处理',
        nodeAttriDesc: '根据信用卡及各外围渠道获取的信息，给客户除标记',
        nodeAttriCode: '5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '总行公积金查询接口处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用总行公积金查询接口获取客户公积金的相关信息',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '人工联系系统查询处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '查询本系统拨打客户电话的次数',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '蚂蚁智信（杭州）信息技术有限公司查询处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用蚂蚁智信查询接口获取客户信用和支付的相关信息',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '中国人民银行征信中心查询接口处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用中国人民银行征信中心查询接口获取客户征信信息',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '百行征信查询接口处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用百行征信查询接口获取客户征信信息',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '50',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '鹏元学府查询接口处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用鹏元学府查询接口获取客户学历信息',
        nodeAttriCode: '2,5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '查询大数据数据接口处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用大数据接口查询客户大数据相关信息',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '50',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '失联状态生成处理',
        nodeAttriType: '第三接口',
        nodeAttriDesc: '调用失联状态生成接口获取客户数据',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '100',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        nodeAttriName: '生成电险风险池处理',
        nodeAttriType: '本系统接口',
        nodeAttriDesc: '根据业务规则，生成电险风险池',
        nodeAttriCode: '3,5.2,9.1,9.2,9.3',
        nodeAttriUp: '50',
        status: '1-生效',
        createUser: 'Admin',
        updateUser: 'Admin',
        createTime: '2025-03-01 10:12:23',
        updateTime: '2025-03-01 10:12:23',
      },
    ],
  },
  // 获取案件信息
  'POST /api/anytxn-collect-web/param/caseInfo/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        caseId: 'CS20230125-001',
        custName: '李四之',
        custType: '身份证',
        custNo: '101223**********11X',
        status: '在催',
        callNum: '4',
        taskDate: '2023/2/2',
        callTotal: '1',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:31',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-002',
        custName: '刘小丽',
        custType: '身份证',
        custNo: '101223**********13X',
        status: '在催',
        callNum: '3',
        taskDate: '2023/2/3',
        callTotal: '2',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:32',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-003',
        custName: '王大山',
        custType: '身份证',
        custNo: '101223**********15X',
        status: '在催',
        callNum: '2',
        taskDate: '2023/2/4',
        callTotal: '2',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:33',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-004',
        custName: '马东来',
        custType: '身份证',
        custNo: '101223**********17X',
        status: '在催',
        callNum: '5',
        taskDate: '2023/2/5',
        callTotal: '4',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:34',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-005',
        custName: '胡列列',
        custType: '身份证',
        custNo: '101223**********19X',
        status: '在催',
        callNum: '2',
        taskDate: '2023/2/6',
        callTotal: '1',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:35',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-006',
        custName: '钟媛媛',
        custType: '身份证',
        custNo: '101223**********21X',
        status: '在催',
        callNum: '6',
        taskDate: '2023/2/7',
        callTotal: '3',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:36',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-007',
        custName: '廖冠泽',
        custType: '身份证',
        custNo: '101223**********23X',
        status: '出催',
        callNum: '1',
        taskDate: '2023/2/8',
        callTotal: '4',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:37',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-008',
        custName: '史一生',
        custType: '身份证',
        custNo: '101223**********25X',
        status: '出催',
        callNum: '2',
        taskDate: '2023/2/9',
        callTotal: '3',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:38',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-009',
        custName: '洪爱民',
        custType: '身份证',
        custNo: '101223**********27X',
        status: '出催',
        callNum: '1',
        taskDate: '2023/2/10',
        callTotal: '2',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:39',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-010',
        custName: '黄艺皇',
        custType: '身份证',
        custNo: '101223**********29X',
        status: '出催',
        callNum: '2',
        taskDate: '2023/2/11',
        callTotal: '2',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:40',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-011',
        custName: '朱俊',
        custType: '身份证',
        custNo: '101223**********31X',
        status: '出催',
        callNum: '1',
        taskDate: '2023/2/12',
        callTotal: '4',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:41',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-012',
        custName: '金开',
        custType: '身份证',
        custNo: '101223**********33X',
        status: '出催',
        callNum: '3',
        taskDate: '2023/2/13',
        callTotal: '1',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:42',
        updateTime: '2025-03-01 10:12:23',
      },
      {
        caseId: 'CS20230125-013',
        custName: '张恩泽',
        custType: '身份证',
        custNo: '101223**********35X',
        status: '出催',
        callNum: '1',
        taskDate: '2023/2/14',
        callTotal: '1',
        createUser: '李道峰',
        updateUser: '李道峰',
        createTime: '2023/1/25 16:22:43',
        updateTime: '2025-03-01 10:12:23',
      },
    ],
  },
  // 获取审批管理列表
  'POST /api/anytxn-collect-web/param/aduitManage/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        id: 'CS20230320-001',
        aduitPage: '节点管理',
        aduitUser: '',
        aduitStatus: '待审批',
        aduitTime: '',
        applyUser: '谢灵均',
        applyTime: '2023/3/20 9:38:00',
      },
      {
        id: 'CS20230320-002',
        aduitPage: '节点管理',
        aduitUser: '',
        aduitStatus: '待审批',
        aduitTime: '',
        applyUser: '谢灵均',
        applyTime: '2023/3/20 9:38:00',
      },
      {
        id: 'CS20230320-003',
        aduitPage: '处理程序管理',
        aduitUser: '',
        aduitStatus: '待审批',
        aduitTime: '',
        applyUser: '楚云帅',
        applyTime: '2023/3/20 9:38:00',
      },
      {
        id: 'CS20230320-004',
        aduitPage: '处理程序管理',
        aduitUser: '',
        aduitStatus: '待审批',
        aduitTime: '',
        applyUser: '慰迟长歌',
        applyTime: '2023/3/20 9:38:00',
      },
      {
        id: 'CS20230320-005',
        aduitPage: '处理程序管理',
        aduitUser: '',
        aduitStatus: '待审批',
        aduitTime: '',
        applyUser: '谢灵均',
        applyTime: '2023/3/20 9:38:00',
      },
      {
        id: 'CS20230319-001',
        aduitPage: '节点管理',
        aduitUser: '温九卿',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/18 15:22:00',
      },
      {
        id: 'CS20230319-002',
        aduitPage: '节点管理',
        aduitUser: '温九卿',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '楚云帅',
        applyTime: '2023/3/19 15:22:00',
      },
      {
        id: 'CS20230319-003',
        aduitPage: '节点管理',
        aduitUser: '温九卿',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '慰迟长歌',
        applyTime: '2023/3/20 15:22:00',
      },
      {
        id: 'CS20230319-004',
        aduitPage: '处理程序管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/21 15:22:00',
      },
      {
        id: 'CS20230319-005',
        aduitPage: '处理程序管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/22 15:22:00',
      },
      {
        id: 'CS20230319-006',
        aduitPage: '处理程序管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '楚云帅',
        applyTime: '2023/3/23 15:22:00',
      },
      {
        id: 'CS20230319-007',
        aduitPage: '节点管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '慰迟长歌',
        applyTime: '2023/3/24 15:22:00',
      },
      {
        id: 'CS20230319-008',
        aduitPage: '节点管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/25 15:22:00',
      },
      {
        id: 'CS20230319-009',
        aduitPage: '处理程序管理',
        aduitUser: '温九卿',
        aduitStatus: '审批通过',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/26 15:22:00',
      },
      {
        id: 'CS20230319-010',
        aduitPage: '处理程序管理',
        aduitUser: '温九卿',
        aduitStatus: '审批拒绝',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '楚云帅',
        applyTime: '2023/3/27 15:22:00',
      },
      {
        id: 'CS20230319-011',
        aduitPage: '处理程序管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批拒绝',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '慰迟长歌',
        applyTime: '2023/3/28 15:22:00',
      },
      {
        id: 'CS20230319-012',
        aduitPage: '节点管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批拒绝',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/29 15:22:00',
      },
      {
        id: 'CS20230319-013',
        aduitPage: '节点管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批拒绝',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '谢灵均',
        applyTime: '2023/3/21 15:22:00',
      },
      {
        id: 'CS20230319-014',
        aduitPage: '节点管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批拒绝',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '楚云帅',
        applyTime: '2023/3/22 15:22:00',
      },
      {
        id: 'CS20230319-015',
        aduitPage: '处理程序管理',
        aduitUser: '楚云帅',
        aduitStatus: '审批拒绝',
        aduitTime: '2023/3/19 16:22:00',
        applyUser: '慰迟长歌',
        applyTime: '2023/3/23 15:22:00',
      },
    ],
  },
  // 综合管理/工作台配置列表
  'POST /api/anytxn-collect-web/param/workBenchManage/query': {
    header: {
      requestId: '********',
      gid: '888888',
      errorCode: '000000',
      errorMsg: '操作成功',
      success: true,
    },
    data: [
      {
        userId: '8763112',
        userName: '张峻峰',
        authPage: '1',
        rightAction: ['1', '2', '3', '4', '5'],
        rightActionCol: '催收信息，客户信息，联系信息，账户信息，卡片信息',
        leftAction: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'i', 'G', 'H', 'I', 'J', 'K', 'L'],
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:13:15',
        status: '1',
        updateUser: '李白',
      },
      {
        userId: '8763112',
        userName: '张峻峰',
        authPage: '2',
        rightAction: ['1', '2', '3'],
        rightActionCol: '催收信息，客户信息，联系信息',
        leftAction: ['A', 'B', 'C', 'D', 'E', 'F'],
        createTime: '2024-6-21 13:13:15',
        updateTime: '2024-6-21 13:14:15',
        status: '1',
        updateUser: '蔡徐坤',
      },
    ],
  },
};
