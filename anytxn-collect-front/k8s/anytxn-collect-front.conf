server {
    listen       8080;
    server_name  10.0.30.2 10.0.30.3 10.0.30.4 10.0.30.5 10.0.30.6 10.0.30.7 10.0.30.8 10.0.30.9;

    location / {
      root   /usr/share/nginx/anytxn-collect-front;
      index  index.html;
      try_files $uri $uri/ index.html;
    }

    # 后端代理
    location /api/ {
        proxy_pass http://10.0.30.2:30611/;
    }

    location /system {
      rewrite ^.*$ / last;
      try_files $uri $uri/ index.html;
    }

    location /serviceParam {
      rewrite ^.*$ / last;
      try_files $uri $uri/ index.html;
    }

    location /service {
      rewrite ^.*$ / last;
      try_files $uri $uri/ index.html;
    }

    location /login {
      rewrite ^.*$ / last;
      try_files $uri $uri/ index.html;
    }

    # location ~ /(login|system|service|service) {
    #   rewrite ^.*$ / last;
    #   try_files $uri $uri/ index.html;
    # }

    error_page   403 404  /;
    location = /40x.html {
        root   /usr/local/nginx/html/build;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   index.html;
    }

}