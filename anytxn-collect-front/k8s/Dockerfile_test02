# 使用最新版本的Nginx作为基础镜像
FROM k8s.jrx.com/library/nginx:1.27.3

# 设置时区为北京时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建用于存放静态文件的目录
RUN mkdir /usr/share/nginx/anytxn-collect-front \
    # 删除默认的nginx配置文件
    && rm -rf /etc/nginx/nginx.conf \
    # 删除任何现有的自定义nginx配置文件
    && rm -rf /etc/nginx/anytxn-collect-front.conf

# 复制自定义的nginx配置文件到nginx配置目录
COPY ./nginx.conf /etc/nginx/nginx.conf

# 复制自定义的anytxn-collect-front配置文件到nginx配置目录
COPY ./anytxn-collect-front-test02.conf /etc/nginx/anytxn-collect-front.conf

# 将构建好的静态代码复制到nginx的静态文件目录
COPY ./build/ /usr/share/nginx/anytxn-collect-front

# 暴露nginx服务的端口
EXPOSE 8080
