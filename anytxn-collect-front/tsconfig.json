{"compilerOptions": {"baseUrl": "./", "module": "ESNext", "target": "ESNext", "lib": ["DOM", "ESNext", "DOM.Iterable"], "jsx": "react-jsx", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": false, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": false, "importHelpers": true, "resolveJsonModule": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "skipLibCheck": true, "paths": {"@/*": ["./src/*"], "ice": [".ice"]}, "esModuleInterop": true}, "include": ["src", ".ice"], "exclude": ["build"]}