{"name": "cub-frontend", "version": "1.0.0", "description": "A new ice.js project.", "dependencies": {"@ant-design/pro-components": "^2.8.2", "@antv/g6": "^5.0.44", "@antv/g6-extension-react": "^0.2.0", "@ice/runtime": "^1.4.13", "@tensorflow/tfjs": "^4.22.0", "antd": "^5.24.3", "axios": "^1.7.7", "classnames": "^2.5.1", "dayjs": "^1.11.13", "echarts": "^5.5.1", "js-cookie": "^3.0.5", "json-bigint": "^1.0.0", "lodash": "^4.17.21", "mathjs": "14.0.0", "motion": "^11.13.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-intl": "^6.8.7", "styled-components": "^6.1.15"}, "devDependencies": {"@applint/spec": "^1.2.3", "@ice/app": "^3.4.12", "@ice/plugin-store": "^1.1.2", "@testing-library/jest-dom": "^6.6.2", "@testing-library/react": "^16.0.1", "@types/node": "^22.8.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "eslint": "^8.35.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "stylelint": "^15.2.0", "typescript": "^5.6.3"}, "scripts": {"start": "ice start --speedup", "build": "ice build", "eslint": "eslint ./src --cache --ext .js,.jsx,.ts,.tsx", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"src/**/*.{css,scss,less}\" --cache", "stylelint:fix": "npm run stylelint -- --fix"}, "publishConfig": {"access": "public"}, "repository": "**************:ice-lab/react-materials.git"}