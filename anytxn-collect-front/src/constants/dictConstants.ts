import urlConstants from "./urlConstants";

// 字段或参数枚举
const DICT_TYPE = {
  dict: "dict",
  param: "param",
};

const DICT_CONSTANTS = {
  // 页面用得较多的需要取字典或参数的字段配置
  DICT_ENUM_MAP: {
    crcdOrgNo: {
      type: DICT_TYPE.dict,
      // 对应的service名
      url: urlConstants.BASE_ORG_PARAM.LIST,
      // select的option转换的key和value，需要的拼接的再加上showKey
      optionKey: "crcdOrgNo",
      optionValue: "crcdOrgNo",
    },
    nodeAttriId: {
      type: DICT_TYPE.param,
      // 对应的service名
      url: urlConstants.ATTRIBUTE_MANAGE.LIST,
      // select的option转换的key和value，需要的拼接的再加上showKey
      optionKey: "nodeAttriId",
      // showKey: "nodeAttriId",
      optionValue: "nodeAttriName",
    },
    nodeRule: {
      type: DICT_TYPE.param,
      // 对应的service名
      url: urlConstants.RULES_AUTH.LIST,
      // select的option转换的key和value，需要的拼接的再加上showKey
      serviceType: "business",
      optionKey: "ruleId",
      optionValue: "ruleName",
    },
    nodeRuleFactor: {
      type: DICT_TYPE.param,
      url: urlConstants.RULE_FACTOR.LIST,
      // select的option转换的key和value，需要的拼接的再加上showKey
      optionKey: "key",
      optionValue: "name",
    },
  },
  // 分页可选条数
  PAGEOPTIONS: [
    { label: "page_10", value: 10 },
    { label: "page_20", value: 20 },
    { label: "page_50", value: 50 },
    { label: "page_100", value: 100 },
  ],
  // 状态枚举
  PARAM_STATUS: [
    { key: "0", value: "invalid" },
    { key: "1", value: "effective" },
  ],
  // 标签类型
  LABEL_TYPE: [
    { key: "1", value: "labelType1" },
    { key: "2", value: "labelType2" },
    { key: "3", value: "labelType3" },
    { key: "4", value: "labelType4" },
    { key: "5", value: "labelType5" },
    { key: "6", value: "labelType6" },
  ],
  // 标签来源
  LABEL_NAME_SRC: [
    { key: "1", value: "labelNameSrc1" },
    { key: "2", value: "labelNameSrc2" },
    { key: "3", value: "labelNameSrc3" },
  ],
  // 节点权限
  NODE_AUTH: [
    { key: "A", value: "nodeAuth1" },
    { key: "B", value: "nodeAuth2" },
    { key: "C", value: "nodeAuth3" },
    { key: "D", value: "nodeAuth4" },
  ],
  // 节点类型
  NODE_TYPE: [
    { key: "0", value: "nodeType0" },
    { key: "1", value: "nodeType1" },
    { key: "2", value: "nodeType2" },
  ],
  // 节点状态
  NODE_STATUS: [
    { key: "0", value: "nodeStatus1" },
    { key: "1", value: "nodeStatus2" },
    { key: "2", value: "nodeStatus3" },
    { key: "3", value: "nodeStatus4" },
  ],
  // 程序类型
  NODE_ATTRI_TYPE: [
    { key: "1", value: "nodeAttriType1" },
    { key: "2", value: "nodeAttriType3" },
    { key: "3", value: "nodeAttriType4" },
  ],
  // 配置页面
  AUTH_PAGE: [
    { key: "1", value: "authPage1" },
    { key: "2", value: "authPage2" },
    { key: "3", value: "authPage3" },
    { key: "4", value: "authPage4" },
    { key: "5", value: "authPage5" },
    { key: "6", value: "authPage6" },
  ],
  // 右侧Tab
  RIGHT_ACTION: [
    { key: "infoForm1", value: "rightAction1", label: "催收信息" },
    { key: "infoForm2", value: "rightAction2", label: "客户信息" },
    { key: "infoForm3", value: "rightAction3", label: "联系信息" },
    { key: "infoForm4", value: "rightAction4", label: "账户信息" },
    { key: "infoForm5", value: "rightAction5", label: "卡片信息" },
  ],
  // 左侧功能
  LEFT_ACTION: [
    { key: "A", value: "leftAction1", label: "转案" },
    { key: "B", value: "leftAction2", label: "提前支付" },
    { key: "C", value: "leftAction3", label: "止付" },
    { key: "D", value: "leftAction4", label: "减免" },
    { key: "E", value: "leftAction5", label: "保证金抵欠" },
    { key: "F", value: "leftAction6", label: "同名账户调账" },
    { key: "G", value: "leftAction7", label: "上除强催标" },
    { key: "H", value: "leftAction8", label: "协商分期" },
    { key: "I", value: "leftAction9", label: "轻财订单" },
    { key: "J", value: "leftAction10", label: "信息修复" },
    { key: "K", value: "leftAction11", label: "短信" },
    { key: "L", value: "leftAction12", label: "微信" },
    { key: "M", value: "leftAction13", label: "APP" },
    { key: "N", value: "leftAction14", label: "电邮" },
    { key: "O", value: "leftAction15", label: "信函" },
  ],
  // 展示字段
  CENTER_FILES: [
    { key: "caseCode", value: "caseCode", disabled: true },
    { key: "orgCustNbr", value: "orgCustNbr", disabled: true },
    { key: "custName", value: "custName", disabled: true },
    { key: "icType", value: "icType", disabled: true },
    { key: "custIc", value: "custIc", disabled: true },
    { key: "chargoffFlag", value: "chargoffFlag" },
    { key: "countOdue", value: "countOdue" },
    { key: "dteIntoCollection", value: "dteIntoCollection" },
    { key: "dteOutCollection", value: "dteOutCollection" },
    { key: "firstDate", value: "firstDate" },
    { key: "lastCaseCode", value: "lastCaseCode" },
    { key: "lastCaseDate", value: "lastCaseDate" },
    { key: "lawDate", value: "lawDate" },
    { key: "lawFlag", value: "lawFlag" },
    { key: "model", value: "model" },
    { key: "currState", value: "currState" },
    { key: "caseState", value: "caseState" },
    { key: "teamCode", value: "teamCode" },
    { key: "lastOpeTime", value: "lastOpeTime" },
    { key: "inTeamDate", value: "inTeamDate" },
    { key: "reasonCode", value: "reasonCode" },
    { key: "noneContactRuleCode", value: "noneContactRuleCode" },
    { key: "contactCode", value: "contactCode" },
    { key: "contactTimes", value: "contactTimes" },
    { key: "resultTime", value: "resultTime" },
    { key: "resultDirection", value: "resultDirection" },
    { key: "attemptCount", value: "attemptCount" },
    { key: "callTotal", value: "callTotal" },
    { key: "lastModel", value: "lastModel" },
    { key: "ename", value: "ename" },
    { key: "eusex", value: "eusex" },
    { key: "statementTypeAll", value: "statementTypeAll" },
    { key: "billingCycle", value: "billingCycle" },
    { key: "badnessCode", value: "badnessCode" },
    { key: "classIBalance", value: "classIBalance" },
    { key: "riskRank", value: "riskRank" },
    { key: "delayDays", value: "delayDays" },
    { key: "activeInstallmentFlag", value: "activeInstallmentFlag" },
    { key: "productType", value: "productType" },
    { key: "status24", value: "status24" },
    { key: "wcsOutcode", value: "wcsOutcode" },
    { key: "handType", value: "handType" },
    // {
    //   key: "rightAction2",
    //   value: "rightAction2",
    // },
    // {
    //   key: "rightAction3",
    //   value: "rightAction3",
    // },
  ],
  // 规则类型
  RULE_TYPE: [
    { key: "node", value: "OperationRules" },
    // { key: "label", value: "label" },
    { key: "labelHanldle", value: "LabelHanldle" },
  ],
  // 规则因子类型
  RULE_FACTORS_TYPE: [
    // { key: 'DIC', label: '字典型', value: 'DIC' },
    { key: "INT_X", label: "整数型", value: "INT_X" },
    // { key: 'DOUBLE_X', label: '浮点型', value: 'DOUBLE_X' },
    // { key: 'AMOUNT', label: '金额型', value: 'AMOUNT' },
    { key: "CHARACTER_X", label: "字符型", value: "CHARACTER_X" },
    // { key: 'BOOLEAN_X', label: '布尔型', value: 'BOOLEAN_X' },
    // { key: 'ARRAY', label: '数组型', value: 'ARRAY' },
    // { key: 'PARAMETER', label: '参数型', value: 'PARAMETER' },
    // { key: 'LONG_X', label: '长整型', value: 'LONG_X' },
  ],
  // 规则配置类型
  RULE_OPTION_TYPE: [
    { key: "JUDGE", value: "JUDGE" },
    { key: "OPERATOR", value: "OPERATOR" },
    { key: "RESULT", value: "RESULT" },
  ],
  // 规则限制输入字符
  CHARACTER: [
    { key: "capital", value: "大写字母" },
    { key: "lowercase", value: "小写字母" },
    { key: "character", value: "特殊字符" },
    { key: "chinese", value: "中文" },
    { key: "number", value: "数字" },
  ],
  // 标签属性
  LABEL_ATTRIBUTE: [
    { key: "A", value: "attribute1" },
    { key: "B", value: "attribute2" },
    { key: "C", value: "attribute3" },
  ],
  // 标签来源
  LABEL_SOURCE: [
    { key: "1", value: "labelSource1" },
    { key: "2", value: "labelSource2" },
    { key: "3", value: "labelSource3" },
  ],
  // 规则运算符
  RULE_OPERATOR_TYPE: [
    { key: "EQ", value: "EQ" },
    { key: "GT", value: "GT" },
    { key: "LT", value: "LT" },
  ],
  // 所属模块
  WORKBENCH_MODEL: [
    { key: "D", value: "model1" },
    { key: "W", value: "model2" },
    { key: "S", value: "model3" },
    { key: "X", value: "model4" },
    { key: "H", value: "model5" },
    { key: "Z", value: "model6" },
  ],
  // 案件管理-所属模块
  CASEMANAGE_MODEL: [
    { key: "D", value: "model1" },
    { key: "W", value: "model2" },
    { key: "S", value: "model3" },
    { key: "X", value: "model4" },
    { key: "H", value: "model5" },
    { key: "Z", value: "model6" },
  ],
  // 案件当前状态
  WORKBENCH_CURR_STATE: [
    { key: "P", value: "pending" },
    { key: "C", value: "completed" },
  ],
  // 案件状态
  WORKBENCH_CASE_STATE: [
    { key: "1", value: "inCollection" },
    { key: "0", value: "outCollection" },
  ],
  // 岗位
  STATION: [
    { key: "1", value: "station1" },
    { key: "2", value: "station2" },
    { key: "3", value: "station3" },
  ],
  // 用户管理相关字典
  GENDER: [
    { key: "M", value: "Male" },
    { key: "F", value: "Female" },
  ],
  LANGUAGE: [
    { key: "zh", value: "Mandarin" },
    { key: "yue", value: "Cantonese" },
    { key: "en", value: "English" },
    { key: "other", value: "Other" },
  ],
  LEVEL: [
    { key: "1", value: "Junior" },
    { key: "2", value: "Intermediate" },
    { key: "3", value: "Senior" },
  ],
  YES_NO: [
    { key: "Y", value: "Yes" },
    { key: "N", value: "No" },
  ],
};
export default DICT_CONSTANTS;
