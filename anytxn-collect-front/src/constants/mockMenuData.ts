

export default {
  header: {
    requestId: '99999999',
    gid: '888888',
    errorCode: '000000',
    errorMsg: 'SUCCESS',
    success: true,
  },
  data: {
    "en": [
      { menuId: 1, parentMenuId: 0, menuName: 'User Center', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
      { menuId: 1001, parentMenuId: 1, menuName: 'Workbench', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      // { menuId: ********, parentMenuId: 1001, menuName: 'Workbench Configuration', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/workbenck', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 10011002, parentMenuId: 1001, menuName: 'Case Monitoring', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10011003, parentMenuId: 1001, menuName: 'Phone Collection Workbench', menuType: '0', menuRouteUrl: '/userCenter/workBench/phoneBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10011004, parentMenuId: 1001, menuName: 'SMS Collection Workbench', menuType: '0', menuRouteUrl: '/userCenter/workBench/messageBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10011009, parentMenuId: 1001, menuName: 'Approval Management', menuType: '0', menuRouteUrl: '/userCenter/authManage/aduitManage', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      
      { menuId: 1002, parentMenuId: 1, menuName: 'Auth Management', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021001, parentMenuId: 1002, menuName: 'User Management', menuType: '0', menuRouteUrl: '/userCenter/authManage/user', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021002, parentMenuId: 1002, menuName: 'Role Management', menuType: '0', menuRouteUrl: '/userCenter/authManage/role', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021003, parentMenuId: 1002, menuName: 'Station Management', menuType: '0', menuRouteUrl: '/userCenter/authManage/position', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021004, parentMenuId: 1002, menuName: 'Org Management', menuType: '0', menuRouteUrl: '/userCenter/authManage/org', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
  
      { menuId: 1003, parentMenuId: 1, menuName: 'Settings and Feedback', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10031001, parentMenuId: 1003, menuName: 'Personalization Settings', menuType: '0', menuRouteUrl: '/userCenter/feedback/feedback', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10031002, parentMenuId: 1003, menuName: 'Operation Logs', menuType: '0', menuRouteUrl: '/userCenter/feedback/feedback', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10031003, parentMenuId: 1003, menuName: 'Feedback and Complaints', menuType: '0', menuRouteUrl: '/userCenter/feedback/feedback', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
  
      { menuId: 2, parentMenuId: 0, menuName: 'Operations Center', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
      { menuId: 2000, parentMenuId: 2, menuName: 'Case Management', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/caseManage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2001, parentMenuId: 2, menuName: 'Rule Management', menuType: '0', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 2001001, parentMenuId: 2001, menuName: 'Rule Factor Management', menuType: '0', menuRouteUrl: '/operationCenter/rules/ruleFactor', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-ruleF' },
      // { menuId: 2001002, parentMenuId: 2001, menuName: '规则配置', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules' },
      { menuId: 2001002, parentMenuId: 2001, menuName: 'Rule Configuration', menuType: '0', menuRouteUrl: '/operationCenter/rules/rules/auth', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules-limit' },
      // { menuId: 2001002001001, parentMenuId: 2001002001, menuName: '查看', menuType: '1', menuRouteUrl: ' ', iconId: '', level: 5, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:rules:rules:limit:check' },
      // { menuId: 2001002002, parentMenuId: 2001002, menuName: '授权', menuType: '0', menuRouteUrl: '/operationCenter/rules/rules/auth', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules-auth' },
  
      { menuId: 2002, parentMenuId: 2, menuName: 'Process Management', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
      // { menuId: 2002001, parentMenuId: 2002, menuName: 'Case Management', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/caseManage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2002002, parentMenuId: 2002, menuName: 'Node Management', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/nodeConfig', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2002003, parentMenuId: 2002, menuName: 'Label Management', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/labelManage', iconId: 'TeamOutlined', level: 4, orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      // { menuId: 2002004, parentMenuId: 2002, menuName: '程序管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/nodeFlowManage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      // { menuId: 2002004, parentMenuId: 2002, menuName: '节点流程', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/dispatchManage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2002005, parentMenuId: 2002, menuName: 'Program Management', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/attribute', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
  
      { menuId: 2003, parentMenuId: 2, menuName: 'General Management', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
      // { menuId: 2003001, parentMenuId: 2003, menuName: '工作台配置', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/workbenck', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2003002, parentMenuId: 2003, menuName: 'Position Management', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/stationManagement', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2003003, parentMenuId: 2003, menuName: 'Staff Management', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/staffManagement', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
    
      { menuId: 2004, parentMenuId: 2, menuName: 'AI Assistant', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
      { menuId: 2004001, parentMenuId: 2004, menuName: 'AI Rules Assistant', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/aiRuels', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2004002, parentMenuId: 2004, menuName: 'Conversation Insights', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2004003, parentMenuId: 2004, menuName: 'Smart Quality Check', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2004004, parentMenuId: 2004, menuName: 'Communication Assistant', menuType: '0', menuRouteUrl: '/operationCenter/intelligence/manage', iconId: 'TeamOutlined', level: 4, orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
  
      { menuId: 3, parentMenuId: 0, menuName: 'Data Management', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
      { menuId: 3001, parentMenuId: 3, menuName: 'Case Data', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: ********, parentMenuId: 3001, menuName: 'Basic Information Data', menuType: '0', menuRouteUrl: '/sourceManage/case/caseInfo', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: ********, parentMenuId: 3001, menuName: 'Account Information Data', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      ],
    "zh-cn": [
      { menuId: 1, parentMenuId: 0, menuName: '用户中心', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
      { menuId: 1001, parentMenuId: 1, menuName: '工作台', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      // { menuId: ********, parentMenuId: 1001, menuName: '工作台配置', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/workbenck', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 10011002, parentMenuId: 1001, menuName: '案件监控台', menuType: '0', menuRouteUrl: '/userCenter/workBench/workBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10011003, parentMenuId: 1001, menuName: '电话催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/phoneBench', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10011004, parentMenuId: 1001, menuName: '短信催收工作台', menuType: '0', menuRouteUrl: '/userCenter/workBench/messageBench', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10011009, parentMenuId: 1001, menuName: '审批管理', menuType: '0', menuRouteUrl: '/userCenter/authManage/aduitManage', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },

      { menuId: 1002, parentMenuId: 1, menuName: '权限管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021001, parentMenuId: 1002, menuName: '用户管理', menuType: '0', menuRouteUrl: '/userCenter/authManage/user', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021002, parentMenuId: 1002, menuName: '角色管理', menuType: '0', menuRouteUrl: '/userCenter/authManage/role', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021003, parentMenuId: 1002, menuName: '岗位管理', menuType: '0', menuRouteUrl: '/userCenter/authManage/position', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 10021004, parentMenuId: 1002, menuName: '机构管理', menuType: '0', menuRouteUrl: '/userCenter/authManage/org', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },

      { menuId: 2, parentMenuId: 0, menuName: '运营中心', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
      { menuId: 2000, parentMenuId: 2, menuName: '案件管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/caseManage', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2001, parentMenuId: 2, menuName: '规则管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: 2001001, parentMenuId: 2001, menuName: '规则因子管理', menuType: '0', menuRouteUrl: '/operationCenter/rules/ruleFactor', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-ruleF' },
      // { menuId: 2001002, parentMenuId: 2001, menuName: '规则配置', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules' },
      { menuId: 2001002, parentMenuId: 2001, menuName: '规则配置', menuType: '0', menuRouteUrl: '/operationCenter/rules/rules/auth', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules-limit' },
      // { menuId: 2001002001001, parentMenuId: 2001002001, menuName: '查看', menuType: '1', menuRouteUrl: ' ', iconId: '', level: 5, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'param:rules:rules:limit:check' },
      // { menuId: 2001002002, parentMenuId: 2001002, menuName: '授权', menuType: '0', menuRouteUrl: '/operationCenter/rules/rules/auth', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules-rules-auth' },

      { menuId: 2002, parentMenuId: 2, menuName: '流程管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
      { menuId: 2002002, parentMenuId: 2002, menuName: '节点管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/nodeConfig', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2002003, parentMenuId: 2002, menuName: '标签管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/labelManage', iconId: 'TeamOutlined', level: 4, orderSeq: '3', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      // { menuId: 2002004, parentMenuId: 2002, menuName: '处理程序管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/nodeFlowManage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      // { menuId: 2002004, parentMenuId: 2002, menuName: '节点流程', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/dispatchManage', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2002005, parentMenuId: 2002, menuName: '程序管理', menuType: '0', menuRouteUrl: '/operationCenter/flowManage/attribute', iconId: 'TeamOutlined', level: 4, orderSeq: '4', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },

      { menuId: 2003, parentMenuId: 2, menuName: '综合管理', menuType: '0', iconId: 'SettingOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param' },
      { menuId: 2003002, parentMenuId: 2003, menuName: '岗位管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/stationManagement', iconId: 'TeamOutlined', level: 4, orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
      { menuId: 2003003, parentMenuId: 2003, menuName: '人员管理', menuType: '0', menuRouteUrl: '/operationCenter/integrateManage/staffManagement', iconId: 'TeamOutlined', level: 4, orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-param-callParam-authR' },
    
      { menuId: 3, parentMenuId: 0, menuName: '数据管理', menuType: '0', iconId: 'TeamOutlined', orderSeq: '2', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter' },
      { menuId: 3001, parentMenuId: 3, menuName: '案件数据', menuType: '0', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: ********, parentMenuId: 3001, menuName: '基本信息数据', menuType: '0', menuRouteUrl: '/sourceManage/case/caseInfo', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
      { menuId: ********, parentMenuId: 3001, menuName: '账户信息数据', menuType: '0', menuRouteUrl: '/sourceManage/case/base', iconId: 'SettingOutlined', orderSeq: '1', menuStatus: 'Y', createTime: '2024-6-20 12:12:12', updateTime: '2024-6-21 13:13:13', createUser: 'testUser1', updateUser: 'testUser2', permissionId: 'operationCenter-rules' },
     ]
  }
};
