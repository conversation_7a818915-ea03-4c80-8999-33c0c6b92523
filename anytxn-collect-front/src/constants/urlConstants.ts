/**
 * 接口常量
 */
import { TABLE_MAP } from "./tableConstants";

const URL_CONSTANST = {
  // 机构号
  BASE_ORG_PARAM: {
    LIST: {
      URL: "param/baseOrg/query",
      TABLE_NAME: TABLE_MAP.BASE_ORG_PARAM,
    },
  },
  // 规则因子管理
  RULE_FACTOR: {
    LIST: { URL: "rule/variable/queryRuleVariable" },
    EDIT: { URL: "rule/variable/update" },
    CREATE: { URL: "rule/variable/save" },
  },
  // 规则-额度
  RULES_LIMIT: {
    LIST: {
      URL: "param/rules/limit/query",
      TABLE_NAME: TABLE_MAP.BASE_RULE_INFO_PARAM,
    },
    EDIT: {
      URL: "param/rules/limit/update",
      TABLE_NAME: TABLE_MAP.BASE_RULE_INFO_PARAM,
    },
    CREATE: {
      URL: "param/rules/limit/create",
      TABLE_NAME: TABLE_MAP.BASE_RULE_INFO_PARAM,
    },
  },
  // 规则-授权
  RULES_AUTH: {
    LIST: {
      URL: "param/rules/pageQuery",
      TABLE_NAME: TABLE_MAP.BASE_RULE_INFO_PARAM,
    },
    EDIT: {
      URL: "param/rules/update",
      TABLE_NAME: TABLE_MAP.BASE_RULE_INFO_PARAM,
    },
    CREATE: {
      URL: "param/rules/create",
      TABLE_NAME: TABLE_MAP.BASE_RULE_INFO_PARAM,
    },
  },
  // 规则因子
  RULES_FACTOR: {
    LIST_BY_RULE_TYPE: {
      URL: "rule/variable/queryByRuleType",
    },
  },
  // 客群管理
  CUTTOMER_MANAGE: {
    LIST: {
      URL: "param/dimensionalManage/query",
      TABLE_NAME: TABLE_MAP.CUTTOMER_MANAGE,
    },
    EDIT: {
      URL: "param/dimensionalManage/update",
      TABLE_NAME: TABLE_MAP.CUTTOMER_MANAGE,
    },
    CREATE: {
      URL: "param/dimensionalManage/create",
      TABLE_NAME: TABLE_MAP.CUTTOMER_MANAGE,
    },
  },
  // 标签管理
  LABEL_MANAGE: {
    LIST: { URL: "api/tags/query", TABLE_NAME: TABLE_MAP.LABEL_MANAGE },
    EDIT: { URL: "api/tags/update", TABLE_NAME: TABLE_MAP.LABEL_MANAGE },
    CREATE: { URL: "api/tags/add", TABLE_NAME: TABLE_MAP.LABEL_MANAGE },
    delete: { URL: "api/tags/delete", TABLE_NAME: TABLE_MAP.LABEL_MANAGE },
  },
  // 节点管理
  NODE_MANAGE: {
    LIST: {
      URL: "param/nodeManage/pageQuery",
      TABLE_NAME: TABLE_MAP.NODE_MANAGE,
    },
    EDIT: { URL: "param/nodeManage/update", TABLE_NAME: TABLE_MAP.NODE_MANAGE },
    CREATE: {
      URL: "param/nodeManage/create",
      TABLE_NAME: TABLE_MAP.NODE_MANAGE,
    },
  },
  // 节点程序
  NODE_FOW_MANAGE: {
    LIST: {
      URL: "param/nodeFlowManage/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    EDIT: {
      URL: "param/nodeFlowManage/update",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    CREATE: {
      URL: "param/nodeFlowManage/create",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
  // 处理程序管理
  ATTRIBUTE_MANAGE: {
    LIST: {
      URL: "api/processingProgram/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    EDIT: {
      URL: "api/processingProgram/update",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    CREATE: {
      URL: "api/processingProgram/add",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    DELETE: {
      URL: "api/processingProgram/delete",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
  // 案件基础信息
  CASE_BASE_INFO: {
    LIST: { URL: "api/wcsCasState/query", TABLE_NAME: TABLE_MAP.LABEL_MANAGE },
    EDIT: { URL: "api/wcsCasState/update", TABLE_NAME: TABLE_MAP.LABEL_MANAGE },
    FLOW_NODE: {
      URL: "api/taskNodeInfo/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    ACTION_DETAIL: {
      URL: "api/taskNodeActionInfo/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
  // 审批管理
  ADUIT_MANAGE: {
    LIST: {
      URL: "param/aduitManage/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    EDIT: {
      URL: "param/aduitManage/update",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    CREATE: {
      URL: "param/aduitManage/create",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
  // 审批管理
  WORKBENCH_CONFIG: {
    LIST: {
      URL: "api/workBenchConfig/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    EDIT: {
      URL: "api/workBenchConfig/edit",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    CREATE: {
      URL: "api/workBenchConfig/add",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
  // 卡户人查询-卡片查询
  CARD_QUERY: {
    LIST: { URL: "service/cardQuery/query" },
    DETAIL: { URL: "service/cardQuery/queryDetailInfo" },
  },
  // 岗位管理
  STATION_MANAGEMENT: {
    LIST: {
      URL: "param/stationManagement/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    EDIT: {
      URL: "param/stationManagement/update",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    CREATE: {
      URL: "param/stationManagement/create",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    DELETE: {
      URL: "param/stationManagement/delete",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
  // 人员管理
  STAFF_MANAGEMENT: {
    LIST: {
      URL: "param/staffManagement/query",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    EDIT: {
      URL: "param/staffManagement/update",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    CREATE: {
      URL: "param/staffManagement/create",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
    DELETE: {
      URL: "param/staffManagement/delete",
      TABLE_NAME: TABLE_MAP.LABEL_MANAGE,
    },
  },
};

export default URL_CONSTANST;
