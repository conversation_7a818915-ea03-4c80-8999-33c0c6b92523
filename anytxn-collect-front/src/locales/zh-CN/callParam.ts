// 标签管理页面
// 维表管理页面
// 案件基本信息页面
// 审批管理页面
// 节点流程页面
// 电话催收工作台
// 短信催收工作台
// 节点管理
// 处理程序管理
// 程序管理
// 综合管理-工作台配置

export default {
  // 标签管理页面
  "callParam.labelManage": "标签管理",
  "callParam.tagName": "标签名称",
  "callParam.paramIndex": "序号",
  "callParam.tagType": "标签类型",
  "callParam.tagSource": "标签来源",
  "callParam.tagId": "标签ID",
  "callParam.tagDescription": "标签描述",
  "callParam.tagAttribute": "标签属性",
  "callParam.labelType1": "还款意愿",
  "callParam.labelType2": "还款能力",
  "callParam.labelType3": "逾期阶段",
  "callParam.labelType4": "风险等级",
  "callParam.labelType5": "流转判断",
  "callParam.labelType6": "特殊标签",
  "callParam.labelNameSrc1": "手动输入",
  "callParam.labelNameSrc2": "批量导入",
  "callParam.labelNameSrc3": "实时接口",
  "callParam.attribute1": "常规标签",
  "callParam.attribute2": "特殊标签",
  "callParam.attribute3": "其他",
  "callParam.labelSource1": "手工导入",
  "callParam.labelSource2": "批量接口",
  "callParam.labelSource3": "实时接口",
  "callParam.labelDesc": "标签描述",
  "callParam.updateUser": "更新人",
  "callParam.updater": "更新人",
  "callParam.updateTime": "更新时间",
  "callParam.createTime": "创建时间",
  "callParam.aduitUser": "审批人",
  "callParam.aduitTime": "审批时间",
  "callParam.status": "状态",

  // 维表管理页面
  "callParam.dimensionalManage": "维表管理",
  "callParam.custName": "客户名称",
  "callParam.custPerson": "客群总数",
  "callParam.custDesc": "客群描述",
  "callParam.baseInfo": "基础信息",
  "callParam.invalid": "失效",
  "callParam.effective": "有效",

  // 案件基本信息页面
  "callParam.caseManage": "案件信息",
  "callParam.aiRulesCheck": "AI辅助",
  "callParam.caseInfo": "案件概览",
  "callParam.caseCode": "案件号",
  "callParam.chargoffFlag": "核销标识",
  "callParam.countOdue": "入催次数",
  "callParam.icType": "证件类型",
  "callParam.custIc": "证件号",
  "callParam.dteIntoCollection": "入催时间",
  "callParam.dteOutCollection": "出催时间",
  "callParam.firstDate": "首次入催时间",
  "callParam.lastCaseCode": "上案件号",
  "callParam.lastCaseDate": "上次案件时间",
  "callParam.lawDate": "诉讼日期",
  "callParam.lawFlag": "诉讼标识",
  "callParam.model": "模块",
  "callParam.orgCustNbr": "客户号",
  "callParam.currState": "当前状态",
  "callParam.caseState": "案件状态",
  "callParam.teamCode": "风险池编号",
  "callParam.lastOpeTime": "最近行动时间",
  "callParam.inTeamDate": "入组日期",
  "callParam.reasonCode": "风险池原因码",
  "callParam.actSerialId": "行动路径编号",
  "callParam.noneContactRuleCode": "失联决策代码",
  "callParam.contactCode": "联系情况",
  "callParam.contactTimes": "人工联系次数",
  "callParam.resultCode": "结果代码",
  "callParam.resultTime": "结果完成时间",
  "callParam.resultDirection": "呼入/呼出",
  "callParam.attemptCount": "尝试联系这个帐户的次数",
  "callParam.clientCode": "用户定义的结果代码",
  "callParam.voiceCallType": "信息传达标记",
  "callParam.specialCaseNote": "特别案件备注",
  "callParam.callTotal": "有效催收总数",
  "callParam.lastModel": "上一模板",
  "callParam.ename": "英文名",
  "callParam.eusex": "性别",
  "callParam.statementTypeAll": "账单类型",
  "callParam.billingCycle": "账单日",
  "callParam.badnessCode": "不良标识域",
  "callParam.classIBalance": "总欠款",
  "callParam.riskRank": "风险等级",
  "callParam.delayDays": "延滞天数",
  "callParam.activeInstallmentFlag": "活跃分期标识",
  "callParam.productType": "大额账户标识",
  "callParam.status24": "24期状况",
  "callParam.wcsOutcode": "委外标志",
  "callParam.handType": "手别",
  "callParam.model1": "电催",
  "callParam.model2": "委外",
  "callParam.model3": "诉讼",
  "callParam.model4": "短信",
  "callParam.model5": "核销",
  "callParam.model6": "专案",
  "callParam.dayNum": "延滞天数",
  "callParam.caseAmt": "逾期金额",
  "callParam.pending": "待办",
  "callParam.completed": "结案",

  // 审批管理页面
  "callParam.aduitManage": "审批管理",
  "callParam.aduitPage": "审批项",
  "callParam.aduitStatus": "审批状态",
  "callParam.applyUser": "申请人",
  "callParam.applyTime": "申请时间",

  // 节点流程页面
  "callParam.dispatchManage": "节点流程",
  "callParam.flowchartId": "节点ID",
  "callParam.flowchartName": "节点名称",
  "callParam.description": "描述",
  "callParam.flowchartText": "节点流程图",
  "callParam.nodeExecStatus": "节点执行状态",
  "callParam.nodeExecCount": "节点执行次数",
  "callParam.lastExecDuration": "最后一次执行时长",
  "callParam.lastExecTime": "最后一次执行时间",
  "callParam.nextNode": "下一节点",
  "callParam.nodeProgram": "节点程序",
  "callParam.nodeProStrategy": "流转策略",
  "callParam.nodeRule": "规则",
  "callParam.nodeRuleFactor": "规则因子",
  "callParam.nodeRuleFactorResult": "规则结果",
  "callParam.operator": "运算符",
  "callParam.nodeRuleValue": "值",

  // 电话催收工作台
  "callParam.phone": "电话催收工作台",

  // 短信催收工作台
  "callParam.message": "短信催收工作台",

  // 节点管理
  "callParam.nodeConfig": "节点管理",
  "callParam.nodeCode": "节点ID",
  "callParam.nodeCallName": "节点名称",
  "callParam.nodeFuncDesc": "节点说明",
  "callParam.nodeType": "节点类型",
  "callParam.nodeStatus": "状态",
  "callParam.nodeAuth": "节点权限",
  "callParam.nodePriority": "优先级",
  "callParam.nodeAttriId": "节点处理程序",
  "callParam.nodeAuth1": "业务员",
  "callParam.nodeAuth2": "组长",
  "callParam.nodeAuth3": "审批人",
  "callParam.nodeAuth4": "管理员",
  "callParam.nodeType0": "开始节点",
  "callParam.nodeType1": "技术节点",
  "callParam.nodeType2": "业务节点",
  "callParam.nodeStatus1": "编辑中",
  "callParam.nodeStatus2": "生效中",
  "callParam.nodeStatus3": "审批中",
  "callParam.nodeStatus4": "停用/失效",

  // 处理程序管理
  "callParam.nodeFlowManage": "节点处理程序",
  "callParam.nodeFlowId": "程序ID",
  "callParam.nodeFlowName": "程序名称",
  "callParam.nodeFlowCode": "支持节点代码",
  "callParam.nodeFlowDesc": "简介",
  "callParam.nodeFlowUp": "实例上限",

  // 程序管理
  "callParam.nodeAttribute": "程序管理",
  "callParam.nodeAttriName": "程序名称",
  "callParam.nodeAttriType": "程序类型",
  "callParam.nodeRuleype": "关联规则",
  "callParam.nodeAttriDesc": "简介",
  "callParam.nodeAttriCode": "支持节点代码",
  "callParam.nodeAttriUp": "实例上限",
  "callParam.nodeAttriType1": "数据同步",
  "callParam.nodeAttriType2": "标签处理",
  "callParam.nodeAttriType3": "第三方接口",
  "callParam.nodeAttriType4": "本系统接口",
  "callParam.department": "部门",
  "callParam.groups": "组别",

  // 综合管理-工作台配置
  "callParam.integrateManageWorkBench": "工作台配置",
  "callParam.workBenchName": "配置页面",
  "callParam.workBenchModel": "展示模块",
  "callParam.callFields": "催收信息字段",
  "callParam.customFields": "客户信息字段",
  "callParam.contact": "联系信息字段",
  "callParam.auxiliaryFunctions": "辅助功能",
  "callParam.authPage1": "电话催收工作台",
  "callParam.authPage2": "短信催收工作台",
  "callParam.authPage3": "核销催收工作台",
  "callParam.authPage4": "委外催收工作台",
  "callParam.authPage5": "法诉催收工作台",
  "callParam.authPage6": "专案工作台",
  "callParam.rightAction1": "催收信息",
  "callParam.rightAction2": "客户信息",
  "callParam.rightAction3": "联系信息",
  "callParam.rightAction4": "账户信息",
  "callParam.rightAction5": "卡片信息",
  "callParam.leftAction1": "转案",
  "callParam.leftAction2": "提前支付",
  "callParam.leftAction3": "止付",
  "callParam.leftAction4": "减免",
  "callParam.leftAction5": "保证金抵欠",
  "callParam.leftAction6": "同名账户调账",
  "callParam.leftAction7": "上除强催标",
  "callParam.leftAction8": "协商分期",
  "callParam.leftAction9": "轻财订单",
  "callParam.leftAction10": "信息修复",
  "callParam.leftAction11": "短信",
  "callParam.leftAction12": "微信",
  "callParam.leftAction13": "APP",
  "callParam.leftAction14": "电邮",
  "callParam.leftAction15": "信函",

  // 智能规则检查
  "callParam.aiRulesTitle": "智能规则检查",
  "callParam.ruleTypeLabel": "请输入要检查的规则类型：",
  "callParam.ruleTypePlaceholder": "例如：节点流程规则",
  "callParam.promptLabel": "请输入提示词：",
  "callParam.promptPlaceholder": "请输入需要检查的内容",
  "callParam.startCheck": "开始检查",
  "callParam.startCheckMessage": "开始检查",
  "callParam.pleaseComplete": "请填写完整信息",

  // 案件管理
  "callParam.noNodeInfo": "无节点信息",
  "callParam.transferDispatchSuccess": "转派成功",
  "callParam.dispatchStrategy": "派案策略",
  "callParam.targetQueue": "目标队列",
  "callParam.transfer": "转案",
  "callParam.dispatch": "派案",
  "callParam.collectionRecord": "催收记录",
  "callParam.reviewDate": "复核时间",
  "callParam.commitmentAmount": "承诺金额",
  "callParam.commitmentDate": "承诺时间",
  "callParam.dailFlag": "是否继续拨号",
  "callParam.callDuration": "通话时长",
  "callParam.startTime": "通话开始时间",
  "callParam.endTime": "通话结束时间",
  "callParam.audioCode": "录音编号",
  "callParam.actionDate": "行动日期",
  "callParam.obType": "外呼类型",
  "callParam.telType": "电话类型",
  "callParam.callType": "通话方向",
  "callParam.outbound": "外呼",
  "callParam.mobile": "手机",
  "callParam.callOut": "呼出",
  "callParam.id": "编号",
  "callParam.customerNo": "客户号",
  "callParam.templateCode": "模板编号",
  "callParam.templateName": "模板名称",
  "callParam.mobileNumber": "手机号码",
  "callParam.sendContent": "发送内容",
  "callParam.receiverName": "接收人姓名",
  "callParam.senderCollector": "发送催收员",
  "callParam.sendTime": "发送时间",
  "callParam.taskDate": "任务日期",
  "callParam.creator": "创建人",
  "callParam.cardNumber": "卡号",
  "callParam.transactionCode": "交易代码",
  "callParam.transactionCurrency": "交易货币",
  "callParam.transactionAmount": "交易金额",
  "callParam.postingCurrency": "入账货币",
  "callParam.postingAmount": "入账金额",
  "callParam.transactionDate": "交易日期",
  "callParam.postingDate": "入账日期",
  "callParam.smsSuccess": "短信发送成功",
  "callParam.connected": "已接通",
  "callParam.rejected": "拒接",
  "callParam.selectPlaceholder": "请选择",
  "callParam.amountBalance": "金额均衡",
  "callParam.caseBalance": "数量均衡",
  "callParam.amountAndCaseBalance": "金额 + 数量均衡",
  "callParam.teamA": "电催A组",
  "callParam.teamB": "电催B组",
  "callParam.teamC": "电催C组",
  "callParam.outsourcingQueue": "委外队列",
  "callParam.litigationQueue": "诉讼队列",

  // 催收工作台
  "callParam.collectionInfoOverview": "催收信息概览",

  // 岗位管理
  "callParam.stationManagement": "岗位管理",
  "callParam.staffManagement": "人员管理",
  "callParam.stationId": "ID",
  "callParam.position": "岗位",
  "callParam.staff": "人员",
  "callParam.preview": "结果",
  "callParam.weightCoefficient": "分案权重",
  "callParam.staffName": "姓名",
  "callParam.station1": "客户经理",
  "callParam.station2": "授信QA",
  "callParam.station3": "催收QA",
  "callParam.customerManager": "客户经理",
  "callParam.collectionQA": "催收QA",
  "callParam.collectionSpecialist": "催收专员",
  "callParam.seniorCollector": "高级催收员",
  "callParam.expertCollector": "资深催收员",
  "callParam.collectionSupervisor": "催收主管",
  "callParam.seniorSupervisor": "高级主管"
};
