// 规则引擎（简体中文）
export default {
  "rules.numbericalValue": "数值",
  "rules.function": "函数",
  "rules.node": "节点流转规则",
  "rules.OperationRules": "节点流转规则",
  "rules.limit": "额度",
  "rules.Limit": "额度",
  "rules.auth": "授权",
  "rules.incId": "序号",
  "rules.ruleId": "规则ID",
  "rules.ruleName": "规则名称",
  "rules.ruleType": "规则类型",
  "rules.ruleSts": "规则状态",
  "rules.ruleDesc": "描述",
  "rules.ruleExecuteType": "规则配置类型",
  "rules.baseInfo": "基本信息",
  "rules.JUDGE": "判断表达式",
  "rules.OPERATOR": "运算表达式",
  "rules.RESULT": "结果信息",
  "rules.ruleResult": "结果",
  "rules.ruleFacField": "规则因子",
  "rules.rulePri": "优先级",
  "rules.name": "因子名称",
  "rules.key": "因子编码",
  "rules.valueType": "因子值类型",
  "rules.remark": "申请说明",
  "rules.createNumber": "添加数值",
  "rules.createFunction": "添加函数",
  "rules.createRuleFac": "添加规则因子",
  "rules.cardPerLayer": "持卡人层",
  "rules.accountLayer": "帐户层",
  "rules.label": "标签匹配规则",
  "rules.LabelHanldle": "标签处理规则",
  "rules.limitControl": "额度管控单元匹配规则",
  "rules.dictInfo": "选择字典",
  "rules.dictName": "字典",
  "rules.dictName1": "字典展示方式",
  "rules.dictName2": "因子值长度",
  "rules.minLength": "因子值最小长度",
  "rules.maxLength": "因子值最大长度",
  "rules.digits": "精度(小数位)",
  "rules.dictName5": "参数表",
  "rules.dictName6": "绑定参数项",
  "rules.dictName7": "展示参数项",
  "rules.deleteNode": "删除节点",
  "rules.createCondition": "添加条件",
  "rules.createConditionGroup": "添加条件组合",
  "rules.ALL": "全部满足",
  "rules.ONE": "满足其中一条",
  "rules.editingText": "点击编辑条件",
  "rules.EQ": "等于",
  "rules.GT": "大于",
  "rules.LT": "小于",
  "rules.NEQ": "不等于",
  "rules.GE": "大于等于",
  "rules.LE": "小于等于",
  "rules.IN": "属于",
  "rules.HASVAL": "存在值",
  "rules.NOHASVAL": "不存在值",
  "rules.NULL": "空",
  "rules.NONULL": "不为空",
  "rules.BETWEEN": "之间",
  "rules.LIKE": "类似",
  "rules.NOLIKE": "不类似",
  "rules.STARTSWITH": "以字符串开头",
  "rules.NOTSTARTSWITH": "不以字符串开头",
  "rules.ENDSWITH": "以字符串结尾",
  "rules.INLIST": "在列表中",
  "rules.NOTINLIST": "不在列表中",
  "rules.NOTIN": "不在",
  "rules.CUTEQ": "裁取相等",
  "rules.CUTNOTEQ": "裁取不相等",
  "rules.CUTIN": "裁取包含",
  "rules.DIVIDE": "除",
  "rules.NOTDIVIDE": "不除",
  "rules.NOTEMPTY": "是否为空，包括空字符与null",
  "rules.EQIGNORECASE": "字符串不区分大小写比较",
  "rules.limitRule": "额度规则",
  "rules.authRule": "规则",
  "rules.true": "是",
  "rules.false": "否",
  "rules.inputMultiplePlaceHolder": "请输入，多个内容请用小写逗号隔开",
  "rules.allowOnly": "限制输入",
  "rules.UPPER_CASE_EN": "大写英文",
  "rules.LOWER_CASE_EN": "小写英文",
  "rules.SPECIAL_CHAR": "特殊字符",
  "rules.CHINESE": "中文",
  "rules.NUMBER": "数字",
  "rules.effective": "已生效",
  "rules.ineffective": "未生效",
  "rules.valueNull": "值不能为空",
  "rules.opExpsValueNull": "运算表达式中值或规则因子不能为空",
  "rules.opExpsFuncNotMeet": "运算表达式中函数必须有两个子节点以上",
  "rules.judgeCondGroupNotMeet": "条件表达式中条件组合必须有一个子节点",
  "rules.judgeCondEditing": "条件表达式中条件在编辑状态",
  "rules.DIC": "字典型",
  "rules.INT_X": "整数型",
  "rules.DOUBLE_X": "浮点型",
  "rules.AMOUNT": "金额型",
  "rules.CHARACTER_X": "字符型",
  "rules.PARAMETER": "参数型",
  "rules.BOOLEAN_X": "布尔型",
  "rules.ARRAY": "数组型",
  "rules.LONG_X": "长整型",
};
