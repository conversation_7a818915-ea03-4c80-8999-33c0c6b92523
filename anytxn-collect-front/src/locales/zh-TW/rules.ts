// 規則引擎（繁體中文）
export default {
  'rules.numbericalValue': '數值',
  'rules.function': '函數',
  'rules.node': '節點流轉規則',
  'rules.OperationRules': '節點流轉規則',
  'rules.limit': '額度',
  'rules.Limit': '額度',
  'rules.auth': '授權',
  'rules.incId': '序號',
  'rules.ruleId': '規則ID',
  'rules.ruleName': '規則名稱',
  'rules.ruleType': '規則類型',
  'rules.ruleSts': '規則狀態',
  'rules.ruleDesc': '描述',
  'rules.ruleExecuteType': '規則配置類型',
  'rules.baseInfo': '基本信息',
  'rules.JUDGE': '判斷表達式',
  'rules.OPERATOR': '運算表達式',
  'rules.RESULT': '結果信息',
  'rules.ruleResult': '結果',
  'rules.ruleFacField': '規則因子',
  'rules.rulePri': '優先級',
  'rules.name': '因子名稱',
  'rules.key': '因子編碼',
  'rules.valueType': '因子值類型',
  'rules.remark': '申請說明',
  'rules.createNumber': '添加數值',
  'rules.createFunction': '添加函數',
  'rules.createRuleFac': '添加規則因子',
  'rules.cardPerLayer': '持卡人層',
  'rules.accountLayer': '帳戶層',
  'rules.label': '標籤匹配規則',
  'rules.LabelHanldle': '標籤處理規則',
  'rules.limitControl': '額度管控單元匹配規則',
  'rules.dictInfo': '選擇字典',
  'rules.dictName': '字典',
  'rules.dictName1': '字典展示方式',
  'rules.dictName2': '因子值長度',
  'rules.minLength': '因子值最小長度',
  'rules.maxLength': '因子值最大長度',
  'rules.digits': '精度(小數位)',
  'rules.dictName5': '參數表',
  'rules.dictName6': '綁定參數項',
  'rules.dictName7': '展示參數項',
  'rules.deleteNode': '刪除節點',
  'rules.createCondition': '添加條件',
  'rules.createConditionGroup': '添加條件組合',
  'rules.ALL': '全部滿足',
  'rules.ONE': '滿足其中一條',
  'rules.editingText': '點擊編輯條件',
  'rules.EQ': '等於',
  'rules.GT': '大於',
  'rules.LT': '小於',
  'rules.NEQ': '不等於',
  'rules.GE': '大於等於',
  'rules.LE': '小於等於',
  'rules.IN': '屬於',
  'rules.HASVAL': '存在值',
  'rules.NOHASVAL': '不存在值',
  'rules.NULL': '空',
  'rules.NONULL': '不為空',
  'rules.BETWEEN': '之間',
  'rules.LIKE': '類似',
  'rules.NOLIKE': '不類似',
  'rules.STARTSWITH': '以字符串開頭',
  'rules.NOTSTARTSWITH': '不以字符串開頭',
  'rules.ENDSWITH': '以字符串結尾',
  'rules.INLIST': '在列表中',
  'rules.NOTINLIST': '不在列表中',
  'rules.NOTIN': '不在',
  'rules.CUTEQ': '裁取相等',
  'rules.CUTNOTEQ': '裁取不相等',
  'rules.CUTIN': '裁取包含',
  'rules.DIVIDE': '除',
  'rules.NOTDIVIDE': '不除',
  'rules.NOTEMPTY': '是否為空，包括空字符與null',
  'rules.EQIGNORECASE': '字符串不區分大小寫比較',
  'rules.limitRule': '額度規則',
  'rules.authRule': '規則',
  'rules.true': '是',
  'rules.false': '否',
  'rules.inputMultiplePlaceHolder': '請輸入，多個內容請用小寫逗號隔開',
  'rules.allowOnly': '限制輸入',
  'rules.UPPER_CASE_EN': '大寫英文',
  'rules.LOWER_CASE_EN': '小寫英文',
  'rules.SPECIAL_CHAR': '特殊字符',
  'rules.CHINESE': '中文',
  'rules.NUMBER': '數字',
  'rules.effective': '已生效',
  'rules.ineffective': '未生效',
  'rules.valueNull': '值不能為空',
  'rules.opExpsValueNull': '運算表達式中值或規則因子不能為空',
  'rules.opExpsFuncNotMeet': '運算表達式中函數必須有兩個子節點以上',
  'rules.judgeCondGroupNotMeet': '條件表達式中條件組合必須有一個子節點',
  'rules.judgeCondEditing': '條件表達式中條件在編輯狀態',
  'rules.DIC': '字典型',
  'rules.INT_X': '整數型',
  'rules.DOUBLE_X': '浮點型',
  'rules.AMOUNT': '金額型',
  'rules.CHARACTER_X': '字符型',
  'rules.PARAMETER': '參數型',
  'rules.BOOLEAN_X': '布爾型',
  'rules.ARRAY': '數組型',
  'rules.LONG_X': '長整型',
};
