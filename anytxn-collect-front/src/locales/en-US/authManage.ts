export default {
  'authManage.baseInfo': 'Basic Information',
  // User Management
  'authManage.userManage': 'User Management',
  'authManage.userId': 'User ID',
  'authManage.userName': 'Username',
  'authManage.orgName': 'Organization',
  'authManage.role': 'Role',
  'authManage.position': 'Position',
  'authManage.gender': 'Gender',
  'authManage.language': 'Language',
  'authManage.level': 'Level',
  'authManage.nextLevelDataAuth': 'Subordinate Data Permission',
  'authManage.originalPassword': 'Original Password',
  'authManage.dept': 'Department',
  // Position Management
  'authManage.positionManage': 'Position Management',
  'authManage.postId': 'Position ID',
  'authManage.postName': 'Position Name',
  'authManage.superiorPost': 'Superior Position',
  'authManage.postDesc': 'Position Description',
  // Role Management
  'authManage.roleManage': 'Role Management', 
  'authManage.roleId': 'Role ID', 
  'authManage.roleStatus': 'Role Status', 
  'authManage.mountStatus': 'Mount Status', 
  'authManage.action': 'Action', 
  'authManage.roleName': 'Role Name', 
  'authManage.enable': 'Enable', 
  'authManage.unEnable': 'Disable',
  'authManage.mounted': 'Mounted', 
  'authManage.unmounted': 'Unmounted', 
  'authManage.roleNamePlaceholder': 'Please enter role name',
  'authManage.roleStatusPlaceholder': 'Please select role status', 
  'authManage.mountStatusPlaceholder': 'Please select mount status', 
  'authManage.viewMount': 'View Mount Details',
  'authManage.editMount': 'Edit Mount', 
  'authManage.tip': 'Tip', 
  'authManage.editMountSuccess': 'Role permission mount edited successfully', 
  'authManage.editSuccess': 'Edit successful!',
  'authManage.addSuccess': 'Add successful!', 
  'authManage.view': 'Search', 
  'authManage.reset': 'Reset',
  'authManage.add': 'Add',
  'authManage.roleDesc': 'Role Description', 
  'authManage.roleIdPlaceholder': 'Please enter role ID', 
  'authManage.roleDescPlaceholder': 'Please enter role description', 
  'authManage.limit16': 'Cannot exceed 16 characters', 
  'authManage.limit2': 'Cannot exceed 2 characters', 
  'authManage.limit200': 'Cannot exceed 200 characters', 
  'authManage.copyObject': 'Copy Object', 
  'authManage.menuPermissions': 'Menu Permissions', 
  'authManage.viewMountDetail': 'View Role Menu Mount', 
  'authManage.editMountDetail': 'Edit Role Menu Mount', 
  'authManage.mountDetail': 'Role Mount', 
  'authManage.warningMsg': 'Disabled roles cannot be edited',
  // org
  'authManage.orgTree': 'Organization Tree',
  'authManage.orgId': 'Organization ID',
  'authManage.parentOrg': 'Parent Organization',
  'authManage.orgType': 'Type',
  'authManage.orgCount': 'Department Count',
  'authManage.edit': 'Edit',
  'authManage.delete': 'Delete',
  'authManage.orgTreeRoot': 'GF Bank',
  'authManage.orgTreeCreditCard': 'Credit Card Center',
  'authManage.orgTreeOps': 'Operation Management',
  'authManage.orgTreeDecision': 'Decision Dept',
  'authManage.orgTreeOperation': 'Operation Dept',
  'authManage.orgTreeSystem': 'System Dept',
  'authManage.orgTreeHead': 'Head Office',
  'authManage.orgTreeItem13': 'Item 1.3',
};