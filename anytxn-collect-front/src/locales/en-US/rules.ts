// 规则引擎（英文）
export default {
  'rules.numbericalValue': 'Numerical Value', // 数值
  'rules.function': 'Function', // 函数
  'rules.OperationRules': 'Node', // 节点
  'rules.limit': 'Limit', // 额度
  'rules.Limit': 'Limit',
  'rules.auth': 'Authorization', // 授权
  'rules.incId': 'No.', // 序号
  'rules.ruleId': 'Rule ID', // 规则ID
  'rules.ruleName': 'Rule Name', // 规则名称
  'rules.ruleType': 'Rule Type', // 规则类型
  'rules.ruleSts': 'Rule Status', // 规则状态
  'rules.updateTime': 'Update Time', // 更新时间
  'rules.ruleDesc': 'Description', // 描述
  'rules.ruleExecuteType': 'Rule Configuration Type', // 规则配置类型
  'rules.baseInfo': 'Basic information', // 基本信息
  'rules.JUDGE': 'Judge Expression', // 判断表达式
  'rules.OPERATOR': 'Operation Expression', // 运算表达式
  'rules.RESULT': 'Result Message', // 结果信息
  'rules.ruleResult': 'Result', // 结果
  'rules.ruleFacField': 'Rule Factor', // 规则因子
  'rules.rulePri': 'Rule Sequence', // 优先级
  'rules.name': 'Factor Name', // 因子名称
  'rules.key': 'Factor Code', // 因子编码
  'rules.valueType': 'Factor Value Type', // 因子值类型
  'rules.remark': 'Application Description', // 申请说明
  'rules.createNumber': 'Add Number', // 添加数值
  'rules.createFunction': 'Add Function', // 添加函数
  'rules.createRuleFac': 'Add Rule Factor', // 添加规则因子
  'rules.cardPerLayer': 'Cardholder Layer',
  'rules.accountLayer': 'Account Layer',
  'rules.label': "Label Matching Rules",
  'rules.LabelHanldle': "Label Processing Rules",
  'rules.checkControl': 'Check Group Rules', // 检查组匹配规则
  'rules.transactionIde': 'Transaction Identification Rules', // 交易识别规则
  'rules.limitControl': 'Quota Control Rules', // 额度管控单元匹配规则
  'rules.dictInfo': 'Select dictionary',
  'rules.dictName': 'literary',
  'rules.dictName1': 'dictionary display method',
  'rules.dictName2': 'factor value length',
  'rules.minLength': 'Min Length',
  'rules.maxLength': 'Max Length',
  'rules.digits': 'precision',
  'rules.dictName5': 'parameter table',
  'rules.dictName6': 'binding parameter item',
  'rules.dictName7': 'Show parameter items',
  'rules.deleteNode': 'Delete Node', // 删除节点
  'rules.createCondition': 'Add Condition', // 添加条件
  'rules.createConditionGroup': 'Add Condition Group', // 添加条件组合
  'rules.ALL': 'Meet All', // 全部满足
  'rules.ONE': 'Meet Any', // 满足其中一条
  'rules.editingText': 'Click to edit condition', // 点击编辑条件
  'rules.EQ': 'Equal', // 等于
  'rules.GT': 'Greater Than', // 大于
  'rules.LT': 'Less Than', // 小于
  'rules.NEQ': 'Not Equal', // 不等于
  'rules.GE': 'Greater Than or Equal', // 大于等于
  'rules.LE': 'Less Than or Equal', // 小于等于
  'rules.IN': 'Belongs To', // 属于
  'rules.HASVAL': 'Has Value', // 存在值
  'rules.NOHASVAL': 'No Value', // 不存在值
  'rules.NULL': 'Null', // 空
  'rules.NONULL': 'Not Null', // 不为空
  'rules.BETWEEN': 'Between', // 之间
  'rules.LIKE': 'Like', // 类似
  'rules.NOLIKE': 'Not Like', // 不类似
  'rules.STARTSWITH': 'Starts With', // 以字符串开头
  'rules.NOTSTARTSWITH': 'Not Starts With', // 不以字符串开头
  'rules.ENDSWITH': 'Ends With', // 以字符串结尾
  'rules.INLIST': 'In List', // 在列表中
  'rules.NOTINLIST': 'Not In List', // 不在列表中
  'rules.NOTIN': 'Not In', // 不在
  'rules.CUTEQ': 'Cut Equal', // 裁取相等
  'rules.CUTNOTEQ': 'Cut Not Equal', // 裁取不相等
  'rules.CUTIN': 'Cut Contains', // 裁取包含
  'rules.DIVIDE': 'Divide', // 除
  'rules.NOTDIVIDE': 'Not Divide', // 不除
  'rules.NOTEMPTY': 'Is Not Empty (including empty string and null)', // 是否为空，包括空字符与null
  'rules.EQIGNORECASE': 'Equal Ignore Case', // 字符串不区分大小写比较
  'rules.limitRule': 'Credit Rule', // 额度规则
  'rules.authRule': 'Authorization Rule', // 授权规则
  'rules.true': 'TRUE', // 是
  'rules.false': 'FALSE', // 否
  'rules.inputMultiplePlaceHolder': 'Please input, separate multiple items with lowercase commas', // 请输入，多个内容请用小写逗号隔开
  'rules.allowOnly': 'Allow only', // 限制输入
  'rules.UPPER_CASE_EN': 'Uppercase Letters', // 大写英文
  'rules.LOWER_CASE_EN': 'Lowercase Letters', // 小写英文
  'rules.SPECIAL_CHAR': 'Special Characters', // 特殊字符
  'rules.CHINESE': 'Chinese Characters', // 中文
  'rules.NUMBER': 'Numbers', // 数字
  'rules.effective': 'Effective', // 已生效
  'rules.ineffective': 'Ineffective', // 未生效
  'rules.valueNull': 'Cannot be null',
  'rules.opExpsValueNull': 'Operation expression value or rule factor cannot be empty', // 运算表达式值或规则因子不能为空
  'rules.opExpsFuncNotMeet': 'Operation expression function must have more than two child nodes', // 运算表达式函数必须有两个子节点以上
  'rules.judgeCondGroupNotMeet': 'Condition group must have at least one child node', // 条件表达式中条件组合必须有一个子节点
  'rules.judgeCondEditing': 'Condition is in editing state', // 条件表达式中条件在编辑状态
  'rules.DIC': 'typical',
  'rules.INT_X': 'INT_X type',
  'rules.DOUBLE_X': 'floating point',
  'rules.AMOUNT': 'AMOUNT type',
  'rules.CHARACTER_X': 'Character type',
  'rules.PARAMETER': 'Parameter type',
  'rules.BOOLEAN_X': 'Boolean',
  'rules.ARRAY': ' Array',
  'rules.LONG_X': 'long shape',
};
