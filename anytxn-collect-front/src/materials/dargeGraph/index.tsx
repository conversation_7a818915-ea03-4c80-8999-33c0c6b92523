// 物料层-流程图
import { lazy, memo, useEffect, useState } from "react";
import { layoutConfig, nodeConfig, edgeConfig } from "./config";
import G6, { edgeDefaultConfig, nodeDefaultConfig } from "@/components/graph";
const NodeDialog = lazy(() => import("./dialog/nodeDialog"));

const DargeGraph = ({ data, behaviors = [], canNodeClick = true }) => {
  // 内部变量
  const [options, setOptions] = useState<any>(null);
  const [dialogVisible, setDialogVisible] = useState<boolean>(false);
  const [nodeSource, setNodeSource] = useState<any>();

  // 副作用
  useEffect(() => {
    console.log("这里拿不到，或者不是最新的 graph");
    setOptions({
      data,
      node: { ...nodeDefaultConfig, ...nodeConfig },
      edge: { ...edgeDefaultConfig, ...edgeConfig },
      layout: { ...layoutConfig },
      // autoFit: 'center',
      behaviors: behaviors
        ? behaviors
        : ["zoom-canvas", "drag-element", "drag-canvas"],
      plugins: [
        {
          type: "minimap",
          size: [240, 160],
        },
      ],
    });
  }, []);

  // 事件处理
  const handleNodeClick = (node) => {
    const nodeData = data?.nodes.find((item) => item?.id === node.target.id);
    setNodeSource(nodeData);
    setDialogVisible(true);
  };

  // 绑定内置交互事件
  const bindEvents = canNodeClick
    ? {
        onNodeClick: handleNodeClick,
      }
    : {};

  return (
    <>
      {data && <G6 options={options} bindEvents={bindEvents} />}
      <NodeDialog
        data={nodeSource}
        dialogVisible={dialogVisible}
        onClose={() => setDialogVisible(false)}
      />
    </>
  );
};

export default memo(DargeGraph);
