/**
 * 节点弹窗
 */
import { Drawer, Table, Tabs } from "antd";
import { useEffect, useState } from "react";
import { FormTemplate } from "@/components";
import commonService from "@/services/common";
import { actionRecordColumns, nodePropsConfig } from "./nodeDialogConfig";
import { I18N_COMON_PAGENAME, TIME_FORMATE } from "@/constants/publicConstant";
import urlConstants from "@/constants/urlConstants";
import dayjs from "dayjs";
import useIntlCustom from "@/hooks/useIntlCustom";

const NodeDialog = ({ dialogVisible, data, onClose }) => {
  const { translate } = useIntlCustom();
  const [activeKey, setActiveKey] = useState<string>("nodeProps");
  const [dataSource, setDatasource] = useState<any>([]);

  const tabsItems = [
    {
      key: "nodeProps",
      label: translate('callParam', 'nodeProps'),
      children: (
        <FormTemplate
          config={nodePropsConfig}
          initialData={{
            ...data?.data,
            lastExecTime: dayjs(data?.data?.dteIntoCollection).format(
              TIME_FORMATE
            ),
          }}
          canEdit={false}
          showMaintenance={false}
          intlPrefix={I18N_COMON_PAGENAME.CALL_PARAM}
        />
      ),
    },
    {
      key: "actionRecord",
      label: translate('callParam', 'actionRecord'),
      children: (
        <Table
          rowKey="id"
          scroll={{ x: "max-content" }}
          columns={actionRecordColumns}
          dataSource={dataSource}
        />
      ),
    },
  ];

  useEffect(() => {
    if (activeKey === "actionRecord") {
      getTableData();
    }
    return () => setActiveKey("nodeProps");
  }, [activeKey]);

  // 获取列表数据
  const getTableData = async () => {
    const params = {
      url: urlConstants.CASE_BASE_INFO.ACTION_DETAIL,
      caseCode: data?.data?.caseCode,
      nodeCode: data?.data?.nodeCode,
    };
    const res: any = await commonService.getEditPostBiz(params);
    setDatasource(res.data);
  };

  const content = () => (
    <Tabs
      defaultActiveKey="nodeProps"
      items={tabsItems}
      onChange={(val: string) => setActiveKey(val)}
    />
  );

  return (
    <Drawer
      title={translate('callParam', 'nodeView')}
      width="60%"
      destroyOnClose
      onClose={onClose}
      open={dialogVisible}
    >
      {content()}
    </Drawer>
  );
};

export default NodeDialog;
