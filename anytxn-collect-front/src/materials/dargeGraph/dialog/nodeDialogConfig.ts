import { COMPONENT_TYPE, FORMITEM_TYPE } from "@/constants/publicConstant";
import { intlConst } from "@/hooks/useIntlCustom";

export const nodePropsConfig = [
  {
    type: FORMITEM_TYPE.Single,
    data: [
      {
        type: COMPONENT_TYPE.INPUT,
        name: "flowchartName",
        label: "flowchartName",
      },
      {
        type: COMPONENT_TYPE.INPUT,
        name: "nodeExecStatus",
        label: "nodeExecStatus",
      },
      {
        type: COMPONENT_TYPE.INPUT,
        name: "nodeExecCount",
        label: "nodeExecCount",
      },
      {
        type: COMPONENT_TYPE.INPUT,
        name: "lastExecDuration",
        label: "lastExecDuration",
      },
      {
        type: COMPONENT_TYPE.INPUT,
        name: "lastExecTime",
        label: "lastExecTime",
      },
      {
        type: COMPONENT_TYPE.INPUT,
        name: "nextNode",
        label: "nextNode",
      },
    ],
  },
];

export const nodeProgramConfig = [
  {
    type: FORMITEM_TYPE.Single,
    data: [
      {
        type: COMPONENT_TYPE.TEXTAREA,
        name: "nodeProgram",
        label: "nodeProgram",
        height: 200,
      },
    ],
  },
];

export const actionRecordColumns = [
  {
    key: "caseCode",
    title: intlConst.formatMessage("callParam", "caseCode"),
    dataIndex: "caseCode",
  },
  {
    key: "taskId",
    title: intlConst.formatMessage("callParam", "taskId"),
    dataIndex: "taskId",
  },
  {
    key: "nodeCode",
    title: intlConst.formatMessage("callParam", "nodeCode"),
    dataIndex: "nodeCode",
  },
  {
    key: "nodeAttriId",
    title: intlConst.formatMessage("callParam", "nodeAttriId"),
    dataIndex: "nodeAttriId",
  },
  {
    key: "nodeAttriName",
    title: intlConst.formatMessage("callParam", "nodeAttriName"),
    dataIndex: "nodeAttriName",
  },
  {
    key: "actionTime",
    title: intlConst.formatMessage("callParam", "actionTime"),
    dataIndex: "actionTime",
  },
  {
    key: "actionResult",
    title: intlConst.formatMessage("callParam", "actionResult"),
    dataIndex: "actionResult",
  },
];
