import { NODE_TYPE, EDGE_TYPE, PORTS_TYPE } from "@/constants/graphConstants";
import { LOCAL } from '@/constants/publicConstant';
import CF from "./assets/images/CF.png";
import DR from "./assets/images/DR.png";
import H from "./assets/images/H.png";
import JC from "./assets/images/JC.png";
import KQ from "./assets/images/KQ.png";
import QD from "./assets/images/QD.png";
import SX from "./assets/images/SX.png";
import TC from "./assets/images/TC.png";
import Y from "./assets/images/Y.png";
import YS from "./assets/images/YS.png";

// 主题色
const themeColor = localStorage.getItem(LOCAL.THEME) || '';

/**
 * 节点图片映射
 */
// export const nodeImageMap = {
//   CF,
//   DR,
//   H,
//   KQ,
//   JC,
//   QD,
//   SX,
//   TC,
//   Y,
//   YS
// };

/**
 * 业务节点面板配置
 */
export const businessNodeList = [
  { nodeType: '01', prefix: 'DR', label: '导入', type: NODE_TYPE.IMAGE, src: DR },
  { nodeType: '02', prefix: 'KQ', label: '客群', type: NODE_TYPE.IMAGE, src: KQ },
  { nodeType: '03', prefix: 'QD', label: '渠道', type: NODE_TYPE.IMAGE, src: QD },
  { nodeType: '04', prefix: 'CF', label: '拆分', type: NODE_TYPE.IMAGE, src: CF },
  { nodeType: '05', prefix: 'SX', label: '筛选', type: NODE_TYPE.IMAGE, src: SX },
  { nodeType: '06', prefix: 'JC', label: '交叉', type: NODE_TYPE.IMAGE, src: JC },
  { nodeType: '07', prefix: 'Y', label: '与', type: NODE_TYPE.IMAGE, src: Y },
  { nodeType: '08', prefix: 'H', label: '或', type: NODE_TYPE.IMAGE, src: H },
  { nodeType: '09', prefix: 'TC', label: '剔除', type: NODE_TYPE.IMAGE, src: TC },
  { nodeType: '10', prefix: 'YS', label: '映射', type: NODE_TYPE.IMAGE, src: YS },
];

/**
 * 流程节点面板配置
 */
export const flowNodeList = [
  { nodeType: '01', label: '圆形', type: NODE_TYPE.CIRCLE },
  { nodeType: '02', label: '矩形', type: NODE_TYPE.RECT },
  { nodeType: '03', label: '菱形', type: NODE_TYPE.DIAMOND },
  { nodeType: '04', label: '椭圆形', type: NODE_TYPE.ELLIPSE },
  { nodeType: '05', label: '三角形', type: NODE_TYPE.ELLIPSE },
]
/**
 * 节点配置
 */
export const nodeConfig = {
  type: NODE_TYPE.CIRCLE,
  style: {
    size: 60,
    src: KQ,
    stroke: '#999',
    lineWidth: 1,
    portR: 4,
    ports: [
      { placement: PORTS_TYPE.LEFT, fill: '#fff', stroke: '#ff0000' },
      { placement: PORTS_TYPE.RIGHT, fill: '#fff', stroke: '#ff0000' },
      { placement: PORTS_TYPE.TOP, fill: '#fff', stroke: '#ff0000' },
      { placement: PORTS_TYPE.BOTTOM, fill: '#fff', stroke: '#ff0000' },
    ],
    portLineWidth: 1,
    portStroke: '#fff',
  },
};

/**
 * 边配置
 */
export const edgeConfig = {
  type: EDGE_TYPE.CUBIC_VERTICAL,
  style: {
    endArrow: true,
    // 颜色
    stroke: themeColor,
    // 线宽度
    lineWidth: 1,
    // 虚线
    // lineDash: 3,
  },
};