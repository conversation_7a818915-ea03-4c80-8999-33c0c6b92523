// 物料层-流程图
import { FC, lazy, memo, useEffect, useRef, useState } from 'react';
import { Graph, GraphData } from "@antv/g6";
import { ELE_TYPE, LAYOUT_TYPE } from "@/constants/graphConstants";
import G6, {
  INodePanelProps,
  useContextMenu,
  getDefaultToolBarItems,
  handleDefaultToolBarClick,
  edgeDefaultConfig,
  nodeDefaultConfig,
} from "@/components/graph";
import { businessNodeList, nodeConfig, edgeConfig } from './config';

const CustomDialog = lazy(() => import('./dialog/customDialog'));

interface IFlowGraphProps {
  operateType?: string,
  data?: GraphData,
  dragNodeList?: Array<INodePanelProps> | boolean | null;
  getContextmenuItems?: (targetType: string, graph: Graph | undefined) => Array<any>,
}
const FlowGraph: FC<IFlowGraphProps> = ({
  operateType,
  data,
  dragNodeList = businessNodeList,
  getContextmenuItems
}) => {
  // 内部变量
  const graphRef = useRef<Graph>();
  const [options, setOptions] = useState<any>(null);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const { getMenuDefaultItems, handleContextMenu, handleMenuClick } = useContextMenu();

  // 副作用
  useEffect(() => {
    console.log('这里拿不到，或者不是最新的 graph')
    setOptions({
      data,
      node: {...nodeDefaultConfig, ...nodeConfig},
      edge: {...edgeDefaultConfig, ...edgeConfig},
      layout: {
        type: LAYOUT_TYPE.ANTV_DARGE,
        ranksep: 32,
        nodesep: 8,
        sortByCombo: true,
      },
      // autoFit: 'center',
      behaviors: ['zoom-canvas', 'drag-canvas'],
      plugins: [
        {
          type: 'toolbar',
          position: 'top-left',
          className: 'g6-custom-toolbar',
          getItems: getDefaultToolBarItems,
          onClick: (item) => handleDefaultToolBarClick(item, graphRef.current),
        },
        {
          type: 'contextmenu',
          trigger: 'contextmenu',
          getItems: (e) => getContextmenuItems ? getContextmenuItems(e, graphRef.current) : getMenuDefaultItems(e),
          enable: (e) => e.targetType !== ELE_TYPE.EDGE,
          onClick: (type, target, current) => handleMenuClick(type, target, current, graphRef.current),
        },
        {
          type: 'history',
          key: 'history',
        },
        {
          type: 'minimap',
          size: [240, 160],
        },
      ],
    })
  }, []);

  // 事件处理
  const handleRender = (graph) => graphRef.current = graph;
  const handleDestroy = () => graphRef.current = undefined;
  const handleContextMenuClick = (type, e, graph) => {
    console.log('handleContextMenuClick', type, e, graph)
  }
  const handleCloseDialog = () => setDialogVisible(false);
  const handleCloseDrawer = () => setDrawerVisible(false);
  const handleNodeClick = (node) => {
    console.log('handleNodeClick', node);
  }
  const handleNodeDbClick = (node) => {
    console.log('handleNodeDbClick', node);
    setDialogVisible(true);
  };
  const handlePortDragStart = (e) => {
    console.log('handlePortDragStart', e);
    const { item, target } = e;
    const portId = target.get('id');
    graphRef.current.setItemState(item, `port:${portId}:active`, true);
  }
  const handlePortDragEnd = (e) => {
    console.log('handlePortDragEnd', e);
    const { item, target } = e;
    const portId = target.get('id');
    graphRef.current.setItemState(item, `port:${portId}:active`, false);
  }
  const handlePortDrag = (e) => {
    console.log('handlePortDrag', e);
    const {item, target} = e;
    const portId = target.get('id');
    const model = item.getModel();
    const port = model.ports.items.find((p) => p.id === portId);
    if (port) {
      port.x = target.x;
      port.y = target.y;
      graphRef.current.updateItem(item, model);
    }
  }
  const handlePortConnect = (e) => {
    console.log('handlePortConnect', e);
    const { source, target } = e;
    const sourceNode = source.get('item');
    const targetNode = target.get('item');
    const sourcePort = source.get('id');
    const targetPort = target.get('id');

    const edge = {
      source: sourceNode.getModel().id,
      target: targetNode.getModel().id,
      sourcePort,
      targetPort,
    };

    graphRef.current.addItem('edge', edge);
  }

  // 绑定内置交互事件
  const bindEvents = {
    onNodeClick: handleNodeClick,
    onNodeDbClick: handleNodeDbClick,
    onPortDragStart: handlePortDragStart,
    onPortDragEnd: handlePortDragEnd,
    onPortDrag: handlePortDrag,
    onPortDragConnect: handlePortConnect,
  }

  return (
    <>
      <G6
        dragNodeList={dragNodeList}
        options={options}
        bindEvents={bindEvents}
        onRender={handleRender}
        onDestroy={handleDestroy}
      />
      <CustomDialog dialogVisible={dialogVisible} onClose={handleCloseDialog} />
    </>
  );
};

export default memo(FlowGraph);
