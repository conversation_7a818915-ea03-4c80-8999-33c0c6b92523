import dictConstants from "@/constants/dictConstants";
import {
  COMPONENT_TYPE,
  DATE_FORMATE,
  I18N_COMON_PAGENAME,
  TIME_FORMATE,
} from "@/constants/publicConstant";
import useIntlCustom from "@/hooks/useIntlCustom";
import dayjs from "dayjs";

const usePageConfig = () => {
  const { translate } = useIntlCustom();
  const descData = [
    {
      props: {
        title: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "collectionInfoOverview"
        ),
        column: 3,
      },
      type: COMPONENT_TYPE.FORM,
      key: "infoForm1",
      columns: [
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "caseCode"),
          key: "caseCode",
          dataIndex: "caseCode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "customerNo"),
          key: "orgCustNbr",
          dataIndex: "orgCustNbr",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "customerName"),
          key: "custName",
          dataIndex: "custName",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "idType"),
          key: "icType",
          dataIndex: "icType",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "idNumber"),
          key: "custIc",
          dataIndex: "custIc",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "chargeOffFlag"),
          key: "chargoffFlag",
          dataIndex: "chargoffFlag",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "collectionCount"),
          key: "countOdue",
          dataIndex: "countOdue",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "collectionEntryTime"
          ),
          key: "dteIntoCollection",
          dataIndex: "dteIntoCollection",
          render: (record) =>
            dayjs(record.dteIntoCollection).format(TIME_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "collectionExitTime"
          ),
          key: "dteOutCollection",
          dataIndex: "dteOutCollection",
          render: (record) =>
            dayjs(record.dteOutCollection).format(TIME_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "firstCollectionTime"
          ),
          key: "firstDate",
          dataIndex: "firstDate",
          render: (record) => dayjs(record.firstDate).format(TIME_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "previousCaseCode"),
          key: "lastCaseCode",
          dataIndex: "lastCaseCode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "previousCaseTime"),
          key: "lastCaseDate",
          dataIndex: "lastCaseDate",
          render: (record) => dayjs(record.lastCaseDate).format(TIME_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "litigationDate"),
          key: "lawDate",
          dataIndex: "lawDate",
          render: (record) => dayjs(record.lawDate).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "litigationFlag"),
          key: "lawFlag",
          dataIndex: "lawFlag",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "module"),
          key: "model",
          dataIndex: "model",
          render: (text, record) => {
            const str = dictConstants.WORKBENCH_MODEL.find(
              (item) => item.key === record?.model
            )?.value;
            return record?.model
              ? record?.model +
                  "-" +
                  translate(I18N_COMON_PAGENAME.CALL_PARAM, str)
              : record?.model;
          },
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "currentStatus"),
          key: "currState",
          dataIndex: "currState",
          render: (text, record) => {
            const str = dictConstants.WORKBENCH_CURR_STATE.find(
              (item) => item.key === record?.currState
            )?.value;
            return record?.currState
              ? record?.currState +
                  "-" +
                  translate(I18N_COMON_PAGENAME.CALL_PARAM, str)
              : record?.currState;
          },
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "caseStatus"),
          key: "caseState",
          dataIndex: "caseState",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "riskPoolCode"),
          key: "teamCode",
          dataIndex: "teamCode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "lastActionTime"),
          key: "lastOpeTime",
          dataIndex: "lastOpeTime",
          render: (record) => dayjs(record.lastOpeTime).format(TIME_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "teamEntryDate"),
          key: "inTeamDate",
          dataIndex: "inTeamDate",
          render: (record) => dayjs(record.inTeamDate).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "riskPoolReasonCode"
          ),
          key: "reasonCode",
          dataIndex: "reasonCode",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "lostContactDecisionCode"
          ),
          key: "noneContactRuleCode",
          dataIndex: "noneContactRuleCode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "contactStatus"),
          key: "contactCode",
          dataIndex: "contactCode",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "manualContactCount"
          ),
          key: "contactTimes",
          dataIndex: "contactTimes",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "resultCompletionTime"
          ),
          key: "resultTime",
          dataIndex: "resultTime",
          render: (record) => dayjs(record.resultTime).format(TIME_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "callDirection"),
          key: "resultDirection",
          dataIndex: "resultDirection",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "attemptedContactCount"
          ),
          key: "attemptCount",
          dataIndex: "attemptCount",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "effectiveCollectionTotal"
          ),
          key: "callTotal",
          dataIndex: "callTotal",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "previousTemplate"),
          key: "lastModel",
          dataIndex: "lastModel",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "englishName"),
          key: "ename",
          dataIndex: "ename",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "gender"),
          key: "eusex",
          dataIndex: "eusex",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "statementType"),
          key: "statementTypeAll",
          dataIndex: "statementTypeAll",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "billingDay"),
          key: "billingCycle",
          dataIndex: "billingCycle",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "badDebtFlag"),
          key: "badnessCode",
          dataIndex: "badnessCode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "totalDebt"),
          key: "classIBalance",
          dataIndex: "classIBalance",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "riskLevel"),
          key: "riskRank",
          dataIndex: "riskRank",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "delayDays"),
          key: "delayDays",
          dataIndex: "delayDays",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "activeInstallmentFlag"
          ),
          key: "activeInstallmentFlag",
          dataIndex: "activeInstallmentFlag",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "largeAccountFlag"),
          key: "productType",
          dataIndex: "productType",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "status24"),
          key: "status24",
          dataIndex: "status24",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "outsourcingFlag"),
          key: "wcsOutcode",
          dataIndex: "wcsOutcode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "handType"),
          key: "handType",
          dataIndex: "handType",
        },
      ],
    },
    {
      props: {
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "customerInfo"),
        column: 3,
      },
      type: COMPONENT_TYPE.FORM,
      key: "infoForm2",
      columns: [
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "branchNumber"),
          key: "bankNbr",
          dataIndex: "bankNbr",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "subBranchNumber"),
          key: "branchNbr",
          dataIndex: "branchNbr",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "restatedAndCannotBeOpened"
          ),
          key: "restateNotOpen",
          dataIndex: "restateNotOpen",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "permanentCreditLimit"
          ),
          key: "crlimitPerm",
          dataIndex: "crlimitPerm",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "temporaryCreditLimit"
          ),
          key: "crlimitTemp",
          dataIndex: "crlimitTemp",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "memo"),
          key: "note",
          dataIndex: "note",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "collectionRemarks"),
          key: "custWcsMemo",
          dataIndex: "custWcsMemo",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "vipFlagDomain"),
          key: "vipcode",
          dataIndex: "vipcode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "idValidity"),
          key: "permIdExp",
          dataIndex: "permIdExp",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "dateOfBirth"),
          key: "bornDate",
          dataIndex: "bornDate",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "residenceType"),
          key: "euTypeOfRes",
          dataIndex: "euTypeOfRes",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "residenceDuration"),
          key: "euPerOfRes",
          dataIndex: "euPerOfRes",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "occupation"),
          key: "job",
          dataIndex: "job",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "companyName"),
          key: "employer",
          dataIndex: "employer",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "annualSalary"),
          key: "income",
          dataIndex: "income",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "abCustomerGroup"),
          key: "abClass",
          dataIndex: "abClass",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "blockCode"),
          key: "blockcode",
          dataIndex: "blockcode",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "blockCodeDate"),
          key: "blockDate",
          dataIndex: "blockDate",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "previousBlockCode"),
          key: "prvBlockcode",
          dataIndex: "prvBlockcode",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "previousBlockCodeDate"
          ),
          key: "prvBlockDate",
          dataIndex: "prvBlockDate",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "mobileNumber"),
          key: "telNum",
          dataIndex: "telNum",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "email"),
          key: "email",
          dataIndex: "email",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "graduationSchoolName"
          ),
          key: "school",
          dataIndex: "school",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "educationLevel"),
          key: "prexCustQualification",
          dataIndex: "prexCustQualification",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "major"),
          key: "major",
          dataIndex: "major",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "maritalStatus"),
          key: "euMaritalStatus",
          dataIndex: "euMaritalStatus",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardholderType"),
          key: "custType",
          dataIndex: "custType",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardholderStatus"),
          key: "custStatus",
          dataIndex: "custStatus",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardholderStatusChangeDate"
          ),
          key: "custStatusDate",
          dataIndex: "custStatusDate",
          render: (record) => dayjs(record.custStatusDate).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "guarantorName"),
          key: "custGName",
          dataIndex: "custGName",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "guarantorPhone"),
          key: "custGPhone",
          dataIndex: "custGPhone",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "deposit"),
          key: "depositArea",
          dataIndex: "depositArea",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "creationAccountOpeningDate"
          ),
          key: "custOpenDate",
          dataIndex: "custOpenDate",
          render: (record) => dayjs(record.custOpenDate).format(DATE_FORMATE),
        },
      ],
    },
    {
      props: {
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "contactInfo"),
        column: 3,
      },
      type: COMPONENT_TYPE.FORM,
      key: "infoForm3",
      columns: [
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "homePhone"),
          key: "custPhone",
          dataIndex: "custPhone",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "alternateContactPhone"
          ),
          key: "custBackPhone",
          dataIndex: "custBackPhone",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "workPhone1"),
          key: "custEmpTel1",
          dataIndex: "custEmpTel1",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "workPhone2"),
          key: "custEmpTel2",
          dataIndex: "custEmpTel2",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "workPhone3"),
          key: "custEmpTel3",
          dataIndex: "custEmpTel3",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "emergencyContact1Relationship"
          ),
          key: "custGlRln1",
          dataIndex: "custGlRln1",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "emergencyContact1Name"
          ),
          key: "custGlNam1",
          dataIndex: "custGlNam1",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "emergencyContact1Phone"
          ),
          key: "custGlTel1",
          dataIndex: "custGlTel1",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "emergencyContact2Relationship"
          ),
          key: "custGlRln2",
          dataIndex: "custGlRln2",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "emergencyContact2Name"
          ),
          key: "custGlNam2",
          dataIndex: "custGlNam2",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "emergencyContact2Phone"
          ),
          key: "custGlTel2",
          dataIndex: "custGlTel2",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "householdAddress"),
          key: "custHomeCityAddr",
          dataIndex: "custHomeCityAddr",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "homeAddress"),
          key: "custAddr",
          dataIndex: "custAddr",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "workAddress"),
          key: "custGEmpA",
          dataIndex: "custGEmpA",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "currentResidence"),
          key: "custmAddr",
          dataIndex: "custmAddr",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "billingAddress"),
          key: "billAddr",
          dataIndex: "billAddr",
        },
      ],
    },
    {
      props: {
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountInfo"),
        column: 3,
      },
      type: COMPONENT_TYPE.FORM,
      key: "infoForm4",
      columns: [
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "institutionNumber"),
          key: "jiGouHao",
          dataIndex: "jiGouHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardTransferNewAccountCurrency"
          ),
          key: "kaZhuanXinZhangHuBiZhong",
          dataIndex: "kaZhuanXinZhangHuBiZhong",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "creditCardHolderNumber"
          ),
          key: "xinYongKaChiKaRenHao",
          dataIndex: "xinYongKaChiKaRenHao",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountBlockCode"),
          key: "zhangHuFengSuoMa",
          dataIndex: "zhangHuFengSuoMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountBlockCodeReasonCode"
          ),
          key: "zhangHuFengSuoMaYuanYinMa",
          dataIndex: "zhangHuFengSuoMaYuanYinMa",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountSingleDay"),
          key: "zhangHuDanRi",
          dataIndex: "zhangHuDanRi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountFreezeStatus"
          ),
          key: "zhangHuDongJieZhuangTai",
          dataIndex: "zhangHuDongJieZhuangTai",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "waiverOfPenaltyFlag"
          ),
          key: "mianShouWeiYueJinBiaoShi",
          dataIndex: "mianShouWeiYueJinBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "collectionOutsourcingFlag"
          ),
          key: "cuiShouWeiWaiBiaoShi",
          dataIndex: "cuiShouWeiWaiBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountCollectionTimes"
          ),
          key: "zhangHuRuCuiCiShu",
          dataIndex: "zhangHuRuCuiCiShu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "currentMinimumRepaymentRemaining"
          ),
          key: "benQiZuiDiHuanKuanShengYu",
          dataIndex: "benQiZuiDiHuanKuanShengYu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "150DayDeferralAmount"
          ),
          key: "150TianYanQiJinE",
          dataIndex: "150TianYanQiJinE",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "deferralPeriods"),
          key: "yanQiQiShu",
          dataIndex: "yanQiQiShu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "150DayDeferralTimes"
          ),
          key: "150TianYanQiCiShu",
          dataIndex: "150TianYanQiCiShu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "initialConsumptionRemainingAmount"
          ),
          key: "qiChuXiaoFeiShengYuJinE",
          dataIndex: "qiChuXiaoFeiShengYuJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "unsecuredRepaymentAmount"
          ),
          key: "weiGuaXianHuanKuanJinE",
          dataIndex: "weiGuaXianHuanKuanJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountWithdrawalAmountAccumulated"
          ),
          key: "zhangHuQuXianJinELeiJi",
          dataIndex: "zhangHuQuXianJinELeiJi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountLastWithdrawalDate"
          ),
          key: "zhangHuShangCiQuXianRiQi",
          dataIndex: "zhangHuShangCiQuXianRiQi",
          render: (record) =>
            dayjs(record.zhangHuShangCiQuXianRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "pledgedConsumptionLoanAdjustmentBalance"
          ),
          key: "yiGuaXiaoFeiDaiTiaoYuE",
          dataIndex: "yiGuaXiaoFeiDaiTiaoYuE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "specialProductFlag"
          ),
          key: "teShuChanPinBiaoShi",
          dataIndex: "teShuChanPinBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountHighestAmountDate"
          ),
          key: "zhangHuZuiGaoJinERiQi",
          dataIndex: "zhangHuZuiGaoJinERiQi",
          render: (record) =>
            dayjs(record.zhangHuZuiGaoJinERiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "previousRepaymentDueDate"
          ),
          key: "shangQiHuanKuanDaoQiRiQi",
          dataIndex: "shangQiHuanKuanDaoQiRiQi",
          render: (record) =>
            dayjs(record.shangQiHuanKuanDaoQiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountNumber"),
          key: "zhangHuHao",
          dataIndex: "zhangHuHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardTransferNewAccountSerialNumber"
          ),
          key: "kaZhuanXinZhangHuXuHao",
          dataIndex: "kaZhuanXinZhangHuXuHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountToleranceSwitchFlag"
          ),
          key: "zhangHuRongChaKaiGuanBiaoShi",
          dataIndex: "zhangHuRongChaKaiGuanBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "oldAccountBlockCode"
          ),
          key: "jiuZhangHuFengSuoMa",
          dataIndex: "jiuZhangHuFengSuoMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "oldAccountBlockCodeReasonCode"
          ),
          key: "jiuZhangHuFengSuoMaYuanYinMa",
          dataIndex: "jiuZhangHuFengSuoMaYuanYinMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountBirthdayValidDate"
          ),
          key: "zhangHuShengRiYouXiaoRi",
          dataIndex: "zhangHuShengRiYouXiaoRi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountFreezeReasonCode"
          ),
          key: "zhangHuDongJieYuanYinMa",
          dataIndex: "zhangHuDongJieYuanYinMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "waiverOfOverlimitFeeFlag"
          ),
          key: "mianShouChaoXianJinBiaoShi",
          dataIndex: "mianShouChaoXianJinBiaoShi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountCoreFlag"),
          key: "zhangHuHeXinBiaoShi",
          dataIndex: "zhangHuHeXinBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountLastDeferralDate"
          ),
          key: "zhangHuShangCiYanQiRiQi",
          dataIndex: "zhangHuShangCiYanQiRiQi",
          render: (record) =>
            dayjs(record.zhangHuShangCiYanQiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "30DayDeferralAmount"
          ),
          key: "30TianYanQiJinE",
          dataIndex: "30TianYanQiJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "180DayDeferralAmount"
          ),
          key: "180TianYanQiJinE",
          dataIndex: "180TianYanQiJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "30DayDeferralTimes"
          ),
          key: "30TianYanQiCiShu",
          dataIndex: "30TianYanQiCiShu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "180DayDeferralTimes"
          ),
          key: "180TianYanQiCiShu",
          dataIndex: "180TianYanQiCiShu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "withdrawalInitialBalance"
          ),
          key: "tiXianQiChuYuE",
          dataIndex: "tiXianQiChuYuE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "redemptionRepaymentOriginalBalance"
          ),
          key: "duiXianHuanKuanYuanYuE",
          dataIndex: "duiXianHuanKuanYuanYuE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountInterestWaiverFlag"
          ),
          key: "zhangHuMianXiBiaoShi",
          dataIndex: "zhangHuMianXiBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "lastConsumptionAmount"
          ),
          key: "shangCiXiaoFeiJinE",
          dataIndex: "shangCiXiaoFeiJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "pledgedCashLoanAdjustmentBalance"
          ),
          key: "yiGuaXianDaiTiaoYuE",
          dataIndex: "yiGuaXianDaiTiaoYuE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "lastTransactionDate"
          ),
          key: "shangCiJiaoYiRiQi",
          dataIndex: "shangCiJiaoYiRiQi",
          render: (record) =>
            dayjs(record.shangCiJiaoYiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "currentBusinessDate"
          ),
          key: "dangQianJingJiRiQi",
          dataIndex: "dangQianJingJiRiQi",
          render: (record) =>
            dayjs(record.dangQianJingJiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountProduct"),
          key: "zhangHuPin",
          dataIndex: "zhangHuPin",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountStatus"),
          key: "zhangHuZhuangTai",
          dataIndex: "zhangHuZhuangTai",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "statusChangeDate"),
          key: "zhuangTaiGaiBianRiQi",
          dataIndex: "zhuangTaiGaiBianRiQi",
          render: (record) =>
            dayjs(record.zhuangTaiGaiBianRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "singleCurrencySettlementFlag"
          ),
          key: "danYiHuoBiJieSuanBiaoShi",
          dataIndex: "danYiHuoBiJieSuanBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accountBlockCodeRemarks"
          ),
          key: "zhangHuFengSuoMaBeiZhu",
          dataIndex: "zhangHuFengSuoMaBeiZhu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "automaticRepaymentAccount"
          ),
          key: "ziDongHuanKuanZhangHu",
          dataIndex: "ziDongHuanKuanZhangHu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "pureAnnualFeeDeferralFlag"
          ),
          key: "chunNianFeiYanQiBiaoShi",
          dataIndex: "chunNianFeiYanQiBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "reasonForCollection"
          ),
          key: "ruCuiYuanYin",
          dataIndex: "ruCuiYuanYin",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "exitCollectionDate"
          ),
          key: "chuCuiRiQi",
          dataIndex: "chuCuiRiQi",
          render: (record) => dayjs(record.chuCuiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "120DayDeferralAmount"
          ),
          key: "120TianYanQiJinE",
          dataIndex: "120TianYanQiJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "120DayDeferralTimes"
          ),
          key: "120TianYanQiCiShu",
          dataIndex: "120TianYanQiCiShu",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "gracePeriodDate"),
          key: "kuanXianRiQi",
          dataIndex: "kuanXianRiQi",
          render: (record) => dayjs(record.kuanXianRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accumulatedAccountConsumptionAmount"
          ),
          key: "zhangHuXiaoFeiJinELeiJi",
          dataIndex: "zhangHuXiaoFeiJinELeiJi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "lastWithdrawalAmount"
          ),
          key: "shangCiQuXianJinE",
          dataIndex: "shangCiQuXianJinE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "currentRepaymentGracePeriodFlag"
          ),
          key: "dangQianHuanKuanRongShiBiao",
          dataIndex: "dangQianHuanKuanRongShiBiao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "highestAccountBalance"
          ),
          key: "zhangHuZuiGaoYuE",
          dataIndex: "zhangHuZuiGaoYuE",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "waiverOfOverlimitAmount"
          ),
          key: "mianJiChaoXianJin",
          dataIndex: "mianJiChaoXianJin",
        },
      ],
    },
    {
      props: {
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardInfo"),
        column: 3,
      },
      type: COMPONENT_TYPE.FORM,
      key: "infoForm5",
      columns: [
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "institutionNumber"),
          key: "jiGouHao",
          dataIndex: "jiGouHao",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardLevel"),
          key: "kaDengJi",
          dataIndex: "kaDengJi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "specialProductFlag"
          ),
          key: "teShuChanPinBiaoShi",
          dataIndex: "teShuChanPinBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardholderInstitutionNumber"
          ),
          key: "chiKaRenJiGouHao",
          dataIndex: "chiKaRenJiGouHao",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardBlockCode"),
          key: "kaPianFengSuoMa",
          dataIndex: "kaPianFengSuoMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "previousCardBlockCode"
          ),
          key: "kaPianShangYiFengSuoMa",
          dataIndex: "kaPianShangYiFengSuoMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "newCurrencyForCardReplacement"
          ),
          key: "huanKaXinBiZhong",
          dataIndex: "huanKaXinBiZhong",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "annualFeeDiscountFlag"
          ),
          key: "nianFeiZheKouBiaoShi",
          dataIndex: "nianFeiZheKouBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "numberOfPasswordErrors"
          ),
          key: "miMaCuoWuCiShu",
          dataIndex: "miMaCuoWuCiShu",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "abbreviatedCardholderName"
          ),
          key: "kaPianXingMingJianCheng",
          dataIndex: "kaPianXingMingJianCheng",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "applicationFormSerialNumber"
          ),
          key: "shenQingShuLiuShuiBianHao",
          dataIndex: "shenQingShuLiuShuiBianHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "marketingStaffEmployeeNumber"
          ),
          key: "yingXiaoRenYuanGongHao",
          dataIndex: "yingXiaoRenYuanGongHao",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "closingDate"),
          key: "guanBiRiQi",
          dataIndex: "guanBiRiQi",
          render: (record) => dayjs(record.guanBiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardTransactionScopeFlag"
          ),
          key: "kaPianJiaoYiFanWeiBiaoShi",
          dataIndex: "kaPianJiaoYiFanWeiBiaoShi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountNumber"),
          key: "zhangHao",
          dataIndex: "zhangHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "primaryOrSupplementaryCardFlag"
          ),
          key: "zhuFuKaBiaoShi",
          dataIndex: "zhuFuKaBiaoShi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardVersionCode"),
          key: "kaBanDaiMa",
          dataIndex: "kaBanDaiMa",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardholderNumber"),
          key: "chiKaRenHao",
          dataIndex: "chiKaRenHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardBlockReasonCode"
          ),
          key: "kaPianFengSuoYuanYinMa",
          dataIndex: "kaPianFengSuoYuanYinMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "previousCardBlockReasonCode"
          ),
          key: "kaPianShangYiFengSuoYuanYinMa",
          dataIndex: "kaPianShangYiFengSuoYuanYinMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "newProductNumberForCardReplacement"
          ),
          key: "huanKaXinChanPinBianHao",
          dataIndex: "huanKaXinChanPinBianHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "annualFeeWaiverFlag"
          ),
          key: "mianShouNianFeiBiaoShi",
          dataIndex: "mianShouNianFeiBiaoShi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "passwordErrorDate"),
          key: "miMaCuoWuRiQi",
          dataIndex: "miMaCuoWuRiQi",
          render: (record) => dayjs(record.miMaCuoWuRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardholderName"),
          key: "chiKaRenXingMing",
          dataIndex: "chiKaRenXingMing",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "channelCode"),
          key: "tongLuDaiMa",
          dataIndex: "tongLuDaiMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "marketingStaffName"
          ),
          key: "yingXiaoRenYuanXingMing",
          dataIndex: "yingXiaoRenYuanXingMing",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardFeeParameterCode"
          ),
          key: "kaPianFeiYongCanShuBianMa",
          dataIndex: "kaPianFeiYongCanShuBianMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardActivationStatus"
          ),
          key: "kaPianJiHuoZhuangTai",
          dataIndex: "kaPianJiHuoZhuangTai",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "currency"),
          key: "biZhong",
          dataIndex: "biZhong",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardQuality"),
          key: "kaPianZhiLiang",
          dataIndex: "kaPianZhiLiang",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "validityPeriod"),
          key: "youXiaoQi",
          dataIndex: "youXiaoQi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "supplementaryCardholderInstitutionNumber"
          ),
          key: "fuShuChiKaRenJiGouHao",
          dataIndex: "fuShuChiKaRenJiGouHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardBlockCodeMaintenanceDate"
          ),
          key: "kaPianFengSuoMaWeiHuRiQi",
          dataIndex: "kaPianFengSuoMaWeiHuRiQi",
          render: (record) =>
            dayjs(record.kaPianFengSuoMaWeiHuRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "endDateOfPreviousBlockCode"
          ),
          key: "kaPianShangYiFengSuoMaJieShuRiQi",
          dataIndex: "kaPianShangYiFengSuoMaJieShuRiQi",
          render: (record) =>
            dayjs(record.kaPianShangYiFengSuoMaJieShuRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "newCardNumberForCardReplacement"
          ),
          key: "huanKaXinKaHao",
          dataIndex: "huanKaXinKaHao",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "annualFeeCollectionDate"
          ),
          key: "nianFeiShouQuRiQi",
          dataIndex: "nianFeiShouQuRiQi",
          render: (record) =>
            dayjs(record.nianFeiShouQuRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "passwordFlag"),
          key: "miMaBiaoShi",
          dataIndex: "miMaBiaoShi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "engravedName"),
          key: "keYinMingCheng",
          dataIndex: "keYinMingCheng",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardCollectionMethod"
          ),
          key: "lingKaFangShi",
          dataIndex: "lingKaFangShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardCancellationExpiryDate"
          ),
          key: "xiaoKaDaoQiRiQi",
          dataIndex: "xiaoKaDaoQiRiQi",
          render: (record) =>
            dayjs(record.xiaoKaDaoQiRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardProcessingParameterCode"
          ),
          key: "kaPianChuLiCanShuBianMa",
          dataIndex: "kaPianChuLiCanShuBianMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardActivationDate"
          ),
          key: "kaPianJiHuoRiQi",
          dataIndex: "kaPianJiHuoRiQi",
          render: (record) =>
            dayjs(record.kaPianJiHuoRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardProduct"),
          key: "kaChanPin",
          dataIndex: "kaChanPin",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardStatusChangeDate"
          ),
          key: "kaPianZhuangTaiGaiBianRi",
          dataIndex: "kaPianZhuangTaiGaiBianRi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardMakingOperationFlag"
          ),
          key: "zhiKaZuoYeBiaoShi",
          dataIndex: "zhiKaZuoYeBiaoShi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardMakingDate"),
          key: "zhiKaRiQi",
          dataIndex: "zhiKaRiQi",
          render: (record) => dayjs(record.zhiKaRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "previousCardMakingDate"
          ),
          key: "shangYiZhiKaRiQi",
          dataIndex: "shangYiZhiKaRiQi",
          render: (record) =>
            dayjs(record.shangYiZhiKaRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "accumulatedCardMakingRequests"
          ),
          key: "zhiKaQingQiuLeiJi",
          dataIndex: "zhiKaQingQiuLeiJi",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "oldCardNumber"),
          key: "jiuKaKaHao",
          dataIndex: "jiuKaKaHao",
        },
        {
          title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardEndDate"),
          key: "kaPianJieShuRiQi",
          dataIndex: "kaPianJieShuRiQi",
          render: (record) =>
            dayjs(record.kaPianJieShuRiQi).format(DATE_FORMATE),
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "smallAmountWaiverFlag"
          ),
          key: "mianBiaoShi",
          dataIndex: "mianBiaoShi",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "co - brandedCardPartnerCode"
          ),
          key: "lianMingKaHeZuoFangBianMa",
          dataIndex: "lianMingKaHeZuoFangBianMa",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardCancellationStaffName"
          ),
          key: "xiaoKaRenYuanXingMing",
          dataIndex: "xiaoKaRenYuanXingMing",
        },
        {
          title: translate(
            I18N_COMON_PAGENAME.CALL_PARAM,
            "cardActivationMethod"
          ),
          key: "kaPianJiHuoFangShi",
          dataIndex: "kaPianJiHuoFangShi",
        },
      ],
    },
  ];
  const segmentedData = {
    A: {
      label: {
        backgroundColor: "#f56a00",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "transferShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "transfer"),
      },
      value: "A",
    },
    B: {
      label: {
        backgroundColor: "#FFEB3B",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "advancePaymentShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "advancePayment"),
      },
      value: "B",
    },
    C: {
      label: {
        backgroundColor: "#4CAF50",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "stopPaymentShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "stopPayment"),
      },
      value: "C",
    },
    D: {
      label: {
        backgroundColor: "#FF9800",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "reductionShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "reduction"),
      },
      value: "D",
    },
    E: {
      label: {
        backgroundColor: "#2196F3",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "marginDeductionShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "marginDeduction"),
      },
      value: "E",
    },
    F: {
      label: {
        backgroundColor: "#87d068",
        text: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "sameNameAccountAdjustmentShort"
        ),
        title: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "sameNameAccountAdjustment"
        ),
      },
      value: "F",
    },
    G: {
      label: {
        backgroundColor: "#f56a00",
        text: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "addStrongCollectionShort"
        ),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "addStrongCollection"),
      },
      value: "G",
    },
    H: {
      label: {
        backgroundColor: "#FFEB3B",
        text: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "negotiatedInstallmentShort"
        ),
        title: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "negotiatedInstallment"
        ),
      },
      value: "H",
    },
    I: {
      label: {
        backgroundColor: "#4CAF50",
        text: translate(
          I18N_COMON_PAGENAME.CALL_PARAM,
          "lightFinanceOrderShort"
        ),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "lightFinanceOrder"),
      },
      value: "I",
    },
    J: {
      label: {
        backgroundColor: "#FF9800",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "infoRepairShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "infoRepair"),
      },
      value: "J",
    },
    K: {
      label: {
        backgroundColor: "#2196F3",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "smsShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "sms"),
      },
      value: "K",
    },
    L: {
      label: {
        backgroundColor: "#87d068",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "wechatShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "wechat"),
      },
      value: "L",
    },
    M: {
      label: {
        backgroundColor: "#4CAF50",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "appShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "app"),
      },
      value: "M",
    },
    N: {
      label: {
        backgroundColor: "#FF9800",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "emailShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "email"),
      },
      value: "N",
    },
    O: {
      label: {
        backgroundColor: "#2196F3",
        text: translate(I18N_COMON_PAGENAME.CALL_PARAM, "letterShort"),
        title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "letter"),
      },
      value: "O",
    },
  };
  // 页面右侧模块功能配置
  const anchorData = {
    infoForm1: {
      key: "infoForm1",
      href: "#infoForm1",
      title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "collectionInfo"),
    },
    infoForm2: {
      key: "infoForm2",
      href: "#infoForm2",
      title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "customerInfo"),
    },
    infoForm3: {
      key: "infoForm3",
      href: "#infoForm3",
      title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "contactInfo"),
    },
    infoForm4: {
      key: "infoForm4",
      href: "#infoForm4",
      title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "accountInfo"),
    },
    infoForm5: {
      key: "infoForm5",
      href: "#infoForm5",
      title: translate(I18N_COMON_PAGENAME.CALL_PARAM, "cardInfo"),
    },
  };

  return {
    descData,
    segmentedData,
    anchorData,
  };
};

export default usePageConfig;
