/**
 * 催收工作台组件
 */
import { memo, useEffect, useRef, useState } from "react";
import {
  Card,
  Anchor,
  Segmented,
  Avatar,
  Tooltip,
  Form,
  Input,
  message,
} from "antd";
import { ProDescriptions } from "@ant-design/pro-components";
// import { descData, anchorData, segmentedData } from "./pageConfig";

import RouterBar from "@/layouts/header/RouterBar";
import { COMPONENT_TYPE } from "@/constants/publicConstant";
import CommonTable from "@/components/table/CommonTable";
import commonService from "@/services/common";
import styles from "./index.module.css";
import { useForm } from "antd/es/form/Form";
import CommonModal from "@/components/commonModal";
import urlConstants from "@/constants/urlConstants";
import useIntlCustom from '@/hooks/useIntlCustom';
import usePageConfig from "./usePageConfig";

const CallBench = ({ dataSource, onSubmit, workBenchName }) => {
  const { translate } = useIntlCustom();
  const [form] = useForm();
  const formRef = useRef(null);
  const [open, setOpen] = useState<boolean>(false);
  const [workConfigData, setWorkConfigData] = useState<any>({
    callFields: {},
    anchorData: [],
    segmentedData: [],
  });
  const { descData, anchorData, segmentedData } = usePageConfig();

  useEffect(() => {
    getBenchConfig();
  }, []);

  // 获取页面配置信息
  const getBenchConfig = async () => {
    const res: any = await commonService.getEditPostBiz({
      url: urlConstants.WORKBENCH_CONFIG.LIST,
      workBenchName,
    });
    const data = res.data?.[0] || [];
    // 催收员自己能看到的右侧模块信息-workBenchModel
    let newAnchorData: any = [];
    for (const key in anchorData) {
      if (data?.workBenchModel?.split(",")?.includes(key)) {
        newAnchorData.push(anchorData[key]);
      }
    }
    // 催收员自己能看到的左侧辅助信息-auxiliaryFunctions
    let newSegmentedData: any = [];
    for (const key in segmentedData) {
      if (data?.auxiliaryFunctions?.split(",")?.includes(key)) {
        newSegmentedData.push(segmentedData[key]);
      }
    }
    // 催收员自己能看到的催收信息表单项-callFields
    const newCallFields = descData[0].columns.filter((item) =>
      data?.callFields?.split(",")?.includes(item.key)
    );
    const oldCallfields = descData
      .filter((item) => data?.workBenchModel?.split(",")?.includes(item.key))
      .slice(1);

    setWorkConfigData({
      ...workConfigData,
      callFields: [
        {
          props: {
            title: translate('callParam', 'collectionInfoOverview'),
            column: 4,
          },
          type: COMPONENT_TYPE.FORM,
          key: "infoForm1",
          columns: newCallFields,
        },
      ].concat(oldCallfields),
      anchorData: newAnchorData,
      segmentedData: newSegmentedData,
    });
  };

  const handleSubmit = async () => {
    try {
      form.validateFields().then(async (values) => {
        const res: any = await commonService.getEditPostBiz({
          ...values,
          url: urlConstants.CASE_BASE_INFO.EDIT,
          id: dataSource.infoForm1?.id,
          model: dataSource.infoForm1?.model,
          prvBlockDate: dataSource.infoForm2?.blockDate,
        });
        if (res.header?.errorCode === "000000") {
          message.success(translate('callParam', 'blockCodeModifySuccess'));
          setOpen(false);
          onSubmit?.();
          form.resetFields();
        }
      });
    } catch (e) {
      console.info(e);
    }
  };

  const content = () => {
    return (
      <Form
        ref={formRef}
        form={form}
        name="basic"
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        autoComplete="off"
      >
        <Form.Item
          label={translate('callParam', 'originalLockCode')}
          name="prvBlockcode"
          rules={[{ required: true }]}
        >
          <Input style={{ width: 200 }} disabled />
        </Form.Item>
        <Form.Item
          label={translate('callParam', 'newLockCode')}
          name="blockcode"
          rules={[{ required: true }]}
        >
          <Input style={{ width: 200 }} />
        </Form.Item>
      </Form>
    );
  };

  // 右侧锚点组件
  const anchorNode = () => {
    return (
      <Anchor
        className={styles.anchorNode}
        items={workConfigData?.anchorData}
      />
    );
  };

  // 左侧操作按钮
  const segmentedNode = () => {
    const options = workConfigData?.segmentedData?.map((item) => {
      return {
        label: (
          <div
            className={styles.segmentedNode}
            onClick={() => {
              if (item.value !== "C") return;
              setOpen(true);
              form.setFieldValue(
                "prvBlockcode",
                dataSource.infoForm2?.blockcode
              );
            }}
          >
            <Tooltip title={item.label.title}>
              <Avatar style={{ backgroundColor: item.label.backgroundColor }}>
                {item.label.text}
              </Avatar>
            </Tooltip>
          </div>
        ),
        value: item.value,
      };
    });
    return (
      <Segmented<string>
        vertical
        size="small"
        className={styles.segmented}
        options={options}
        onChange={(value) => {}}
      />
    );
  };

  return (
    <>
      <div className="app-block m-b">
        <RouterBar />
      </div>
      <Card className="app-container" style={{ paddingLeft: "36px" }}>
        {anchorNode?.()}
        {segmentedNode?.()}
        {open && (
          <CommonModal
            type="CALL_BLOCKCODE_MODAL"
            open={open}
            content={() => content()}
            onClose={() => setOpen(false)}
            onSubmit={handleSubmit}
          />
        )}
        {Array.isArray(workConfigData?.callFields) &&
          workConfigData?.callFields.map((item, index) => {
            const { key, type, columns: col, props } = item;
            const columns = Array.isArray(col) ? col : [];
            switch (type) {
              // 表格
              case COMPONENT_TYPE.TABLE:
                return (
                  <CommonTable
                    key={`${index}-${key}-table`}
                    columns={columns}
                    dataSource={dataSource[key]}
                    intlPrefix="callParam"
                    paginationConfig={false}
                  />
                );
              // 表单
              case COMPONENT_TYPE.FORM: {
                const tempColumns = columns.map((item) => ({
                  ...item,
                  ellipsis: true,
                  editable: item?.editable ? null : false,
                }));
                return (
                  <ProDescriptions
                    key={`${index}-${key}-form`}
                    id={key}
                    dataSource={dataSource[key]}
                    columns={tempColumns}
                    className="p-b"
                    {...props}
                  />
                );
              }
              default:
                return null;
            }
          })}
      </Card>
    </>
  );
};

export default memo(CallBench);
