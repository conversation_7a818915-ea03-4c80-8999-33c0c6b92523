// 物料层/规则详情页面
import {
  FC,
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  memo,
} from "react";
import _ from "lodash";
import useIntlCustom from "@/hooks/useIntlCustom";
import { getRuleFacByRuleType } from "@/services/rules";
import common from "@/services/common";
import { FormMaintenance } from "@/components";
import {
  I18N_COMON_PAGENAME,
  NOTIFICATION_TYPE,
  OPERATE_TYPE,
} from "@/constants/publicConstant";
import { RULE_TYPE, RULE_OPTION_TYPE } from "@/constants/rulesConstant";
import {
  IDetailData,
  IDetailProps,
  IOpExpsField,
  IRuleFac,
  IRuleResultFieldItem,
} from "./types/IRules";
import useDetailInfoForm from "./hooks/detail/useDetailInfoForm";
import useDetailOpEpxs from "./hooks/detail/useDetailExpression";
import useDetailResultForm from "./hooks/detail/useDetailResultForm";
import useRuleUtil from "./hooks/useRuleUtil";

const Detail: FC<IDetailProps> = forwardRef(
  (
    {
      type = OPERATE_TYPE.detail,
      data = {},
      ruleType = RULE_TYPE.limit,
      urlObj,
    },
    ref = null
  ) => {
    // hook变量
    const { openNotificationTip } = useIntlCustom();
    const { setListByRuleFac } = useRuleUtil();
    const [opRuleFacTypeList, setOpRuleFacTypeList] = useState<Array<IRuleFac>>(
      []
    );
    const [judgeRuleFacTypeList, setJudgeRuleFacTypeList] = useState<
      Array<IRuleFac>
    >([]);
    const [resultRuleFacTypeList, setResultRuleFacTypeList] = useState<object>(
      {}
    );
    const [opExps, setOpExps] = useState<string>("");
    const [ruleCondField, setRuleCondField] = useState<object>({});
    const [ruleResultField, setRuleResultField] = useState<Array<any>>([]);
    const [ruleOptionList, setRuleOptionList] = useState<Array<string>>([]);
    const canEdit = type !== OPERATE_TYPE.detail;
    // 副作用
    useEffect(() => {
      // 新增进入不查询
      if (type === OPERATE_TYPE.create) {
        return;
      }
      let {
        ruleType,
        opExpsField = { opExps: "" },
        ruleCondField,
        ruleResultField,
        ruleOptionList,
      } = data as IDetailData;
      ruleType && getRuleFacType("node");
      // 解析运算表达式
      const { opExps } = opExpsField as IOpExpsField;
      setOpExps(opExps);
      setRuleCondField(ruleCondField);
      setRuleResultField(ruleResultField as IRuleResultFieldItem[]);
      setRuleOptionList(ruleOptionList);
    }, [data]);
    // 暴露给父组件的方法
    useImperativeHandle(ref, () => {
      return {
        async onSubmit() {
          try {
            // 获取基本信息、运算表达式、条件表达式、结果信息数据
            const { formData } = await ruleEditFormRef?.current?.onSubmit();
            let opExpsField = await opExpsGraphRef?.current?.onSubmit();
            let ruleCondField = await ruleCondGraphRef?.current?.onSubmit();
            let { editTableData = [] } =
              (await ruleResultFormRef?.current?.onSubmit()) || {};
            if (formData) {
              const { ruleOptionList = [] } = formData;
              if (
                (ruleOptionList.includes(RULE_OPTION_TYPE.opExps) &&
                  !opExpsField) ||
                (ruleOptionList.includes(RULE_OPTION_TYPE.judgeExps) &&
                  !ruleCondField) ||
                (ruleOptionList.includes(RULE_OPTION_TYPE.ruleResultField) &&
                  !editTableData.length)
              ) {
                throw Error("checkMsg");
              }
              // 赋默认值，不能传空
              opExpsField = opExpsField || " ";
              ruleCondField = ruleCondField || " ";
              // 接口
              delete data.ruleOptionList;
              delete formData.ruleOptionList;
              let ruleResultField = editTableData.map(
                ({ ruleFac, ruleResult }) => ({ [ruleFac]: ruleResult })
              );
              ruleResultField = JSON.stringify(ruleResultField);
              const postData = {
                ...data,
                ...formData,
                opExpsField,
                ruleCondField,
                ruleResultField,
                // execType: "0",
                // ruleSts: "1",
              };
              // 修改/新增接口
              const res = await common.getEditPost({
                data: postData,
                url: type === OPERATE_TYPE.edit ? urlObj.EDIT : urlObj.CREATE,
                type,
              });
              // const res = await common.getEditPostBiz({
              //   ...postData,
              //   url: type === OPERATE_TYPE.edit ? urlObj.EDIT : urlObj.CREATE,
              //   type,
              // });
              if (res) {
                openNotificationTip(
                  I18N_COMON_PAGENAME.COMMON,
                  NOTIFICATION_TYPE.SUCCESS,
                  "editSuccess",
                  1
                );
                return true;
              }
            }
            return null;
          } catch (error) {
            console.error(error);
            openNotificationTip(
              I18N_COMON_PAGENAME.COMMON,
              NOTIFICATION_TYPE.ERROR,
              "checkMsg"
            );
            return null;
          }
        },
      };
    });
    // 获取规则因子数据
    const getRuleFacType = async (ruleType: string): Promise<void> => {
      try {
        const res = await getRuleFacByRuleType({ ruleType });
        const {
          opRuleFactTypeList,
          judgeRuleFactTypeList,
          resultRuleFactTypeList,
        } = setListByRuleFac(res);
        setOpRuleFacTypeList(opRuleFactTypeList);
        setJudgeRuleFacTypeList(judgeRuleFactTypeList);
        setResultRuleFacTypeList(resultRuleFactTypeList);
      } catch (error) {
        console.error(error);
      }
    };
    // 事件处理
    // 表格值改变事件
    const handleFormChange = (resObj): void => {
      for (const key in resObj) {
        // 规则配置类型
        if (key === "ruleOptionList") {
          setRuleOptionList(resObj[key]);
        } else if (key === "ruleType") {
          // 根据规则类型查询规则因子
          getRuleFacType(resObj[key]);
        }
      }
    };

    // 基本信息组件hooks
    const { ruleEditFormRef, renderInfoForm } = useDetailInfoForm({
      type,
      ruleType,
      canEdit,
      data,
      handleFormChange,
    });
    // 运算表达式和条件表达式hooks
    const { opExpsGraphRef, ruleCondGraphRef, renderOpExps, renderJudgeExps } =
      useDetailOpEpxs({
        canEdit,
        ruleType,
        opExps,
        ruleCondField,
        opRuleFacTypeList,
        judgeRuleFacTypeList,
      });
    // 结果信息组件hooks
    const { ruleResultFormRef, renderResultForm } = useDetailResultForm({
      canEdit,
      data,
      ruleResultField,
      resultRuleFacTypeList,
    });

    return (
      <>
        {renderInfoForm()}
        {ruleOptionList.includes(RULE_OPTION_TYPE.opExps) && renderOpExps()}
        {ruleOptionList.includes(RULE_OPTION_TYPE.judgeExps) &&
          renderJudgeExps()}
        {ruleOptionList.includes(RULE_OPTION_TYPE.ruleResultField) &&
          renderResultForm()}
        {!canEdit && (
          <FormMaintenance data={data as IDetailData} showVersion={false} />
        )}
      </>
    );
  }
);

export default memo(Detail);
