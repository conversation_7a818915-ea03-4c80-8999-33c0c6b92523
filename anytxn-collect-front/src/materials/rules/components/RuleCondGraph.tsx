// 规则引擎-条件表达式
import { FC, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import _ from 'lodash';
import { notification } from 'antd';
import useIntlCustom from '@/hooks/useIntlCustom';
import { RulesGraph } from '@/components';
import { RULE_TYPE } from '@/constants/rulesConstant';
import { I18N_COMON_PAGENAME, NOTIFICATION_TYPE } from '@/constants/publicConstant';
import { getEventNodeData } from '../util/rulesUtil';
import {
  getMenuItems,
  getRuleCondGraphData,
  getRuleCondNodeData,
  getValueMenuList,
  setNodeSize,
  setRuleCondData,
} from '../util/condExpsUtil';
import useRuleUtil from '../hooks/useRuleUtil';
import { prefix } from '../pageConfig';
import { INodeData, IRuleCondGraphProps } from '../types/IRules';
import useDetailIntl from '../hooks/detail/useDetailIntl';

const RuleCondGraph: FC<IRuleCondGraphProps> = forwardRef(
  ({ ruleType = RULE_TYPE.limit, ruleCondField, ruleFacTypeList, canEdit }, ref = null) => {
    // 变量
    const { translate, openNotificationTip } = useIntlCustom();
    const { getErrorDesc } = useRuleUtil();
    const { inputPlaceholder, inputMultiplePlaceHolder, selectPlaceholder, opMenuItems, booleanMenuItems } =
      useDetailIntl();
    const [graph, setGraph] = useState<any>(null);
    const [data, setData] = useState({});

    // 副作用
    useEffect(() => {
      if (graph) {
        getGraphData();
      }
    }, [graph, ruleCondField, ruleFacTypeList]);
    // 暴露给父组件的方法
    useImperativeHandle(ref, () => {
      return {
        onSubmit() {
          const res = setRuleCondData(graph);
          if (res.errorType) {
            openNotificationTip(prefix, NOTIFICATION_TYPE.ERROR, res.errorType);
            return null;
          } else {
            return JSON.stringify(res);
          }
        },
      };
    });

    // 逻辑处理
    // 转换数据为画布数据并渲染
    const getGraphData = (): void => {
      const graphData = getRuleCondGraphData(ruleCondField, {
        ruleFacTypeList,
        inputPlaceholder,
        inputMultiplePlaceHolder,
        selectPlaceholder,
        opMenuItems,
        booleanMenuItems,
      });
      setData(graphData);
      if (graph) {
        graph.setData(graphData);
        graph.render();
      }
    };
    // 获取画布实体
    const afterMounted = (g: any): void => {
      setGraph(g);
    };

    // 菜单点击事件
    const handleMenuClcik = (graph, e, data): void => {
      const { id, childrenNum, data: parentData } = data;
      const { nodeDim } = parentData;
      const nodeResult = getEventNodeData(graph, e, data);
      if (nodeResult) {
        const { opNode, nodeId, nodeNo } = nodeResult as INodeData;
        // 新增节点
        graph.updateNodeData([{ id, childrenNum: childrenNum + 1 }]);
        let nodeData = getRuleCondNodeData(opNode, nodeId, nodeNo, nodeDim + 1);
        graph.addChildrenData(id, [nodeData]);
        graph.render();
      }
    };
    // 更新画布数据
    const handleValueChange = _.debounce((graph, obj, data): void => {
      graph.updateNodeData([{ id: data.id, data: { ...data.data, ...obj } }]);
    }, 200);
    // 条件表达式相应错误提示
    const handleError = (errorList): void => {
      notification[NOTIFICATION_TYPE.ERROR]({
        message: translate(I18N_COMON_PAGENAME.COMMON, 'tip'),
        duration: 3,
        description: getErrorDesc(errorList),
      });
    };
    // 传递给节点的参数
    const nodeProps = {
      canEdit,
      getValueMenuList,
      onMenuClick: handleMenuClcik,
      getMenuItems: (nodeData) => getMenuItems(nodeData, ruleType),
      onError: handleError,
    };

    return (
      <RulesGraph
        id="RuleCondGraph"
        data={data}
        nodeProps={nodeProps}
        afterMounted={afterMounted}
        setSize={setNodeSize}
        onValueChange={handleValueChange}
      />
    );
  },
);

export default RuleCondGraph;
