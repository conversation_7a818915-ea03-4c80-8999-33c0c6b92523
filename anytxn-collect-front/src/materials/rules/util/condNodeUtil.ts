import { RULE_ERROR_TYPE, RULE_FAC_LIST, RULE_FAC_OP_TYPE } from '@/constants/rulesConstant';

/**
 * 设置节点数据值类型
 */
export const NODE_DATA_ENUM = {
  opType: 'opType',
  isOpExps: 'isOpExps',
  comValue: 'comValue',
  tempResult: 'tempResult',
  opMenuList: 'opMenuList',
};

/**
 * 根据规则因子类型筛选出符号枚举
 * @param valueType 规则因子类型
 * @returns {array}
 */
export const getOpListByValueType = (valueType) => {
  let opTyleList: any[];
  switch (valueType) {
    // 字符型
    case RULE_FAC_LIST.STRING:
      opTyleList = [
        RULE_FAC_OP_TYPE.EQ,
        RULE_FAC_OP_TYPE.EQIGNORECASE,
        RULE_FAC_OP_TYPE.NEQ,
        RULE_FAC_OP_TYPE.IN,
        RULE_FAC_OP_TYPE.NOTIN,
        RULE_FAC_OP_TYPE.CUTEQ,
        RULE_FAC_OP_TYPE.CUTNOTEQ,
        RULE_FAC_OP_TYPE.CUTIN,
        RULE_FAC_OP_TYPE.HASVAL,
        RULE_FAC_OP_TYPE.NOHASVAL,
        RULE_FAC_OP_TYPE.NULL,
        RULE_FAC_OP_TYPE.NONULL,
        RULE_FAC_OP_TYPE.BETWEEN,
        RULE_FAC_OP_TYPE.LIKE,
        RULE_FAC_OP_TYPE.NOLIKE,
        RULE_FAC_OP_TYPE.STARTSWITH,
        RULE_FAC_OP_TYPE.NOTSTARTSWITH,
        RULE_FAC_OP_TYPE.ENDSWITH,
        RULE_FAC_OP_TYPE.NOTEMPTY,
      ];
      break;
    //
    case RULE_FAC_LIST.DICTIONARY:
    // 参数型
    case RULE_FAC_LIST.PARAMETER:
      opTyleList = [
        RULE_FAC_OP_TYPE.EQ,
        RULE_FAC_OP_TYPE.EQIGNORECASE,
        RULE_FAC_OP_TYPE.NEQ,
        RULE_FAC_OP_TYPE.IN,
        RULE_FAC_OP_TYPE.NOTIN,
        RULE_FAC_OP_TYPE.HASVAL,
        RULE_FAC_OP_TYPE.NOHASVAL,
        RULE_FAC_OP_TYPE.NULL,
        RULE_FAC_OP_TYPE.NONULL,
        RULE_FAC_OP_TYPE.LIKE,
        RULE_FAC_OP_TYPE.NOLIKE,
        RULE_FAC_OP_TYPE.STARTSWITH,
        RULE_FAC_OP_TYPE.NOTSTARTSWITH,
        RULE_FAC_OP_TYPE.ENDSWITH,
        RULE_FAC_OP_TYPE.NOTEMPTY,
      ];
      break;
    // 数组型
    case RULE_FAC_LIST.ARRAY:
      opTyleList = [RULE_FAC_OP_TYPE.INLIST, RULE_FAC_OP_TYPE.NOTINLIST];
      break;
    default:
      // 整数型、长整型、浮点型、金额型
      opTyleList = [
        RULE_FAC_OP_TYPE.EQ,
        RULE_FAC_OP_TYPE.GT,
        RULE_FAC_OP_TYPE.LT,
        RULE_FAC_OP_TYPE.NEQ,
        RULE_FAC_OP_TYPE.GE,
        RULE_FAC_OP_TYPE.LE,
        RULE_FAC_OP_TYPE.IN,
        RULE_FAC_OP_TYPE.NOTIN,
        RULE_FAC_OP_TYPE.DIVIDE,
        RULE_FAC_OP_TYPE.NOTDIVIDE,
      ];
      break;
  }
  return opTyleList;
};

/**
 * 根据运算符返回能否输入或选择多个值
 * @param operateType 运算符
 * @returns {number}
 */
export const getValueRuleByOpType = (operateType) => {
  if (
    [
      RULE_FAC_OP_TYPE.EQ,
      RULE_FAC_OP_TYPE.GT,
      RULE_FAC_OP_TYPE.LT,
      RULE_FAC_OP_TYPE.NEQ,
      RULE_FAC_OP_TYPE.GE,
      RULE_FAC_OP_TYPE.LE,
    ].includes(operateType)
  ) {
    return 1;
  } else if (operateType === RULE_FAC_OP_TYPE.BETWEEN) {
    return 2;
  } else {
    return 3;
  }
};

// 判断表达式中以下运算符不展示值
export const unShowValueComList = [
  RULE_FAC_OP_TYPE.HASVAL,
  RULE_FAC_OP_TYPE.NOHASVAL,
  RULE_FAC_OP_TYPE.NULL,
  RULE_FAC_OP_TYPE.NONULL,
  RULE_FAC_OP_TYPE.NOTEMPTY,
];

/**
 * 规则因子值校验
 * @param res 规则因子值与其配置
 * @returns {array}
 */
export const validateValue = (res) => {
  const {
    value,
    valueType,
    wordSegBid,
    ruleFacValueType,
    ruleFacMax: max,
    ruleFacMin: min,
    ruleFacFloat,
    operateType,
  } = res;
  // 以下运算符不用输入值
  if (unShowValueComList.includes(operateType)) {
    return [];
  }
  const errorList: any = [];
  // 值是否为空
  if (!value && !wordSegBid) {
    errorList.push({ type: RULE_ERROR_TYPE.valueNull });
  }
  switch (ruleFacValueType) {
    // 字符串
    case RULE_FAC_LIST.STRING:
      {
        const charResult = value.split(',');
        // 该运算符下可输入的字符数量
        const maxNum = getValueRuleByOpType(operateType);
        let res = true;
        // 只能输入一个字符
        if (maxNum === 1) {
          res = charResult.length === 1;
          res = res && value.length <= max && value.length >= min;
        } else if (maxNum === 2) {
          // 只能输入两个值
          res = charResult.length === 2;
          charResult.forEach((char) => {
            if (res) {
              res = char.length <= max && char.length >= min;
            }
          });
        } else {
          // 多个
          // 至少输入两个值
          // res = charResult.length >= 2;
          charResult.forEach((char) => {
            if (res) {
              res = char.length <= max && char.length >= min;
            }
          });
        }
        if (!res) {
          errorList.push({ type: ruleFacValueType, min, max, maxNum });
        }
      }
      break;
    // 整形/长整形
    case RULE_FAC_LIST.INTEGER:
    case RULE_FAC_LIST.LONGINTEGER:
      {
        const maxNum = getValueRuleByOpType(operateType);
        const numberList = value.split(',');
        if (numberList.length > maxNum) {
          errorList.push({ type: ruleFacValueType, min, max, maxNum });
          break;
        }
        for (const item of numberList) {
          const regExp = /^[0-9]+$/;
          let res = regExp.test(item);
          if (res) {
            res = item >= min && item <= max;
          }
          if (!res) {
            errorList.push({ type: ruleFacValueType, min, max, maxNum });
            break;
          }
        }
      }
      break;
    // 浮点型
    case RULE_FAC_LIST.FLOAT:
    case RULE_FAC_LIST.LONGINTEGER:
      {
        const maxNum = getValueRuleByOpType(operateType);
        const numberList = value.split(',');
        if (numberList > maxNum) {
          errorList.push({ type: valueType, min, max, maxNum });
          return;
        }
        for (const item of numberList) {
          const regExp = new RegExp(`/^\\d+(\\.\\d{1,${ruleFacFloat}})?$/`);
          let res = regExp.test(item);
          if (res) {
            res = item >= min && item <= max;
          }
          if (!res) {
            errorList.push({ type: valueType, min, max, maxNum, float: ruleFacFloat });
            break;
          }
        }
      }
      break;

    default:
      break;
  }

  return errorList;
};
