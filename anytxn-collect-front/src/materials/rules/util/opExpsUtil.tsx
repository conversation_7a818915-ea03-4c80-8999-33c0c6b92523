// 运算表达式util
import { GraphData, treeToGraphData } from '@antv/g6';
import { parse } from 'mathjs';
import { FormattedMessage } from 'react-intl';
import { COMPONENT_TYPE } from '@/constants/publicConstant';
import { MATH_NODE_TYPE, OPEXP_TYPE, OPEXPS_MENU_TYPE, RULE_ERROR_TYPE, RULE_TYPE } from '@/constants/rulesConstant';
import { TDropDownMenuItem } from '@/types/TCommon';
import { prefix } from '../pageConfig';
import { getSelection } from './rulesUtil';
import { IRuleFac, IGraphNodeData } from '../types/IRules';
import { IOpExpsFuncData, IOpExpsNumData } from '../types/IOpExps';

// 规则因子配置
const ruleFacConfig = {
  type: COMPONENT_TYPE.SELECT,
  valueMenuItems: [] as TDropDownMenuItem[],
  showSearch: true,
  style: {
    wapperStyle: {
      minWidth: '18rem',
      width: 'fit-content',
      justifyContent: 'flex-start',
    },
    selectStyle: {
      minWidth: '100%',
    },
  },
};

// 运算表达式配置
const opExpsConfig = {
  type: COMPONENT_TYPE.SELECT,
  valueMenuItems: getSelection(OPEXP_TYPE),
};

// 数值配置
const numConfig = {
  type: COMPONENT_TYPE.INPUT_NUMBER,
};

// 获取菜单子项
export const getMenuItems = (nodeData, ruleType: string): TDropDownMenuItem[] => {
  const {
    data: { type },
    isRoot,
  } = nodeData;
  const res: TDropDownMenuItem[] = [];
  // 函数类型
  if (type && type === OPEXP_TYPE[type]) {
    // 额度有规则因子，授权没有
    if (ruleType === RULE_TYPE.limit) {
      res.push({ key: OPEXPS_MENU_TYPE.ruleFacField, label: <FormattedMessage id={`${prefix}.createRuleFac`} /> });
    }
    res.push(
      { key: OPEXPS_MENU_TYPE.number, label: <FormattedMessage id={`${prefix}.createNumber`} /> },
      { key: OPEXPS_MENU_TYPE.function, label: <FormattedMessage id={`${prefix}.createFunction`} /> },
    );
  }
  // 除了根节点
  if (!isRoot) {
    res.push({ key: OPEXPS_MENU_TYPE.delete, label: <FormattedMessage id={`${prefix}.deleteNode`} /> });
  }
  return res;
};

/**
 * 处理运算表达式不同节点类型的数据
 * @param opNode mathjs的数据
 * @param nodeId id
 * @returns {object}
 */
export const getOpExpsData = (
  opNode,
  nodeId: string,
  nodeNo: string,
): IGraphNodeData<IOpExpsFuncData | IOpExpsNumData> => {
  const { type, name, value, isNumber } = opNode;
  let id = '';
  if ([MATH_NODE_TYPE.OperatorNode, MATH_NODE_TYPE.FunctionNode].includes(type)) {
    // SUM/MIN/MAX
    const type = name ? OPEXP_TYPE[name] : OPEXP_TYPE.SUM;
    id = `${MATH_NODE_TYPE.OperatorNode}-${nodeId}`;
    return { id, data: { value: type, type, nodeNo }, config: opExpsConfig, childrenNum: 0 };
  } else if (type === MATH_NODE_TYPE.ConstantNode) {
    // 常量
    // 判断是数值还是规则因子
    let config = {};
    if (value || value === 0) {
      const numReg = /^(\d+)$/;
      config = numReg.test(value) ? numConfig : ruleFacConfig;
    } else {
      config = isNumber ? numConfig : ruleFacConfig;
    }
    id = `${type}-${nodeId}`;
    return { id, data: { value, nodeNo }, config, childrenNum: 0 };
  } else {
    // 规则因子有时候会识别成SymbolNode
    id = `${MATH_NODE_TYPE.ConstantNode}-${nodeId}`;
    return { id, data: { value: name, nodeNo }, config: ruleFacConfig, childrenNum: 0 };
  }
};

/**
 * 根据mathjs的数据转换成G6的treeToGraphData需要的格式
 * @param opExpsNode mathjs的数据
 * @param nodeId 节点id
 * @param parentId 父级id
 * @returns {object}
 */
const getTreeData = (opExpsNode, nodeId = 1, parentNodeData: any = {}) => {
  const { id: parentId = '', data: parentData = {} } = parentNodeData;
  const { nodeNo: parentNodeNo } = parentData;
  const id = parentId ? `${parentId}-${nodeId + 1}` : `${nodeId}`;
  let nodeNo;
  if (parentId) {
    nodeNo = parentNodeNo ? `${parentNodeNo}.${nodeId + 1}` : nodeId + 1;
  }
  const nodeData = getOpExpsData(opExpsNode, id, nodeNo);
  const treeData = { ...nodeData, children: [], isRoot: Boolean(!parentId), childrenNum: 0 };
  const { args } = opExpsNode;
  if (args) {
    treeData.children as any;
    treeData.children = args.map((nodeItem, index) => getTreeData(nodeItem, index, nodeData));
    treeData.childrenNum = treeData.children.length; // 用于添加子节点时展示编号
  }
  return treeData;
};

/**
 * 根据运算表达式渲染画布
 * @param opExps 运算表达式
 * @returns {object}
 */
export const getOpExpsGraphData = (opExps: string, ruleFacTypeList: IRuleFac[]): GraphData => {
  ruleFacConfig.valueMenuItems = ruleFacTypeList.map((item: IRuleFac) => ({
    key: item.ruleFacEnName,
    value: item.ruleFacEnName,
    label: item.ruleFacCnName,
  }));
  let treeData;
  if (opExps) {
    const opExpsNode = parse(opExps);
    treeData = getTreeData(opExpsNode);
  } else {
    // 赋初始值
    const opExpsNode = {
      type: MATH_NODE_TYPE.FunctionNode,
      name: OPEXP_TYPE.MIN,
    };
    treeData = getTreeData(opExpsNode);
  }

  return treeToGraphData(treeData);
};

/**
 * 根据父节点id获取后代节点表达式
 * @param graph 画布实体
 * @param nodeId 父节点id
 * @returns {string}
 */
const getGraphDataToOpExps = (graph, nodeData, isCheck = false): string | object => {
  const {
    id,
    data: { value, nodeNo },
  } = nodeData;
  let result = '';
  const childRes: Array<string> = [];
  const children = graph.getChildrenData(id);
  if (children) {
    for (const childNode of children) {
      const res = getGraphDataToOpExps(graph, childNode, isCheck);
      if (typeof res === 'object') {
        return res;
      } else {
        childRes.push(res);
      }
    }
  }

  // 校验函数必须有两个子节点
  if (isCheck) {
    if (value === OPEXP_TYPE[value]) {
      if (childRes.length < 2) {
        return {
          type: RULE_ERROR_TYPE.opExpsFuncNotMeet,
          nodeNo,
        };
      }
    } else if (value !== 0 && !value) {
      // 校验是否有值
      return {
        type: RULE_ERROR_TYPE.opExpsValueNull,
        nodeNo,
      };
    }
  }

  if (value === OPEXP_TYPE.SUM) {
    result = childRes.join('+');
  } else if (value === OPEXP_TYPE[value]) {
    result = `${value}(${childRes.join(', ')})`;
  } else {
    // 数值/规则因子
    result = value;
  }
  return result;
};

/**
 * 将画布数据转换为运算表达式
 * @param graph 画布实体
 * @returns {string}
 */
export const setOpExpsData = (graph, isCheck = false): string | object => {
  const { nodes } = graph.getData();
  let rootNodeData;
  for (const nodeData of nodes) {
    const { isRoot } = nodeData;
    // 找出根元素
    if (isRoot) {
      rootNodeData = nodeData;
      break;
    }
  }
  const res = getGraphDataToOpExps(graph, rootNodeData, isCheck);
  return res;
};
