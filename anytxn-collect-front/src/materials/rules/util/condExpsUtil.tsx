// 条件表达式util
import { GraphData, treeToGraphData } from "@antv/g6";
import { FormattedMessage } from "react-intl";
import { COMPONENT_TYPE } from "@/constants/publicConstant";
import {
  COND_MENU_TYPE,
  RULE_COND_MENU_TYPE,
  RULE_COND_NODE_LIST,
  RULE_COND_NODE_TYPE,
  RULE_ERROR_TYPE,
  RULE_FAC_OP_TYPE,
  RULE_GROUP_TYPE,
  RULE_NUMBER_OPEXPS_LIST,
  RULE_TYPE,
} from "@/constants/rulesConstant";
import { TDropDownMenuItem } from "@/types/TCommon";
import { getDictByType } from "@/services/rules";
import RulesCondNode from "../components/RulesCondNode";
import { getSelection, generateRandomString } from "./rulesUtil";
import { prefix } from "../pageConfig";
import { ICondNodeData } from "../types/ICondExps";
import { isEmpty } from "@/utils/mathUtil";

// 条件组合配置
export const ruleCondGroupConfg = {
  type: COMPONENT_TYPE.SELECT,
  valueMenuItems: getSelection(RULE_GROUP_TYPE, true),
  valueKey: "stsfCondJudge",
  style: {
    wapperStyle: {
      width: "fit-content",
      justifyContent: "flex-start",
    },
    selectStyle: {
      minWidth: "10rem",
    },
  },
};

// 条件配置（因为G6的ReactNode在App外层，所以需要国际化都要这样传进去
export let ruleCondConfig = {
  // 规则因子枚举值
  ruleFacTypeList: [],
  // 运算符枚举
  opMenuItems: [],
  // 布尔值枚举
  booleanMenuItems: [],
  // 单个的文本框提示语
  inputPlaceholder: "",
  // 多个的文本框提示语
  inputMultiplePlaceHolder: "",
  // 多选框提示语
  selectPlaceholder: "",
  style: {
    wapperStyle: {
      minWidth: "18rem",
      width: "fit-content",
      justifyContent: "flex-start",
    },
  },
};

// 获取菜单子项
export const getMenuItems = (
  nodeData,
  ruleType: string
): TDropDownMenuItem[] => {
  const {
    data: { type },
    isRoot,
    children,
  } = nodeData;
  const res: TDropDownMenuItem[] = [];
  // 条件组合
  if (type === RULE_COND_NODE_TYPE.ConditionGroup) {
    // 额度只能有一个条件
    if (!(ruleType === RULE_TYPE.limit && children.length > 0)) {
      res.push({
        key: COND_MENU_TYPE.addCond,
        label: <FormattedMessage id={`${prefix}.createCondition`} />,
      });
    }
    // 额度不能加第二层的条件组合
    if (ruleType === RULE_TYPE.auth) {
      res.push({
        key: COND_MENU_TYPE.addCondGroup,
        label: <FormattedMessage id={`${prefix}.createConditionGroup`} />,
      });
    }
  }
  // 除了根节点
  if (!isRoot) {
    res.push({
      key: COND_MENU_TYPE.delete,
      label: <FormattedMessage id={`${prefix}.deleteNode`} />,
    });
  }
  return res;
};

// 获取条件值菜单子项
export const getValueMenuList = async (type: string, data): Promise<object> => {
  if (type === RULE_COND_MENU_TYPE.opExps) {
    return RULE_NUMBER_OPEXPS_LIST;
  } else {
    return await getDictByType(data);
  }
};
// 设置节点宽高
export const setNodeSize = (d: any): number[] => {
  const { type } = d?.data;
  return type === RULE_COND_NODE_TYPE.ConditionGroup ? [240, 48] : [400, 48];
};

/**
 * 转换成treeToGraphData能解析的数据
 * @param nodeData 节点数据
 * @returns {object}
 */
const getRuleCondTreeData = (nodeData) => {
  let { nodeId, subNode = [], nodeType, value, stsfCondJudge } = nodeData || {};
  const children: Array<any> = [];
  if (subNode.length) {
    subNode.forEach((node) => {
      const childTreeData = getRuleCondTreeData(node);
      children.push(childTreeData);
    });
  }
  let type = "";
  for (const key in RULE_COND_NODE_LIST) {
    if (
      Object.prototype.hasOwnProperty.call(RULE_COND_NODE_LIST, key) &&
      RULE_COND_NODE_LIST[key] === nodeType
    ) {
      type = key;
      break;
    }
  }
  let config = {};
  let rightChildren: any = null;
  if (type === RULE_COND_NODE_TYPE.ConditionGroup) {
    config = ruleCondGroupConfg;
    value = stsfCondJudge;
  } else {
    rightChildren = <RulesCondNode />;
    config = ruleCondConfig;
  }
  const treeData = {
    id: String(nodeId),
    children,
    data: { ...nodeData, type, value },
    config,
    rightChildren,
    childrenNum: subNode.length,
  };
  return treeData;
};

/**
 * 将判断表达式树形结构转为graph
 * @param ruleCondField 判断表达式
 * @returns {object} {nodes: [], edges: []}
 */
export const getRuleCondGraphData = (
  ruleCondField: object,
  ruleCondMenuItems: object
): GraphData => {
  ruleCondConfig = { ...ruleCondConfig, ...ruleCondMenuItems };
  let treeData;
  if (isEmpty(ruleCondField)) {
    // 赋初始值
    const ruleData = {
      type: RULE_COND_NODE_TYPE.ConditionGroup,
      nodeDim: 1,
      nodeId: Date.now(),
      nodeNo: "1",
      nodeType: RULE_COND_NODE_LIST.ConditionGroup,
      config: ruleCondGroupConfg,
      stsfCondJudge: RULE_GROUP_TYPE.ALL,
    };
    treeData = getRuleCondTreeData(ruleData);
  } else {
    treeData = getRuleCondTreeData(ruleCondField);
  }
  return treeToGraphData({ ...treeData, isRoot: true });
};

/**
 * 获取判断表达式不同类型节点的数据
 * @param type 类型
 * @param nodeId 节点id
 * @returns {object}
 */
export const getRuleCondNodeData = (nodeData, nodeId, nodeNo, nodeDim) => {
  const { type, value } = nodeData;
  let id = "";
  let data = {
    value,
    nodeId,
    nodeNo,
    nodeDim,
  };
  if (type === COND_MENU_TYPE.addCondGroup) {
    // 条件组合
    id = `${RULE_COND_NODE_TYPE.ConditionGroup}-${nodeId}`;
    return {
      id,
      data: {
        ...data,
        type: RULE_COND_NODE_TYPE.ConditionGroup,
        nodeType: RULE_COND_NODE_LIST.ConditionGroup,
      },
      config: ruleCondGroupConfg,
      childrenNum: 0,
    };
  } else {
    // 条件
    id = `${RULE_COND_NODE_TYPE.Condition}-${nodeId}`;
    return {
      id,
      data: {
        ...data,
        type: RULE_COND_NODE_TYPE.Condition,
        nodeType: RULE_COND_NODE_LIST.Condition,
      },
      config: ruleCondConfig,
      rightChildren: <RulesCondNode />,
      childrenNum: 0,
    };
  }
};

// 根据运算符返回格式
const getCondValueByOpType = (
  opType: string,
  value: string | string[]
): string[] => {
  if (Array.isArray(value)) {
    return value;
  }
  if (
    [
      RULE_FAC_OP_TYPE.EQ,
      RULE_FAC_OP_TYPE.GT,
      RULE_FAC_OP_TYPE.LT,
      RULE_FAC_OP_TYPE.NEQ,
      RULE_FAC_OP_TYPE.GE,
      RULE_FAC_OP_TYPE.LE,
    ].includes(opType)
  ) {
    return [value];
  } else {
    return value.split(",");
  }
};

/**
 * 根据节点数据拼出后端需要的数据
 * @param ruleNode 节点数据
 * @returns {object}
 */
const getRuleCondParam = (ruleNode: ICondNodeData) => {
  const {
    nodeId,
    nodeType,
    nodeDim,
    nodeNo,
    stsfCondJudge,
    operateType,
    opName,
    valueType,
    value,
    fieldBid,
    fieldName,
    nodeCtx,
    valueWorldSegType,
    wordSegBid,
  } = ruleNode;
  // 条件组合
  if (nodeType === RULE_COND_NODE_LIST.ConditionGroup) {
    return { nodeId, nodeDim, nodeNo, nodeType, stsfCondJudge };
  } else {
    // 条件：各种类型
    let valueObj = {};
    if (wordSegBid) {
      valueObj = { valueWorldSegType, wordSegBid };
    } else {
      let otherParam = value
        ? { value: getCondValueByOpType(operateType, value) }
        : {};
      valueObj = { valueType, ...otherParam };
    }
    return {
      nodeId: generateRandomString(9),
      nodeDim,
      nodeNo,
      nodeType,
      operateType,
      opName,
      fieldBid,
      fieldName,
      nodeCtx,
      ...valueObj,
    };
  }
};

/**
 * 设置条件表达式子节点数据
 * @param graph 画布实例
 * @param parentNodeId 父节点id
 * @returns {object}
 */
const setRuleCondChildren = (graph, parentNodeId) => {
  const childNodes: Array<any> = [];
  const children = graph.getChildrenData(parentNodeId);
  if (children && children.length) {
    for (const { id, data } of children) {
      let nodeData: any = getRuleCondParam(data);
      if (nodeData.nodeType === RULE_COND_NODE_LIST.ConditionGroup) {
        const res: any = setRuleCondChildren(graph, id);
        if (res.errorType) {
          return res;
        }
        if (res?.length) {
          nodeData = { ...nodeData, subNode: res };
        } else {
          // 条件组合没有子节点
          return { errorType: RULE_ERROR_TYPE.judgeCondGroupNotMeet };
        }
      } else if (data.editing) {
        // 正在编辑状态
        return { errorType: RULE_ERROR_TYPE.judgeCondEditing };
      }
      childNodes.push(getRuleCondParam(data));
    }
  } else {
    return { errorType: RULE_ERROR_TYPE.judgeCondGroupNotMeet };
  }
  return childNodes;
};

/**
 * 将画布数据转为后端数据结构
 * @param graph 画布实体
 * @returns {object}
 */
export const setRuleCondData = (graph): any => {
  const { nodes } = graph.getData();
  let rootNodeData;
  let rootNodeId = "";
  for (const { isRoot, id, data } of nodes) {
    // 找出根元素
    if (isRoot) {
      rootNodeData = { ...data, nodeId: generateRandomString(9) };
      rootNodeId = id;
      break;
    }
  }
  const nodeData = getRuleCondParam(rootNodeData);
  const res = setRuleCondChildren(graph, rootNodeId);
  return res.errorType ? res : { ...nodeData, subNode: res };
};
