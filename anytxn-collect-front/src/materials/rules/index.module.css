.graphWrapper {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  /* overflow: hidden; */
  .opExpsGraph {
    width: calc(100% - 62rem);
  }
}
.opExpsWrapper {
  min-width: 62rem;
}
.opExpress {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 12rem;
  border-left: 2px solid var(--gray-three);
  .textWrapper {
    flex: 1;
    padding: 0.5rem;
    padding-left: 1.5rem;
    white-space: normal;
    overflow-wrap: break-word;
    word-break: break-word;
  }
}
.updateBtn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.condWrapper {
  width: max-content;
  height: 100%;
  border-radius: 5px;
  .dropDownButton {
    border-width: 0;
    box-shadow: 'none';
  }
  .ruleFacInput {
    min-width: 16rem;
  }
  .ruleFacSelect {
    min-width: 10rem;
  }
  .ruleFacButton {
    border-radius: 10%;
    border:  2px solid var(--gray-three);
    padding: 0.5rem;
    color: var(--color-main);
  }
  .resultWrapper {
    width: 100%;
    max-width: 280px;
    height: 100%;
    position: relative;
    .activeEditingText{
      position: absolute;
      z-index: 10;
    }
    .inactiveEditingText {
      position: absolute;
      opacity: 0;
    }
    .resultHoverText {
      opacity: 0;
    }
    .resultText {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}
.hoverText {
  color: var(--gray-light);
  background-color: var(--gray-six);
  opacity: 0.8;
}
