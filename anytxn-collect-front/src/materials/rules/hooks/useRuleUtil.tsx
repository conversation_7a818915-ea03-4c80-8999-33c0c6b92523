// 规则需要国际化的util
import { ReactNode } from 'react';
import useIntlCustom from '@/hooks/useIntlCustom';
import { RULE_ERROR_TYPE, RULE_FAC_LIST, RULE_OPTION_LIST } from '@/constants/rulesConstant';
import { prefix } from '../pageConfig';
import { IRuleFac } from '../types/IRules';

const useRuleUtil = () => {
  const { translate } = useIntlCustom();

  /**
   * 设置规则因子可配置的规则类型
   * @param ruleFacList 规则因子数据
   * @returns {object}
   */
  const setListByRuleFac = (ruleFacList) => {
    const opRuleFactTypeList: IRuleFac[] = [];
    const judgeRuleFactTypeList: IRuleFac[] = [];
    const tempResultRuleFactTypeList: IRuleFac[] = [];
    // 筛选出规则因子可配置的规则类型
    ruleFacList.forEach((item) => {
      if (Array.isArray(item.ruleExecuteType)) {
        let {
          name: ruleFacCnName,
          key: ruleFacEnName,
          valueType: ruleFacValueType,
          minLength: ruleFacMin,
          maxLength: ruleFacMax,
          digits: ruleFacFloat,
        } = item;
        // 拼接展示的规则因子名称
        ruleFacCnName = `${ruleFacCnName} (${translate(prefix, ruleFacValueType)})`;
        const tempItem: IRuleFac = {
          ruleFacCnName,
          ruleFacEnName,
          ruleFacValueType,
          ruleFacMin,
          ruleFacMax,
          ruleFacFloat,
        };

        // 运算表达式可配置的规则因子
        // item.ruleExecuteType.includes(RULE_OPTION_LIST.opExps) && opRuleFactTypeList.push(tempItem);
        // // 条件表达式可配置的规则因子
        // item.ruleExecuteType.includes(RULE_OPTION_LIST.judgeExps) && judgeRuleFactTypeList.push(tempItem);
        // // 结果信息可配置的规则因子
        // item.ruleExecuteType.includes(RULE_OPTION_LIST.ruleResultField) && tempResultRuleFactTypeList.push(tempItem);
        // 运算表达式可配置的规则因子
          opRuleFactTypeList.push(tempItem);
        // 条件表达式可配置的规则因子
          judgeRuleFactTypeList.push(tempItem);
        // 结果信息可配置的规则因子
          tempResultRuleFactTypeList.push(tempItem);
      
      }
    });
    let resultRuleFactTypeList = {};
    // 结果信息的规则因子数据转换格式
    tempResultRuleFactTypeList.forEach((item) => {
      const { ruleFacEnName, ruleFacCnName } = item;
      resultRuleFactTypeList[ruleFacEnName] = { ...item, text: ruleFacCnName };
    });
    return {
      opRuleFactTypeList,
      judgeRuleFactTypeList,
      resultRuleFactTypeList,
    };
  };

  /**
   * 获取错误提示JSX
   */
  const getErrorDesc = (errorList): ReactNode => {
    let description: ReactNode[] = [];
    errorList.forEach((errorObj) => {
      const { type, max, min, float = 0 } = errorObj;
      if (type === RULE_ERROR_TYPE.valueNull) {
        description.push(
          <p key={RULE_ERROR_TYPE.valueNull} className="m-b-s">
            {translate(prefix, type)}
          </p>,
        );
      } else if (
        [RULE_FAC_LIST.INTEGER, RULE_FAC_LIST.STRING, RULE_FAC_LIST.AMOUNT, RULE_FAC_LIST.FLOAT].includes(type)
      ) {
        // 整型、字符型
        description.push(
          <p key={`${type}-allowOnly`} className="m-b-s">
            {translate(prefix, 'allowOnly')}:
          </p>,
        ); // 限制输入
        description.push(
          <p key={`${type}-maxLength`} className="m-b-s">
            {translate(prefix, 'maxLength')}: {max}
          </p>,
        ); // 最大值
        description.push(
          <p key={`${type}-minLength`} className="m-b-s">
            {translate(prefix, 'minLength')}: {min}
          </p>,
        ); // 最小值
        if ([RULE_FAC_LIST.AMOUNT, RULE_FAC_LIST.FLOAT].includes(type)) {
          description.push(
            <p key={`${type}-digits`} className="m-b-s">
              {translate(prefix, 'digits')}: {float}
            </p>,
          ); // 浮点数
        }
      }
    });
    return <div>{description}</div>;
  };

  return { setListByRuleFac, getErrorDesc };
};

export default useRuleUtil;
