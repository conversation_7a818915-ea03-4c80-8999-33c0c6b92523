// 详情/运算表达式和条件表达式hooks
import { ReactNode, useRef } from 'react';
import { Collapse } from 'antd';
import { FormHearder } from '@/components';
import useIntlCustom from '@/hooks/useIntlCustom';
import { prefix } from '../../pageConfig';
import OpExpsGraph from '../../components/OpExpsGraph';
import styles from '../../index.module.css';
import RuleCondGraph from '../../components/RuleCondGraph';

// 折叠面板样式
const collaspStyle = { body: { height: '15rem', padding: '0' } };

const useDetailExpression = ({ canEdit, ruleType, opExps, ruleCondField, opRuleFacTypeList, judgeRuleFacTypeList }) => {
  // hooks变量
  const opExpsGraphRef = useRef<any>();
  const ruleCondGraphRef = useRef<any>();
  const { translate } = useIntlCustom();
  // 渲染运算表达式组件
  const renderOpExps = (): ReactNode => {
    return (
      <div className={`p-r ${styles.opExpsWrapper}`}>
        <FormHearder title={translate(prefix, 'OPERATOR')} />
        <Collapse
          defaultActiveKey="1"
          items={[
            {
              key: '1',
              label: 'opExps',
              children: (
                <OpExpsGraph
                  ref={opExpsGraphRef}
                  ruleType={ruleType}
                  opExps={opExps}
                  ruleFacTypeList={opRuleFacTypeList}
                  canEdit={canEdit}
                />
              ),
              styles: collaspStyle,
            },
          ]}
          size="small"
        />
      </div>
    );
  };
  // 渲染条件表达式组件
  const renderJudgeExps = (): ReactNode => {
    return (
      <div className="p-r">
        <FormHearder title={translate(prefix, 'JUDGE')} />
        <Collapse
          defaultActiveKey="1"
          items={[
            {
              key: '1',
              label: 'judgeExpression',
              children: (
                <RuleCondGraph
                  ref={ruleCondGraphRef}
                  ruleType={ruleType}
                  ruleCondField={ruleCondField}
                  ruleFacTypeList={judgeRuleFacTypeList}
                  canEdit={canEdit}
                />
              ),
              styles: collaspStyle,
            },
          ]}
          size="small"
        />
      </div>
    );
  };
  return { opExpsGraphRef, ruleCondGraphRef, renderOpExps, renderJudgeExps };
};

export default useDetailExpression;
