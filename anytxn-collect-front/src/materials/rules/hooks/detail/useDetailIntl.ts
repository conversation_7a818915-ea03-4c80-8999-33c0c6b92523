// 用于传递给自定义节点的国际化hooks
import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import { prefix } from '../../pageConfig';
import { RULE_BOOLEAN_TYPE, RULE_FAC_OP_TYPE } from '@/constants/rulesConstant';
import { getSelection } from '../../util/rulesUtil';

const useDetailIntl = () => {
  // hooks变量
  const { translate, getSelectOption } = useIntlCustom();
  // 只允许输入单个的文本框提示语
  const inputPlaceholder = translate(I18N_COMON_PAGENAME.COMMON, 'inputPlaceholder');
  // 允许输入多个的文本框提示语
  const inputMultiplePlaceHolder = translate(prefix, 'inputMultiplePlaceHolder');
  // 多选框提示语
  const selectPlaceholder = translate(I18N_COMON_PAGENAME.COMMON, 'selectPlaceholder');
  // 条件表达式的运算符枚举
  const opMenuItems = getSelectOption(getSelection(RULE_FAC_OP_TYPE), false, prefix);
  // 条件表达式的布尔值枚举
  const booleanMenuItems = getSelectOption(getSelection(RULE_BOOLEAN_TYPE), false, prefix);

  return {
    inputPlaceholder,
    inputMultiplePlaceHolder,
    selectPlaceholder,
    opMenuItems,
    booleanMenuItems,
  };
};

export default useDetailIntl;
