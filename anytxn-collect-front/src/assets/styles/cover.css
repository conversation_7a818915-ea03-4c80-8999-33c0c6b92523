/* 覆盖antd样式 */

/* 覆盖不生效时候字体显示太浅 */
.ant-input-outlined.ant-input-disabled,
.ant-input-number .ant-input-number-input,
.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector,
.ant-select-disabled.ant-select-multiple .ant-select-selection-overflow .ant-select-selection-item,
.ant-picker .ant-picker-input > input[disabled] {
  color: rgb(0 0 0.4);
}
/* 覆盖 list action 样式 */
.ant-list-item-action {
  margin-left: 0;
}
/* 覆写卡片 */
.ant-card-body {
  padding: var(--margin-padding);
}
/* 覆盖 card action 样式 */
.ant-card-actions {
  background: var(--gray-light);
}
/* 覆盖表格宽高 */
.ant-table-thead > tr > th,
.ant-table-tbody > tr > td,
.ant-table tfoot > tr > th,
.ant-table tfoot > tr > td {
  padding: 0.6rem 0.5rem;
}
/* 覆盖 modal 默认高度 */
.ant-modal-wrap .ant-modal-body .ant-card-body {
  height: 24rem;
}
/* 覆盖：用于主题切换 菜单深色主题展开 背景色 */
.ant-menu-dark.ant-menu-inline .ant-menu-sub.ant-menu-inline {
  background: var(--color-main);
  .ant-menu-item-selected {
    /* background-color: linear-gradient(-35deg, var(--color-six)); */
    background-color: var(--color-three);
  }
  .ant-menu-item-active {
    background-color: var(--color-two);
  }
}
/* 覆盖：用于主题切换 button 背景色 */
.ant-btn-color-dangerous.ant-btn-variant-link {
  color: var(--warning-color);
}
.ant-btn-primary:hover {
  background-color: var(--color-three);
}
/* 覆盖 table 滚动条样式 */
.ant-table-body, .ant-table-content {
  scrollbar-width: auto;
  scrollbar-color: auto;
  /* 为滚动区域添加自定义样式 */
  &::-webkit-scrollbar {
    width: 4px; /* 设置滚动条的宽度 */
    height: 4px; /* 设置滚动条的高度 */
    border-radius: 2px;
    background-color: var(--gray-main);
  }

  /* 为滚动条轨道自定义样式 */
  &::-webkit-scrollbar-track {
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0);
    background-color: #fff;
  }

  /* 为滚动条滑块自定义样式 */
  &::-webkit-scrollbar-thumb {
    border-radius: 2px;
    -webkit-box-shadow: inset 0 0 4px rgba(0, 0, 0, 0);
    background-color: var(--gray-three);
  }
}
/* 覆写 sider 背景色 */
.ant-layout-sider {
  margin: var(--margin-padding) 0;
  padding: 0.75rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  box-shadow: 0 0.15rem 0.3rem rgba(0, 0, 0, 0.06), 0 0 0.45rem rgba(0, 0, 0, 0.03);
  .ant-menu-dark {
    background: var(--gray-one);
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        color: var(--gray-six);
      }
    }
    .ant-menu-item {
      color: var(--gray-six);
    }
    .ant-menu-inline {
      background: var(--gray-one);
      color: var(--gray-six);
    }
    .ant-menu-item-selected {
      background-color: var(--color-two);
    }
    .ant-menu-item-active {
      background-color: var(--color-one) !important;
      color: var(--gray-six) !important;
    }
  }
  .ant-menu-dark.ant-menu-inline .ant-menu-sub.ant-menu-inline {
    background: var(--gray-one);
    .ant-menu-item-selected {
      background-color: var(--color-two);
    }
    .ant-menu-item-active {
      background-color: var(--color-one);
      color: var(--gray-six);
    }
  }
}
/* 收缩菜单时hover的样式 */
.ant-menu-submenu-popup {
  .ant-menu-vertical {
    background-color: var(--gray-one) !important;
    .ant-menu-submenu {
      .ant-menu-submenu-title {
        color: var(--gray-six);
      }
    }
    .ant-menu-item {
      color: var(--gray-six);
    }
    .ant-menu-item-selected {
      background-color: var(--color-two);
      color: var(--gray-six);
    }
    .ant-menu-item-active {
      background-color: var(--color-one) !important;
      color: var(--gray-six) !important;
    }
  }
}
