import { defineAppConfig } from 'ice';
import { defineStoreConfig } from '@ice/plugin-store/types';

// App config, see https://v3.ice.work/docs/guide/basic/app
export default defineAppConfig(() => ({}));

export const storeConfig = defineStoreConfig(async (appData) => {
  // const { userInfo = {} } = appData;
  return {
    initialStates: {
      user: {
        currentUser: {},
      },
    },
  };
});
