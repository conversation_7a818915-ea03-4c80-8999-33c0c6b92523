// 操作手册等组件
import { FC } from 'react';
import { Button, Dropdown } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import useIntlCustom from '@/hooks/useIntlCustom';
import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import { TDropDownMenuItem } from '@/types/TCommon';
import util from '../util';

const HelpBar: FC = () => {
  // hook变量
  const { translate } = useIntlCustom();

  const items: TDropDownMenuItem[] = [
    {
      key: '1',
      label: translate(I18N_COMON_PAGENAME.COMMON, 'operationManual'),
    },
    {
      key: '2',
      label: translate(I18N_COMON_PAGENAME.COMMON, 'guide'),
    },
    {
      key: '3',
      label: translate(I18N_COMON_PAGENAME.COMMON, 'updateLog'),
    },
  ];
  const onClick = () => {
    console.log('需要什么帮助呢');
  };
  return (
    <Dropdown menu={{ items, onClick }}>
      <Button type="primary" shape="circle" icon={<QuestionCircleOutlined />} style={util.iconStyle} />
    </Dropdown>
  );
};

export default HelpBar;
