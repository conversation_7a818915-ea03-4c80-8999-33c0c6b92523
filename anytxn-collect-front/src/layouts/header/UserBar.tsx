/**
 * 用户信息组件
 */

import { FC } from "react";
import { UserOutlined, ImportOutlined } from "@ant-design/icons";
import { Popover, Avatar, Button } from "antd";
import store from "@/store";
import useIntlCustom from "@/hooks/useIntlCustom";
import { intlConst } from "@/hooks/useIntlCustom";
import styleConstant from "@/constants/styleConstant";
import styles from "../index.module.css";
import { I18N_COMON_PAGENAME } from "@/constants/publicConstant";
import avatar from "@/assets/images/avatar.png";

const UserBar: FC = () => {
  const { translate } = useIntlCustom();
  const [userState] = store.useModel("user");
  const userInfo = userState.currentUser;
  const getContent = () => {
    const {
      userName = intlConst.formatMessage("callParam", "defaultUserName"),
      empName = intlConst.formatMessage("callParam", "defaultEmpName"),
      branchName = intlConst.formatMessage("callParam", "defaultBranchName"),
    } = userInfo;

    return (
      <div style={{ width: 250 }}>
        <div className={styles.popUser}>
          <span style={styleConstant.mr}>
            {translate(I18N_COMON_PAGENAME.COMMON, "userName")}：{userName}
          </span>
        </div>
        <div className={styles.popUser}>
          {translate(I18N_COMON_PAGENAME.COMMON, "branchName")}：{branchName}
        </div>
        <div className={styles.popUser}>
          {translate(I18N_COMON_PAGENAME.COMMON, "empName")}：{empName}
        </div>
        {/* <div className={styles.popLogout}>
          <Button icon={<ImportOutlined />} onClick={handleExit}>
            {translate(I18N_COMON_PAGENAME.COMMON, 'logout')}
          </Button>
        </div> */}
      </div>
    );
  };
  const handleExit = async () => {
    sessionStorage.clear();
    window.location.href = "/$";
  };
  return (
    <div className={styles.user}>
      <Popover placement="bottomRight" content={getContent()}>
        <Avatar
          src={avatar}
          size={36}
          icon={<UserOutlined />}
          className={styles.avatar}
        />
      </Popover>
    </div>
  );
};

export default UserBar;
