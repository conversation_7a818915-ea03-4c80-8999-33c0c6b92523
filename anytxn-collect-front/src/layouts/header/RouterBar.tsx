/**
 * 路由路径组件
 */

import { useState, useEffect, FC } from 'react';
import { Breadcrumb } from 'antd';
import { useLocation } from 'ice'; // react-router-dom
import { TSysMenuItem } from '@/types/TCommon';
import store from '@/store';
import util from '../util';

type menuSelectedType = {
  key: string;
  title: string | undefined;
};
const RouterBar: FC = () => {
  const [items, setItems] = useState<Array<menuSelectedType>>([]);
  const [userState] = store.useModel('user');
  const location = useLocation();
  const menuData: TSysMenuItem[] = userState.menuData || [];

  useEffect(() => {
    const menuSelected: Array<menuSelectedType> = [];
    const matchData = util.getMenuByRoute(menuData, location.pathname);
    // 权限是否已初始化完成
    if (matchData && userState.initDone) {
      const { menuId, parentMenuId, menuName } = matchData;
      // 赋值路由中文名字显示
      let parentMenuList: TSysMenuItem[] = util.getRecordMenuList(menuData, parentMenuId, 1);
      parentMenuList.forEach((item) => menuSelected.push({ key: item.menuId, title: item.menuName }));
      menuSelected.push({ key: menuId, title: menuName });
      setItems(menuSelected);
    }
  }, [location.pathname, userState.initDone]);

  return (
    <div className="flex-row flex-align-center">
      <div className="theme-color-main form-title-prefix" />
      <Breadcrumb items={items} className="m-tb" />
    </div>
  );
};

export default RouterBar;
