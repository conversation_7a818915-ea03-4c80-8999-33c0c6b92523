/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import { Layout } from "antd";
import LogoBar from "./LogoBar";
import Topbar from "./Topbar";
import HelpBar from "./HelpBar";
import LanguageBar from "./LanguageBar";
import ThemeBar from "./ThemeBar";
import UserBar from "./UserBar";
import styles from "../index.module.css";

export default function Header({ onSelect = (codeIndex: string): {} => ({}) }) {
  return (
    <Layout.Header className={styles.header}>
      <LogoBar />
      <Topbar onSelect={onSelect} />
      <div className={`${styles.rightbar} m-r-s`}>
        <HelpBar />
        <LanguageBar />
        <ThemeBar />
        <UserBar />
      </div>
    </Layout.Header>
  );
}
