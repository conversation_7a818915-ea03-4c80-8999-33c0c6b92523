// 切换语言组件
import { FC, useEffect, useState } from 'react';
import { Dropdown, Button } from 'antd';
import type { MenuProps } from 'antd';
import { TranslationOutlined } from '@ant-design/icons';
import { LOCAL, LANGUAGE_LIST } from '@/constants/publicConstant';
import * as dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/zh-tw';
import 'dayjs/locale/en';
import util from '../util';
import { TDropDownMenuItem } from '@/types/TCommon';

const LanguageBar: FC = () => {
  const [items, setItems] = useState<TDropDownMenuItem[]>([]);
  const sessionLocale = localStorage.getItem(LOCAL.LOCALE);

  useEffect(() => {
    const temp: TDropDownMenuItem[] = [];
    for (const key in LANGUAGE_LIST) {
      const { label, locale } = LANGUAGE_LIST[key];
      temp.push({ key: locale, label, disabled: sessionLocale === locale });
    }
    setItems(temp);
  }, []);

  // 切换语言
  const onClick: MenuProps['onClick'] = ({ key: locale }) => {
    localStorage.setItem(LOCAL.LOCALE, locale);
    if (locale) {
      dayjs.locale(locale);
    } else {
      dayjs.locale(LANGUAGE_LIST.CN.label);
    }
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  return (
    <Dropdown menu={{ items, onClick }}>
      <Button type="primary" shape="circle" icon={<TranslationOutlined />} style={util.iconStyle} />
    </Dropdown>
  );
};

export default LanguageBar;
