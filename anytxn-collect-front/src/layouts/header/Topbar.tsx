/**
 * 一级菜单组件
 */

import { FC, useEffect, useState } from 'react';
import { Link } from 'ice';
import { Menu } from 'antd';
import store from '@/store';
import { TSysMenuItem } from '@/types/TCommon';
import { SESSION, MENUTYPE_KEY, MENU_THEMEN } from '@/constants/publicConstant';
import styles from '../index.module.css';

interface ITopbarProps {
  onSelect: (codeIndex) => {};
}

const Topbar: FC<ITopbarProps> = ({ onSelect = (codeIndex) => {} }) => {
  const menuCodeIndex = sessionStorage.getItem(SESSION.codeIndex);
  const [userState] = store.useModel('user');
  const [headerSelectedKeys, setHeaderSelectedKeys] = useState<Array<string>>([]);
  const menuData: TSysMenuItem[] = userState.menuData || [];

  // 设置默认菜单
  useEffect(() => {
    menuCodeIndex && setHeaderSelectedKeys([menuCodeIndex]);
  }, [menuCodeIndex]);

  // 处理数据
  const getMenuData = (): any[] => {
    const headerMenuData: any[] = [];
    menuData.forEach((item) => {
      const headItem = JSON.parse(JSON.stringify(item));
      if (item.level && item.level == 1) {
        // 赋值组件属性
        headItem.key = item.menuId;
        headItem.disabled = item.type === (MENUTYPE_KEY.link || MENUTYPE_KEY.blank) && !item.menuRouteUrl;
        if (item.menuRouteUrl) {
          headItem.label = <Link to={item.menuRouteUrl}>{item.menuName}</Link>;
        } else {
          headItem.label = item.menuName;
        }
        // 删除多余属性
        const deleteList = [
          'menuId',
          'parentMenuId',
          'iconId',
          'level',
          'menuName',
          'menuRouteUrl',
          'menuStatus',
          'menuType',
          'permissionId',
          'type',
          'createUser',
          'createTime',
          'updateTime',
          'updateUser',
          'orderSeq',
        ];
        deleteList.forEach((arr) => delete headItem?.[arr]);
        if (item.children && item.children.length > 0) {
          delete headItem.children;
        }
        headerMenuData.push(headItem);
      }
    });
    return headerMenuData;
  };

  const handleHeaderSelect = (e) => {
    setHeaderSelectedKeys(e.selectedKeys);
    onSelect(e.key);
  };

  return (
    <div className={styles.topbar}>
      <Menu
        theme={MENU_THEMEN}
        mode="horizontal"
        items={getMenuData()}
        selectedKeys={headerSelectedKeys}
        className="theme-color-main"
        style={{ display: 'flex', height: '100%', marginBottom: 1 }}
        onSelect={handleHeaderSelect}
      />
    </div>
  );
};

export default Topbar;
