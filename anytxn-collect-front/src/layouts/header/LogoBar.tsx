/**
 * logo组件
 */

import { FC } from 'react';
import { Link } from 'ice'; // react-router-dom
import logo from '@/assets/images/logo.png';
import useIntlCustom from '@/hooks/useIntlCustom';
import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import styles from '../index.module.css';

const LogoBar: FC = () => {
  const { translate } = useIntlCustom();
  return (
    <Link className={styles.logo} to="workbench">
      <img alt="logo" src={logo} style={{ height: 36, width: 36, marginRight: 12 }} />
      <span>{translate(I18N_COMON_PAGENAME.COMMON, 'bankName')}</span>
    </Link>
  );
};

export default LogoBar;
