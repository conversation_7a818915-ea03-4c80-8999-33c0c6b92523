/**
 * 切换主题组件
 */
import { FC } from 'react';
import { Button, Dropdown } from 'antd';
import { SkinOutlined } from '@ant-design/icons';
import { themeColor } from '@/constants/styleConstant';
import { LOCAL } from '@/constants/publicConstant';
import { TDropDownMenuItem } from '@/types/TCommon';
import util from '../util';
import styles from '../index.module.css';

const ThemeBar: FC = () => {
  // 内部变量
  const items: TDropDownMenuItem[] = [];
  for (const key in themeColor) {
    items.push({
      key: themeColor[key],
      label: (
        <div className="flex-row flex-align-center">
          <div className={styles.themeBlock} style={{ background: themeColor[key] }} />
          {key}
        </div>
      ),
    });
  }

  // 切换主题
  const onClick = (e) => {
    localStorage.setItem(LOCAL.THEME, e.key);
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };
  return (
    <Dropdown menu={{ items, onClick }}>
      <Button type="primary" shape="circle" icon={<SkinOutlined />} style={util.iconStyle} />
    </Dropdown>
  );
};

export default ThemeBar;
