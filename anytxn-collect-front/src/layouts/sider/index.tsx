/**
 * 侧边栏组件
 */

import { useState, useEffect } from "react";
import { Layout, Menu, Input, ConfigProvider, Dropdown } from "antd";
import { DoubleRightOutlined, DoubleLeftOutlined } from "@ant-design/icons";
import { Link, useLocation } from "ice"; // react-router-dom
import _ from "lodash";
import {
  SESSION,
  MENUTYPE_KEY,
  MENU_THEMEN,
  I18N_COMON_PAGENAME,
} from "@/constants/publicConstant";
import { TDropDownMenuItem, TSysMenuItem } from "@/types/TCommon";
import store from "@/store";
// import IconMap from './IconMap'; // eslint 需要关闭 forceConsistentCasingInFileNames 才不报错，否则只能通过@来引用
import util from "../util";
import styles from "../index.module.css";
// todo 动态而优雅的导入
import * as iconList from "@ant-design/icons"; // 数据太多，请原谅我这么不优雅的导入
import styleConstant from "@/constants/styleConstant";
import useIntlCustom from "@/hooks/useIntlCustom";

export default function Sider() {
  const [items, setItems] = useState<Array<any>>([]); // TMenuItem 传到组件类型校验通过大可以编译
  const [openKeys, setOpenKeys] = useState<Array<string>>([]);
  const [selectedKeys, setSelectedKeys] = useState<Array<string>>([]);
  const [collapsed, setCollapsed] = useState(false);
  const [linkMenuList, setLinkMenuList] = useState<TDropDownMenuItem[]>([]);
  const [menuItems, setMenuItems] = useState<TDropDownMenuItem[]>([]);
  const [userState] = store.useModel("user");
  const location = useLocation();
  const { translate } = useIntlCustom();
  const menuData: TSysMenuItem[] = userState.menuData || [];
  const menuTree: TSysMenuItem[] = userState.menuTree || [];
  const codeIndex = sessionStorage.getItem(SESSION.codeIndex);

  useEffect(() => {
    // 是否已初始化菜单完成，且已跳转至第一个菜单的页面，且菜单展开
    if (
      !(userState.initDone && codeIndex) ||
      location.pathname === "/" ||
      collapsed
    ) {
      return;
    }
    const newTreeByIndex = menuTree.filter((item) => item.menuId == codeIndex);
    const childrenData: any =
      newTreeByIndex && newTreeByIndex.length > 0 && newTreeByIndex[0].children
        ? newTreeByIndex[0].children
        : [];
    // 初始化路由菜单和打开、选择子项
    if (childrenData && childrenData.length > 0) {
      // recursionMenuTree(childrenData);
      // 侧边栏菜单数据整理
      const sideMenuData = JSON.parse(JSON.stringify(childrenData));
      recursionMenuTree(sideMenuData);
      // 设置菜单数据
      setItems(sideMenuData);
      setLinkMenuList(util.getLinkMenuList(sideMenuData));
      // 判断是否切换一级菜单，并做对应处理
      const matchData = userState.menuData.filter(
        (item) => item.menuRouteUrl === location.pathname
      )[0];
      if (matchData) {
        // 获取二级路由开始的路径
        const parentMenuList: TSysMenuItem[] = matchData.parentMenuId
          ? util.getRecordMenuList(menuData, matchData.parentMenuId)
          : [];
        if (parentMenuList.length) {
          setOpenKeys(
            parentMenuList.map((item: TSysMenuItem) => String(item.menuId))
          );
        } else {
          setOpenKeys([String(matchData.menuId)]);
        }
        setSelectedKeys([String(matchData.menuId)]);
      }
    }
  }, [userState.initDone, codeIndex, location.pathname, collapsed]);

  const recursionMenuTree = (menuTree) => {
    menuTree.forEach((item) => {
      const {
        menuId,
        parentMenuId,
        menuName,
        menuRouteUrl,
        iconId,
        children,
        type,
      } = item;
      // 赋值组件属性
      item.key = menuId;
      item.disabled =
        type === (MENUTYPE_KEY.link || MENUTYPE_KEY.blank) && !menuRouteUrl;
      if (menuRouteUrl) {
        item.label = <Link to={menuRouteUrl}>{menuName}</Link>;
      } else {
        item.label = menuName;
      }
      if (iconId && iconId.length > 0) {
        // item.icon = IconMap[iconId];
        item.icon = iconList[iconId].render();
      } else {
        // 默认图标
        const str = "SettingOutlined";
        item.icon = iconList[str].render();
      }
      // 保留menuRouteUrl数据
      item.routeurl = menuRouteUrl;
      // 删除多余属性
      const deleteList = [
        "menuId",
        "parentMenuId",
        "iconId",
        "level",
        "menuName",
        "menuRouteUrl",
        "menuStatus",
        "menuType",
        "permissionId",
        "createUser",
        "createTime",
        "updateTime",
        "updateUser",
        "orderSeq",
      ];
      deleteList.forEach((arr) => delete item?.[arr]);
      if (children && children.length > 0) {
        recursionMenuTree(children);
      } else {
        delete item.children;
      }
    });
  };
  const handleCollapsed = () => setCollapsed(!collapsed);
  const handleOpenChange = (e) => setOpenKeys(e);
  const handleSelect = (e) => setSelectedKeys(e.selectedKeys);

  // 菜单搜索
  const handleSearch = (value: string): void => {
    // 清空输入
    if (!value) {
      setMenuItems([]);
      return;
    }
    // 根据输入的菜单名称筛选
    const result = linkMenuList.filter((item) => {
      let menuName = "";
      if (typeof item.label === "string") {
        menuName = item.label;
      } else {
        menuName = _.get(item, "label.props.children", "");
      }
      return menuName.indexOf(value) > -1;
    });
    setMenuItems(result);
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Layout: {
            siderBg: styleConstant.grayOne,
          },
        },
      }}
    >
      <Layout.Sider
        width={240}
        trigger={null}
        collapsible
        collapsed={collapsed}
      >
        <div
          className={`sider-collapsed-button ${
            collapsed ? styles.iconCollapsedIn : styles.iconCollapsedOut
          }`}
          onClick={handleCollapsed}
        >
          {collapsed ? (
            <DoubleRightOutlined className={styles.iconColor} />
          ) : (
            <DoubleLeftOutlined className={styles.iconColor} />
          )}
        </div>
        <div className="height100 flex-col">
          {collapsed ? null : (
            <Dropdown menu={{ items: menuItems }}>
              <Input.Search
                placeholder={translate(
                  I18N_COMON_PAGENAME.COMMON,
                  "menuSearchPlaceHolder"
                )}
                className={styles.searchMenu}
                onSearch={handleSearch}
              />
            </Dropdown>
          )}
          <Menu
            theme={MENU_THEMEN}
            mode="inline"
            openKeys={openKeys}
            selectedKeys={selectedKeys}
            items={items}
            className="theme-color-main"
            style={{ borderRight: 0, overflowY: "auto", overflowX: "hidden" }}
            onOpenChange={handleOpenChange}
            onSelect={handleSelect}
          />
        </div>
      </Layout.Sider>
    </ConfigProvider>
  );
}
