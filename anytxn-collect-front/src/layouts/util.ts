import { TDropDownMenuItem, TSysMenuItem } from '@/types/TCommon';
import { isEmpty } from '@/utils/mathUtil';

/**
 * 根据路由获取菜单数据
 * @param menuData
 * @param pathname
 */
const getMenuByRoute = (menuData: Array<TSysMenuItem> = [], pathname: string) => {
  // const url = pathname === '/' ? '/workbench/dashboard' : pathname;
  const url = pathname === '/' ? '' : pathname;
  let match: TSysMenuItem | any = null;
  menuData.forEach((item) => {
    // 有些菜单路由没有在最前面添加'/'
    if (item.menuRouteUrl === url || `/${item.menuRouteUrl}` === url) {
      match = item;
    }
  });
  return match;
};

/**
 * 获取路由父级数据
 * @param menuData
 * @param parentCode
 */
const getParentMenu = (menuData: Array<TSysMenuItem> = ([] = []), parentCode: string) => {
  let parentMenu: TSysMenuItem | any = null;
  menuData.forEach((item) => {
    if (parentCode === item.menuId) {
      parentMenu = item;
    }
  });
  return parentMenu;
};

/**
 * 获取路由从二级菜单开始的路由
 */
const getRecordMenuList = (menuData: Array<TSysMenuItem>, parentCode: string, topLevel: number = 2): TSysMenuItem[] => {
  const record: TSysMenuItem[] = [];
  const parentMenu = getParentMenu(menuData, parentCode);
  if (!isEmpty(parentMenu)) {
    // 一级路由不需要
    if (parentMenu.level > topLevel) {
      const ancestorMenu = getParentMenu(menuData, parentMenu.parentMenuId);
      ancestorMenu.menuId && record.push(ancestorMenu);
    }
    record.push(parentMenu);
  }
  return record;
};

/**
 * 找到底层菜单
 */
const getLinkMenuList = (menuData: any[]): TDropDownMenuItem[] => {
  const result: any[] = [];
  menuData.forEach((item) => {
    const tempRes = { ...item };
    if (Array.isArray(item.children)) {
      result.push(...getLinkMenuList(item.children));
    } else if (item.type === 'link') {
      delete tempRes.icon;
      result.push(tempRes);
    }
  });
  return result;
};

export default {
  iconStyle: { margin: '0 .5rem', cursor: 'pointer' },
  getMenuByRoute,
  getParentMenu,
  getRecordMenuList,
  getLinkMenuList,
};
