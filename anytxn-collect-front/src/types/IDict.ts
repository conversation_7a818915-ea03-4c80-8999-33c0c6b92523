import { IUrlItem } from "./ICommon";

/**
 * 字典/参数表字段store约束接口
 */
export interface IState {
  dictMap: any;
}

/**
 * 请求数据方法传参约束接口
 */
export interface IDictParam {
  /**
   * 接口
   */
  url: IUrlItem;
  /**
   * 存储在dictMap的key值
   */
  key: string;
  /**
   * 查询接口传参
   */
  param: object;
  /**
   * select下拉框的key字段
   */
  optionKey: string;
  /**
   * select下拉框的value字段
   */
  optionValue: string;
  /**
   * 下拉框是否展示key
   */
  showKey?: boolean;
  /**
   * 请求枚举类型
   */
  serviceType?: string;
}
