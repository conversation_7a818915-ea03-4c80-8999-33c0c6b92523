// 表格约束

import { ReactNode } from 'react';

/**
 * 表格columns约束接口
 */
export interface IColumns {
  key: string;
  /**
   * 数据路径
   */
  dataIndex: string | string[];
  /**
   * 列表名称
   */
  title: string;
  /**
   * 对齐方式
   */
  align?: string | 'center' | 'left' | 'right';
  /**
   * 宽度
   */
  width?: number;
  /**
   * 需要格式化的数据类型，RENDER_TYPE
   */
  valueType?: string;
  /**
   * select下拉框等数据
   */
  data?: Array<any>;
  /**
   * 参数或字典从store的dict里拿的dictMap的key值
   */
  dictType?: string;
  /**
   * 当前列表自定义国际化前缀
   */
  prefix?: string;
  /**
   * select下拉框是否展示key
   */
  showKey?: boolean;
  /**
   * 数据渲染函数
   */
  render?: (_, data) => any;
}

/**
 * 列表公共字段
 */
export interface IPublicColumn {
  /**
   * 版本号
   */
  version: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 创建人
   */
  createUser: string;
  /**
   * 更新人
   */
  updateUser: string;
}

/**
 * 列表操作列约束接口
 */
export interface ICommonTableActionItem {
  /**
   * 类型
   */
  type: string;
  /**
   * 按钮提示文本
   */
  title: string;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 另外配置的图标
   */
  icon?: ReactNode;
}
