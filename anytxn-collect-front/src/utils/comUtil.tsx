/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import { ReactNode } from "react";
import dayjs from "dayjs";
import _ from "lodash";
import { FormattedMessage } from "react-intl";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popconfirm } from "antd";
import {
  EyeOutlined,
  CopyOutlined,
  FormOutlined,
  DeleteOutlined,
  CloseOutlined,
  SaveOutlined,
  SearchOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ApartmentOutlined,
  SendOutlined,
  DownloadOutlined,
  ClusterOutlined,
} from "@ant-design/icons";
import store from "@/store";
import * as publicConstant from "@/constants/publicConstant";
import { IDictList } from "@/types/ICommon";
import { LANGUAGE_LIST } from "@/constants/publicConstant";
import { formatNumber } from "./mathUtil";

const theme = localStorage.getItem(publicConstant.LOCAL.THEME) || "";
/**
 * 获取表格操列宽度
 * @param count 操作按钮数量
 * @returns {number}
 */
export function getActionColumnWidth(count: number = 1): number {
  let width = 100;
  switch (count) {
    case 1:
      width = 100;
      break;
    case 2:
      width = 116;
      break;
    case 3:
      width = 136;
      break;
    case 4:
      width = 168;
      break;
    case 5:
      width = 232;
      break;
    case 6:
      width = 280;
      break;
  }
  return width;
}

/**
 * 找到根节点
 * @param nodes 节点数据
 * @param targetId 需要找的节点id
 * @param id id的key
 * @param parentId 父级id的key
 * @returns {object}
 */
export const findRootNode = (
  nodes: any[],
  targetId,
  id: string,
  parentId: string
): object => {
  const nodeMap: Record<number | string, any> = {};
  // 创建一个映射，方便通过 id 快速查找节点
  for (const node of nodes) {
    nodeMap[node[id]] = node;
  }
  let currentNode = nodeMap[targetId];
  // 向上遍历直到找到根节点
  while (currentNode && currentNode[parentId]) {
    currentNode = nodeMap[currentNode[parentId]];
  }

  return currentNode;
};

/**
 * 将扁平化数据转为树形结构
 * @param flatData 扁平化数据
 * @param id id的key
 * @param parentId 父级id的key
 * @returns {Array}
 */
export const buildTree = (
  flatData: any[],
  id: string,
  parentId: string
): any[] => {
  // 用于快速查找节点的映射
  const idMapping: Record<number, any> = {};
  flatData.forEach((node) => {
    idMapping[node[id]] = { ...node, children: [] };
  });

  let rootNodes: any[] = [];
  flatData.forEach((node) => {
    const mappedNode = idMapping[node[id]];
    if (node[parentId]) {
      // 有父节点，将其添加到父节点的 children 中
      const parentNode = idMapping[node[parentId]];
      if (parentNode) {
        parentNode.children?.push(mappedNode);
      }
    } else {
      // 没有父节点，这是根节点
      rootNodes.push(mappedNode);
    }
  });
  return rootNodes;
};

/**
 * 扁平化对象
 * @param obj 对象
 * @param parentKey 父级key
 * @param result 上一次遍历的结果
 * @returns {object}
 */
export const flattenObject = (
  obj: object,
  parentKey = "",
  result = {}
): object => {
  for (let key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      let newKey = parentKey ? `${parentKey}.${key}` : key;
      if (typeof obj[key] === "object" && obj[key] !== null) {
        flattenObject(obj[key], newKey, result);
      } else {
        result[newKey] = obj[key];
      }
    }
  }
  return result;
};

/**
 * 嵌套对象扁平化
 * @param obj
 * @returns {object} 模块扁平化对象
 */
export const flattenNestedObject = (obj: object): object => {
  let res = {};
  for (const key in obj) {
    if (
      Object.prototype.hasOwnProperty.call(obj, key) &&
      typeof obj[key] === "object"
    ) {
      const temp = flattenObject({ [key]: obj[key] });
      res = { ...res, ...temp };
    }
  }
  return res;
};

/**
 * 找出目录下符合规则的文件
 * @param context 上下文
 * @param reg 匹配文件的正则
 * @param flatten 输出对象是否需要扁平化 {模块1: {字段1，字段2}, 模块2: {字段3, 字段4}}
 * @returns {object}
 */
export const getFileObj = (
  context: any,
  reg: RegExp,
  flatten: boolean = true
): object => {
  const obj: object = {};
  const temp = context.keys().filter((t: string) => t !== "./index.ts");

  temp.forEach((key: string) => {
    const match = key.match(reg);
    if (match) {
      const fileName = match[1];
      obj[fileName] = context(key).default;
    }
  });
  return flatten ? flattenNestedObject(obj) : obj;
};

/**
 * 根据传入的rem转换为px某些组件如G6需要使用px）
 * @param rem
 * @returns {string}
 */
export const getFontSize = (rem) => {
  const { fontSize } = window.getComputedStyle(document.documentElement);
  const matchFontSize = fontSize.match(/\d+(?=px)/);
  let result = 0;
  if (matchFontSize) {
    result = Number(matchFontSize[0]);
  }
  const matchRem = rem.match(/\d+(?=rem)/);
  let remNum = 0;
  if (matchRem) {
    remNum = Number(matchRem[0]);
  }
  return result * remNum;
};

/**
 * 动态修改表单某项的disabled的值，支持多项修改
 * @param infoFormConfig 表单内容
 * @param fieldName 需要修改的字段，支持'a,b,c'传输
 * @param newDisabledValue 修改后的属性
 */
export const updateDisabledProperty = (
  infoFormConfig,
  fieldName,
  newDisabledValue
) => {
  const newFieldName = _.isArray(fieldName) ? fieldName : fieldName.split(",");
  _.forEach(infoFormConfig, (section) => {
    if (section.type === "Row") {
      _.forEach(section.data, (field) => {
        if (newFieldName.includes(field.name)) {
          field.disabled = newDisabledValue;
        }
      });
    }
  });
};

/**
 * 动态修改或新增表单某个或多个项的某个属性的值
 * @param infoFormConfig 表单内容
 * @param fieldName 需要修改的字段，支持'a,b,c'传输
 * @param updatePropName 对应fieldName下想要修改的属性名
 * @param newValue 修改后的值
 */
export const updateProperty = (
  infoFormConfig,
  fieldName,
  updatePropName: string,
  newValue
) => {
  const newFieldName = _.isArray(fieldName) ? fieldName : fieldName.split(",");
  _.forEach(infoFormConfig, (section) => {
    if (section.type === "Row") {
      _.forEach(section.data, (field) => {
        if (newFieldName.includes(field.name)) {
          field[updatePropName] = newValue;
        }
      });
    }
  });
};

/**
 * 国际化，用于处理antdSign组件标题等
 * @param prefix
 * @param key
 * @returns
 */
const formateHtmlText = (prefix: string, key: string): ReactNode => {
  const id = prefix ? `${prefix}.${key}` : key;
  return <FormattedMessage id={id} />;
};

/**
 * 处理下拉枚举，格式化为label - value 展示
 * @param text 后端返回的字段值
 * @param list 枚举列表
 * @param prefix 国际化前缀
 * @param showKey 是否展示label
 * @returns
 */
const renderCol = (
  text: string,
  list: Array<IDictList>,
  prefix: string = "",
  showKey = true
) => {
  const label = _.get(_.find(list, { key: text }), "value", "");
  if (!label) {
    return text;
  }
  if (showKey) {
    return (
      <>
        {text && <span>{text} - </span>}
        {formateHtmlText(prefix, label)}
      </>
    );
  } else {
    return formateHtmlText(prefix, label);
  }
};

/**
 * 格式化值
 * @param value 值
 * @param option 相关配置（如valueType、prefix...）
 * @returns {string | JSX}
 */
export const renderValueByType = (value, option) => {
  const {
    valueType,
    dictType,
    data,
    prefix,
    showKey,
    amountLength,
    optionPrefix,
  } = option;
  switch (valueType) {
    // 金额
    case publicConstant.RENDER_TYPE.Amount:
      return formatNumber(value, amountLength);
    // 数据字典
    case publicConstant.RENDER_TYPE.Dictionary: {
      const [dictState] = store.useModel("dict");
      const list: any = dictState.dictMap[dictType] || [];
      return renderCol(value, list, prefix, showKey);
    }
    // 数据字典(mock)
    case publicConstant.RENDER_TYPE.MockDictionary:
      return renderCol(value, data, optionPrefix || prefix, showKey);
    // 时间
    case publicConstant.RENDER_TYPE.DateTime:
      return value && dayjs(value).format(publicConstant.TIME_FORMATE);
    // 日期
    case publicConstant.RENDER_TYPE.Date:
      return value && dayjs(value).format(publicConstant.DATE_FORMATE);
    default:
      return value;
  }
};

/**
 * 格式化列表值
 * @param column 每列列表数据
 * @returns {object}
 */
export const renderColumnByType = (column) => {
  let { valueType, render } = column;
  const res = { ...column };
  switch (valueType) {
    // 超长省略
    case publicConstant.RENDER_TYPE.Ellipsis:
      res.ellipsis = true;
      break;
    // 金额
    case publicConstant.RENDER_TYPE.Amount:
      res.align = "right";
    // 其他不需特殊加字段的类型（数据字典、数据字典(mock)、时间、日期...）
    default:
      // 有render方法则自己处理
      if (_.isNil(render)) {
        res.render = (text) => renderValueByType(text, column);
      }
      break;
  }
  delete res.data;
  return res;
};
/**
 * 是否请求列表接口，查询条件若有必输，首次进页面则不掉用接口
 * @param data 查询条件数据
 * @returns
 */
export const isGetTableData = (data: any) => {
  const { searchSource } = data;
  const isSearch = searchSource
    .filter((item) => item.rules)
    .map((ite) => ite.rules.find((it) => it.required))
    .some((item) => item.required === true);
  return isSearch;
};

/**
 * 格式化form时间
 * @param type 字段类型
 * @param value 字段值
 * @returns
 */
export const formatformItemData = (type, value) => {
  if (type === publicConstant.COMPONENT_TYPE.DATE_PICKER) {
    return value && dayjs(value);
  } else {
    return value;
  }
};

/**
 * 处理form录入的值，时间格式或者输入框去掉空格
 * @param type 字段类型
 * @param value 字段值
 * @param format 传入的格式，用于格式化时间
 * @returns
 */
export const formatformItemNormalize = (
  type,
  value,
  format = publicConstant.DATE_FORMATE
) => {
  let res;
  switch (type) {
    case publicConstant.COMPONENT_TYPE.DATE_PICKER:
      res = value && dayjs(value).format(format);
      break;
    case publicConstant.COMPONENT_TYPE.INPUT:
      res = _.isNumber(value) ? value : value?.trim();
      break;
    default:
      res = value;
      break;
  }
  return res;
};

// 金额类输入框默认 13位整数，2位小数
export const formatterAmountInput = (value, decimal) => {
  // 使用Intl.NumberFormat来格式化数字为带千分位的字符串
  // 注意：这里使用'zh-Hans-CN'作为locale，但'zh-CN'通常也是有效的
  const formatter = new Intl.NumberFormat("zh-Hans-CN", {
    style: "decimal",
    minimumFractionDigits: decimal, // 根据需要设置最小小数位数
    maximumFractionDigits: decimal, // 根据需要设置最大小数位数
  });
  return value && formatter.format(value);
};

/**
 *  处理金额数据
 * @param value 输入框的值
 * @returns
 */
export const parserAmountInput = (value) => {
  // 移除所有非数字字符（包括千分位逗号和小数点），然后尝试转换
  // 但这里我们不应该移除小数点，所以只移除千分位逗号
  const cleanedValue = value.replace(/,/g, "");
  // 尝试将清理后的字符串转换为浮点数
  const numericValue = parseFloat(cleanedValue);
  // 如果转换后的数字不是NaN且是有限的（避免Infinity等情况），则返回它
  return !isNaN(numericValue) && isFinite(numericValue)
    ? numericValue
    : undefined;
};

const BUTTON_MAP = {
  copy: <CopyOutlined />,
  detail: <EyeOutlined />,
  edit: <FormOutlined />,
  delete: <DeleteOutlined />,
  cancel: <CloseOutlined />,
  save: <SaveOutlined />,
  up: <ArrowUpOutlined />,
  down: <ArrowDownOutlined />,
  search: <SearchOutlined />,
  flowNode: <ApartmentOutlined />,
  dispatch: <SendOutlined />,
  dowload: <DownloadOutlined />,
  aiHelp: <ClusterOutlined />,
};

// 获取按钮
export const renderButton = (data): ReactNode => {
  const { type, disabled = false, loading = false, onClick = () => {} } = data;
  const title = formateHtmlText(
    publicConstant.I18N_COMON_PAGENAME.COMMON,
    type
  );
  switch (type) {
    case publicConstant.OPERATE_TYPE.copy:
    case publicConstant.OPERATE_TYPE.detail:
    case publicConstant.OPERATE_TYPE.edit:
    case publicConstant.OPERATE_TYPE.cancel:
    case publicConstant.OPERATE_TYPE.save:
    case publicConstant.OPERATE_TYPE.up:
    case publicConstant.OPERATE_TYPE.down:
    case publicConstant.OPERATE_TYPE.flowNode:
    case publicConstant.OPERATE_TYPE.dispatch:
    case publicConstant.OPERATE_TYPE.dowload:
    case publicConstant.OPERATE_TYPE.aiHelp:
      return (
        <Tooltip key={type} placement="top" title={title}>
          <Button
            type="link"
            style={{ color: theme }}
            icon={BUTTON_MAP[type]}
            disabled={disabled}
            loading={loading}
            onClick={() => onClick(type)}
          />
        </Tooltip>
      );
    case publicConstant.OPERATE_TYPE.delete:
      return (
        <Tooltip key={type} placement="top" title={title}>
          <Popconfirm
            title={formateHtmlText(
              publicConstant.I18N_COMON_PAGENAME.COMMON,
              "confirmDelete"
            )}
            okText={formateHtmlText(
              publicConstant.I18N_COMON_PAGENAME.COMMON,
              "confirm"
            )}
            cancelText={formateHtmlText(
              publicConstant.I18N_COMON_PAGENAME.COMMON,
              "cancel"
            )}
            onConfirm={() => onClick(type)}
          >
            <Button
              type="link"
              icon={BUTTON_MAP[type]}
              disabled={disabled}
              danger
              loading={loading}
            />
          </Popconfirm>
        </Tooltip>
      );
    default:
      return (
        <Tooltip
          title={formateHtmlText(
            publicConstant.I18N_COMON_PAGENAME.COMMON,
            "search"
          )}
        >
          <Button shape="circle" icon={<SearchOutlined />} loading={loading} />
        </Tooltip>
      );
  }
};

/**
 * 分页国际化
 * @param total 总页数
 * @returns {string}
 */
export const showTotal = (total: number): string => {
  const locale = localStorage.getItem("locale");
  switch (locale) {
    case LANGUAGE_LIST.EN.locale:
      return `Toal ${total}`;
    // case LANGUAGE_LIST.TW.locale:
    //   return `共 ${total} 條`;
    default:
      return `共 ${total} 条`;
  }
};
