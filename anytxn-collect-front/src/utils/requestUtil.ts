/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import axios from 'axios';
import BigJSO<PERSON> from 'json-bigint';
import { notification } from 'antd';
import { AUTH_FAILED, SUCCESS_CODE, SESSION } from '@/constants/publicConstant';
import { IResponseResult } from '@/types/IRequest';

export const IS_DEV_MODE = process.env.NODE_ENV !== 'production';
const JSONbigString = BigJSON({
  storeAsString: true,
});
const requestUtil = axios.create({
  baseURL: '/api/anytxn-collect-web', // 采用NG统一接口地址，开发时候用代理就可以
  timeout: 10000,
  transformResponse: [
    function (data) {
      try {
        data = JSONbigString.parse(data);
        return data;
      } catch (err) {
        console.error(err);
        notification.error({ message: '接口回應異常' });
      }
    },
  ],
});

requestUtil.interceptors.request.use(
  (config) => {
    const token = sessionStorage.getItem(SESSION.token);
    if (token) {
      config.headers['X-Requested-With'] = 'XMLHttpRequest';
      config.headers.Authorization = ['Bearer', token].join(' ');
    }
    return config;
  },
  (error) => {
    console.error('request错误', error);
  },
);

requestUtil.interceptors.response.use(
  (response) => {
    const res: IResponseResult<any> = response.data;
    // 下载，返回文件流
    if (res instanceof Blob) {
      return res;
    }
    const { header, message } = res || {};
    // 有返回码的普通请求
    if (header?.errorCode) {
      // 接口错误
      if (header.errorCode !== SUCCESS_CODE) {
        notification.error({ message: header.errorMsg });
        return false;
      }
      // 请求成功
      // 后管接口分一参数返回在了res里面，所以需要全部返回到页面
      // 目前data都为null--废弃
      return res;
    } else {
      // 没返回码的特殊请求
      message && notification.error({ message });
      return res;
    }
  },
  (error) => {
    const code = error.response?.status;
    let errorMsg = error.response?.data?.header?.errorMsg || error.message;
    // 前端异常状态处理
    if ([502, 503].includes(code)) {
      // 要重定向到登录页面
      sessionStorage.clear();
      window.location.href = '/$';
    }
    // 后端异常状态码处理（header下errorCode为B1300001为接口无权访问，B1300002为token认证失败，需要处理跳转到404页面）
    if (code === 401) {
      // token过期重定向到404
      if (error.response?.data?.header?.errorCode === AUTH_FAILED) {
        sessionStorage.clear();
        notification.error({ message: errorMsg });
        setTimeout(() => {
          window.location.href = '/$';
        }, 1000);
        return false;
      }
    }
    console.error('response错误', error);
    notification.error({ message: errorMsg });
    return false;
  },
);

export default requestUtil;
