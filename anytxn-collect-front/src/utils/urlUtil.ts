/**
 * Created by ca<PERSON>un on 2025/6/6.
 */
import _ from 'lodash';
import { isEmpty } from './mathUtil';
import { OPERATE_TYPE, PARAMS_URLOBJ, SUBMIT_TYPE } from '@/constants/publicConstant';
import { IQueryData, IQueryParam } from '@/types/IRequest';
import { IPaniation } from '@/types/ICommon';
import { TABLE_BIZKEY_MAP } from '@/constants/requestTableConstants';

/**
 * get请求链接参数转成object对象
 * @param locationSearch
 */
export const url2obj = (locationSearch: string): object => {
  const res = {};
  const paramArr = locationSearch.substr(1, locationSearch.length).split('&');
  paramArr.forEach((item) => {
    const keyValue = item.split('=');
    if (keyValue.length === 2) {
      res[keyValue[0]] = keyValue[1];
    }
  });
  return res;
};

/**
 * object对象转换成get请求链接参数
 * @param obj
 */
export const obj2url = (obj: object): string => {
  if (obj) {
    if (Object.prototype.toString.call(obj) === '[object Object]') {
      let res = '';
      for (const key of Object.keys(obj)) {
        const value = obj[key];
        res += `${key}=${value}&`;
      }
      if (res.indexOf('&') > -1) {
        res = res.substr(0, res.length - 1);
      }
      return `?${res}`;
    } else {
      return `/${obj}`;
    }
  } else {
    return '';
  }
};

/**
 * 设置参数查询接口传参
 */
export const setParamQueryUrlParamData = (param: IQueryParam, connector: string, conditionKey: string): object => {
  const { searchValue = {}, url, pagination = {}, groupByFieldList, orderByFieldList } = param;
  const dataD = {
    condition: Object.entries(searchValue)
      .filter(([key, value]) => value)
      .map(([key, value]) => (conditionKey ? `${conditionKey}=#{${key}}` : `${key}=#{${key}}`))
      .join(connector || ' and '),
    data: Object.fromEntries(Object.entries(searchValue).filter(([key, value]) => value)),
  };
  const { TABLE_NAME } = url;
  // 表名改为在REQUEST_TABLE_MAP维护，可在param传
  const tableName = TABLE_NAME || '';
  const { currentPage: start, pageSize } = pagination as IPaniation;
  // 判断有无传分页
  const otherParam = isEmpty(pagination) ? {} : { start, pageSize };
  return {
    body: {
      ...otherParam,
      ...dataD,
      tableName,
      groupByFieldList,
      orderByFieldList,
    },
  };
};

/**
 * 设置参数维护接口传参
 */
export const setParamUpdateUrlParamData = (params): object => {
  const { url = PARAMS_URLOBJ.edit, data, type } = params;
  const { TABLE_NAME } = url;
  const deleteArr = [
    'incId',
    'paramIndex',
    'type',
    'createUser',
    'createTime',
    'updateTime',
    'updateUser',
    'createUserNo',
    'updateUserNo',
    'syncTime',
    'createOrgNo',
    'updateOrgNo',
  ];
  // 兼容单表，多表传数组
  const postParam = Array.isArray(data) ? data : [{ data }];
  const paramList = postParam.map((param) => {
    const { data: paramData, bizKey = [], tableName } = param;
    // 删除不需要的参数
    deleteArr.map((item) => !_.isNil(paramData?.[item]) && delete paramData[item]);
    // 多表的需要单独传，否则使用外层的TABLE_NAME
    const tName = tableName || TABLE_NAME;
    const paramDataList = Array.isArray(paramData) ? paramData : [paramData];
    return {
      tableName: tName,
      // bizKey改为在TABLE_BIZKEY_MAP维护，可在param传

      bizKey: TABLE_BIZKEY_MAP[tName] || bizKey,
      paramData: paramDataList.map((paramItem) => {
        // 如果多表里存在可编辑表格可新增可修改的情况，可以每行数据传_opType_，没传用外层的type
        const operateType = paramItem?._opType_ === SUBMIT_TYPE.create ? OPERATE_TYPE.create : type;
        // 参数新增，参数状态默认为有效
        const otherParam = [OPERATE_TYPE.create, OPERATE_TYPE.copy].includes(operateType) ? { paramSts: '1' } : {};
        // 删除不需要传的参数
        deleteArr.map((item) => !_.isNil(paramItem?.[item]) && delete paramItem[item]);
        return {
          // 默认版本1
          version: 1,
          _opType_: SUBMIT_TYPE[type] || SUBMIT_TYPE.edit,
          ...paramItem,
          ...otherParam,
        };
      }),
    };
  });

  return {
    body: {
      tableName: TABLE_NAME,
      paramList,
    },
  };
};

/**
 * 设置业务查询接口传参
 * @param params
 */
export const setBusinessQueryParamData = (params: IQueryParam) => {
  const { searchValue, pagination = {}, url } = params;
  const { currentPage: start, pageSize } = pagination as IPaniation;
  const otherParam = isEmpty(pagination) ? {} : { start, pageSize };
  return {
    url: url?.URL || '',
    body: {
      ...otherParam,
      ...searchValue,
    },
  };
};

/**
 * 处理返回数据格式
 */
export const setResult = (res): IQueryData => {
  return Array.isArray(res?.data)
    ? {
        data: res?.data.map((item, i) => ({
          ...item,
          paramIndex: i++ + 1, // 用于页面序号展示
        })),
        total: res.page?.totalCount,
      }
    : { data: [], total: 0 };
};

/**
 * 参数页面通用处理接口参数
 */
export const getRequestData = (postData, type: string, rowData): object => {
  let otherParam = {};
  switch (type) {
    case OPERATE_TYPE.edit: {
      // 编辑带入版本号和参数状态
      const { version, paramSts } = rowData;
      otherParam = { version, paramSts };
    }
    case OPERATE_TYPE.create:
      return { ...otherParam, ...postData.formData };
    default:
      return { ...postData };
  }
};
