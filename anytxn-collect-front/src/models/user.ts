/**
 * 用户信息store
 */

import { createModel } from 'ice';
import { IUserInfo } from '@/types/IUser';
import { TSysMenuItem } from '@/types/TCommon';
import { getUserPermission } from '@/services/menu';
import { buildTree } from '@/utils/comUtil';

interface IState {
  // 用户信息
  currentUser: IUserInfo;
  // 按钮权限
  permissionButtons: TSysMenuItem[];
  // 菜单数据
  menuData: TSysMenuItem[];
  // 菜单数据（树形结构）
  menuTree: TSysMenuItem[];
  // 是否初始化完成
  initDone: boolean;
}

export default createModel({
  state: {
    currentUser: {} as IUserInfo,
    permissionButtons: [],
    menuData: [],
    menuTree: [],
    initDone: false,
  } as IState,
  reducers: {
    // 更新用户信息
    updateUserInfo(state, payload) {
      state.currentUser = payload;
    },
    // 更新按钮权限数据
    updatePermissionButtonsInfo(state, payload: TSysMenuItem[]) {
      state.permissionButtons = payload;
    },
    // 更新菜单数据（扁平结构
    updateMenuData(state, payload: TSysMenuItem[]) {
      state.menuData = payload;
    },
    // 更新菜单数据（树结构
    updateMenuTree(state, payload: TSysMenuItem[]) {
      state.menuTree = payload;
    },
    updateInitDone(state, payload: boolean) {
      state.initDone = payload;
    },
  },
  effects: (dispatch) => ({
    // 获取菜单按钮权限数据
    async getUserPermissionInfo() {
      const res = await getUserPermission({ body: {} });
      if (!res) {
        return res;
      }
      const { menuData, buttonData } = res;
      this.updateMenuData(menuData);
      this.updatePermissionButtonsInfo(buttonData);
      if (menuData && menuData.length > 0) {
        this.updateMenuTree(buildTree(menuData, 'menuId', 'parentMenuId'));
      }
      this.updateInitDone(true);
    },
  }),
});
