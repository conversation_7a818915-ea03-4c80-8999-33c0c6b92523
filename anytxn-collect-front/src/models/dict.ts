/**
 * 字典/参数表字段store
 */
import { createModel } from "ice";
import _ from "lodash";
import common from "@/services/common";
import { IDictParam, IState } from "@/types/IDict";

export default createModel({
  state: {
    dictMap: {},
  } as IState,
  reducers: {
    // 更新store的值
    updateDictMap(state, payload) {
      state.dictMap = { ...state.dictMap, ...payload };
    },
  },
  effects: (dispatch) => ({
    // 获取字典/参数数据
    async getDictList(dictParam: IDictParam) {
      const {
        url,
        key,
        param,
        optionKey,
        optionValue,
        serviceType,
        showKey = false,
      } = dictParam;
      // let res: any;
      // if (serviceType === "business") {
      //   res = await common.getTableListDataBiz({ url, ...param });
      // } else {
      // }
      const res = await common.getTableListData({ url, param });
      const { data } = res;
      const result = data.map((item) => {
        const value = showKey
          ? `${item[optionKey]} - ${item[optionValue]}`
          : item[optionValue];
        return { ...item, key: item[optionKey], value };
      });
      this.updateDictMap({ [key]: result });
    },
  }),
});
