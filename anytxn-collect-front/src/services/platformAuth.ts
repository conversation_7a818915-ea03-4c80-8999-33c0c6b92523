import { postData, getData, deleteData } from '@/utils/serviceUtil';
import dayjs from 'dayjs';

// 业务平台下授权模块
// 获取授权交易流水列表
export async function getDailyAuthQueryList(data: object): Promise<any> {
    const res = await getData('/anytxn-param/nq/service/pageQuery/getDailyQueryList', data);
    let itemArray: any = [];
    if (res.data && res.data.length > 0) {
        let i = 1;
        res.data.map((item: any) => {
            itemArray.push({ ...item, coreAcqrDate: dayjs(item.coreAcqrDate), coreAcqrDateShow: item.coreAcqrDate, coreAcqrTime: dayjs(item.coreAcqrTime), coreAcqrTimeShow: item.coreAcqrTime, id: i++ });
        });
    }
    return { data: itemArray, total: 3 };
}

// 获取密码维护流水列表
export async function getpassWordList(data: object): Promise<any> {
    const res = await getData('/anytxn-param/nq/service/pageQuery/getPassList', data);
    let itemArray: any = [];
    if (res.data && res.data.length > 0) {
        let i = 1;
        res.data.map((item: any) => {
            itemArray.push({ ...item, paramIndex: i++ });
        });
    }
    res.data = itemArray;
    return { data: itemArray, total: 11 };
}

// 获取流量累计列表
export async function getflowList(data: object): Promise<any> {
    const res = await getData('/anytxn-param/nq/service/pageQuery/getFlowList', data);
    let itemArray: any = [];
    if (res.data && res.data.length > 0) {
        let i = 1;
        res.data.map((item: any) => {
            itemArray.push({ ...item, paramIndex: i++ });
        });
    }
    res.data = itemArray;
    return { data: itemArray, total: 11 };
}

// 获取卡片活动累计信息
export async function getActivityCumData(data: object): Promise<any> {
    const res = await postData('/anytxn-param/nq/service/pageQuery/getActivityCumData', data);
    return res;
}

// 新增或者修改卡片活动累计开参信息
export async function editActivityCumData(data: object): Promise<any> {
    const res = await postData('/anytxn-param/nq/service/pageQuery/editActivityCumData', data);
    return res;
}

// 获取授權卡片控管群列表数据
export async function getAuthCardControlGroupList(data: object): Promise<any> {
    const res = await getData('/anytxn-param/nq/service/pageQuery/getAuthCardControlGroupList', data);
    let itemArray: any = [];
    if (res.data && res.data.length > 0) {
        let i = 1;
        res.data.map((item: any) => {
            itemArray.push({ ...item, paramIndex: i++ });
        });
    }
    res.data = itemArray;
    return { data: itemArray, total: 11 };
}

// 新增或者修改授權卡片控管群数据
export async function editAuthCardControlGroup(data: object): Promise<any> {
    const res = await postData('/anytxn-param/nq/service/pageQuery/editAuthCardControlGroup', data);
    return res;
}

// 删除授權卡片控管群数据
export async function deleteAuthCardControlGroup(data: object): Promise<any> {
    const res = await postData('/anytxn-param/nq/service/pageQuery/deleteAuthCardControlGroup', data);
    return res;
}

// 根据卡号获取伪冒卡维护信息
export async function getfakeCardData(data: object): Promise<any> {
    const res = await getData('/anytxn-param/nq/service/pageQuery/getfakeCardData', data);
    return { data: res.data, total: 1 };
}

// 新增或者修改伪冒卡维护信息
export async function editfakeCardData(data: object): Promise<any> {
    const res = await postData('/anytxn-param/nq/service/pageQuery/editfakeCardData', data);
    return res;
}

// 删除伪冒卡维护信息
export async function deletefakeCardData(data: object): Promise<any> {
    const res = await postData('/anytxn-param/nq/service/pageQuery/deletefakeCardData', data);
    return res;
}

export default {
    getDailyAuthQueryList,
    getpassWordList,
    getflowList,
    getActivityCumData,
    editActivityCumData,
    getAuthCardControlGroupList,
    editAuthCardControlGroup,
    deleteAuthCardControlGroup,
    getfakeCardData,
    editfakeCardData,
    deletefakeCardData,
};