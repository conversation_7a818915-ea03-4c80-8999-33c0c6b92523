import { getData, postData } from "@/utils/serviceUtil";
import { TSysMenuItem } from "@/types/TCommon";
import mockMenuData from "@/constants/mockMenuData";
import { getCurrentLocale } from "@/hooks/useIntlCustom";

const processMenuData = (menuData: TSysMenuItem[]) => {
  // 递归方法
  const recursionMenuData = (item, childrenData) => {
    childrenData &&
      childrenData.forEach((child) => {
        if (item.menuId === child.parentMenuId) {
          recursionMenuData(child, childrenData);
          item.children.push(child);
        }
      });
    if (item.children && item.children.length == 0) {
      delete item.children;
    }
  };
  const menuTree: TSysMenuItem[] = [];
  const childrenData: TSysMenuItem[] = [];
  menuData.forEach((item) => {
    item.children = [];
    item.key = item.menuId;
    // 数据转换
    if (item.level === 1) {
      menuTree.push(item);
    } else {
      childrenData.push(item);
    }
  });
  menuTree.forEach((item) => recursionMenuData(item, childrenData));
  // console.log(menuTree);
  return menuTree;
  // setMenuList(JSON.parse(JSON.stringify(menuTree)));
};

// 数据处理
const handleData = (res) => {
  // 数据处理，增加level和type的数据处理
  res.forEach((item) => {
    // menuStatus和iconId处理,需要注释
    if (!item.menuStatus) {
      item.menuStatus = "Y";
    }
    // if (!item.iconId) {
    //   item.iconId = item.icon ? item.icon : 'SettingOutlined';
    //   delete item.icon;
    // }
    // type处理
    if (
      item.menuRouteUrl &&
      item.menuRouteUrl.length > 0 &&
      item.menuRouteUrl !== " "
    ) {
      item.type = "link";
    } else {
      item.type = "directory";
    }
    // level处理
    const strLength = item.menuId.toString().length;
    switch (strLength) {
      case 1:
        item.level = 1;
        break;
      case 4:
        item.level = 2;
        break;
      case 7:
        item.level = 3;
        break;
      case 10:
        item.level = 4;
        break;
      case 13:
        item.level = 5;
        break;
      default:
        item.level = 5;
    }
  });
  return res;
};

// 菜单管理页面获取平台所有菜单按钮数据，树形结构
export async function getTreeList(data: object): Promise<any> {
  // const res = await getData('/api/sysMenuData', data);
  const param = { body: data };
  const res = await postData("/menu/list", param);
  // 数据处理封装
  const handleRes = handleData(res.data.menuInfoList);
  // 扁平化数据改成树状数据
  const val = processMenuData(handleRes);
  return val;
}

// 菜单管理页面获取平台所有菜单按钮数据，扁平化数据
export async function getMenuInfo(data: object): Promise<any> {
  const param = { body: data };
  const res = await postData("/menu/list", param);
  // 数据处理封装
  const handleRes = handleData(res.data.menuInfoList);
  return handleRes;
}

export async function getList(data: object): Promise<any> {
  const res = await getData("/api/sysMenuData", data);
  const val = handleData(res.data);
  // 去除按钮数据
  const result = val.filter((item) => item.menuType !== "1");
  return result;
}

export async function updateData(data: object): Promise<any> {
  // const res = await postData('/api/newMenuData', data);
  const res = await postData("/menu/update", data);
  return res;
}

// 获取用户权限
export async function getUserPermission(
  data: object
): Promise<{ menuData: TSysMenuItem[]; buttonData: TSysMenuItem[] }> {
  // const res = await postData('/user/getUserPermission', data);
  const res = mockMenuData;
  if (!res) {
    return res;
  }
  // const val = handleData(res.data?.[getCurrentLocale()]);
  const val = handleData(res.data?.['zh-cn']);
  let result = val.map((item: TSysMenuItem) => {
    // 若menuRouteUrl为'',type = 'directory',移除menuRouteUrl
    if (item.type === "directory" && item.menuRouteUrl === " ") {
      delete item.menuRouteUrl;
    }
    return item;
  });
  const menuData: TSysMenuItem[] = [];
  const buttonData: TSysMenuItem[] = [];
  result.forEach((item: TSysMenuItem) => {
    // 有权限的按钮数据
    item.menuType === "1" && buttonData.push(item);
    // 有权限的菜单数据
    item.menuType === "0" && menuData.push(item);
  });
  return { menuData, buttonData };
}

// 获取用户基本信息
export async function getUserInfo(data: object): Promise<any> {
  const res = await postData("/user/getUserInfo", data);
  return res ? res.data : false;
}

export default {
  getTreeList,
  getList,
  updateData,
  getUserPermission,
  getUserInfo,
  getMenuInfo,
};
