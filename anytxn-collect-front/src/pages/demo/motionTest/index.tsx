import { React, useState } from 'react';
import { motion, AnimatePresence } from "framer-motion";
import {PageA,PageB} from './testPage';

const MotionDemo: React.FC = () => {
    const [page, setPage] = useState("PageA");
    return (
        // <div>
            <AnimatePresence  mode='wait'>
                {page === "PageA" ? (
                    <PageA key="PageA" setPage={setPage}/>
                ) : (
                    <PageB key="PageB" setPage={setPage}/>
                )}
            </AnimatePresence>
        // </div>
    )
};
export default MotionDemo;