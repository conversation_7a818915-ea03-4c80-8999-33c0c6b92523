import { motion } from "framer-motion";

const PageA = ({ setPage }) => {
    return (
        <motion.div
            initial = {{ opacity: 0 }}
            animate = {{ opacity: 1 }}
            exit = {{ opacity: 0 }}
        >
            <h1>Page A</h1>
            <button onClick={() => setPage("PageB")}>Go to Page B</button>
        </motion.div>
    );
};

const PageB = ({ setPage }) => {
    return (
        <motion.div
            initial = {{ opacity: 0 }}
            animate = {{ opacity: 1 }}
            exit = {{ opacity: 0 }}
        >
            <h1>Page B</h1>
            <button onClick={() => setPage("PageA")}>Go to Page A</button>
        </motion.div>
    );
};

export { PageA,PageB };