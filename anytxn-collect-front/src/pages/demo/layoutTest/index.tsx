/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */
import React from "react";
import { GridRow, GridCol } from "@/components/grids";

const styleHeight1 = { minHeight: 400 };
const styleHeight2 = { minHeight: 300 };
const styleHeight3 = { minHeight: 240 };
export default class Dashboard extends React.PureComponent {
  render() {
    return (
      <div className="flex-col flex-1 app-overflow">
        <GridRow style={styleHeight1}>
          <GridCol>
            <div className="app-container">
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
              <p>这里放置主要信息看板</p>
            </div>
          </GridCol>
          <GridCol isRight>
            <div className="app-container flex-1 m-b-s">
              <p>看板1：放置仪表盘</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
              <p>看板1</p>
            </div>
            <div className="app-container flex-1 m-t-s">
              <p>看板2：放置仪表盘</p>
              <p>看板2</p>
              <p>看板2</p>
              <p>看板2</p>
            </div>
          </GridCol>
        </GridRow>
        <GridRow style={styleHeight2}>
          <GridCol>
            <div className="app-container">
              <p>看板3：放置饼图</p>
              <p>看板3</p>
              <p>看板3</p>
              <p>看板3</p>
            </div>
          </GridCol>
          <GridCol isRight>
            <div className="app-container">
              <p>看板4：放置雷达图</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
              <p>看板4</p>
            </div>
          </GridCol>
        </GridRow>
        <GridRow style={styleHeight3} isBottom>
          <GridCol>
            <div className="app-container">
              <p>看板5：放置柱图</p>
              <p>看板5</p>
              <p>看板5</p>
              <p>看板5</p>
            </div>
          </GridCol>
          <GridCol isRight>
            <div className="app-container">
              <p>看板6：放置趋势图</p>
              <p>看板6</p>
              <p>看板6</p>
              <p>看板6</p>
            </div>
          </GridCol>
        </GridRow>
      </div>
    );
  }
}
