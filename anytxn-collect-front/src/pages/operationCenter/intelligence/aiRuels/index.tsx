import React, { useState } from 'react';
import { Input, Button, message } from 'antd';
import styled from 'styled-components';
import RouterBar from '@/layouts/header/RouterBar';
import useIntlCustom from '@/hooks/useIntlCustom';

// 使用styled-components创建样式组件
const Container = styled.div`
  width: 100%;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const Title = styled.h1`
  font-size: 24px;
  color: #333;
  margin-bottom: 24px;
`;

const FormItem = styled.div`
  margin-bottom: 24px;

  .label {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
  }

  .ant-input {
    border-radius: 4px;
  }
`;

const StyledButton = styled(Button)`
  background: #7e57c2;
  &:hover {
    background: #673ab7;
  }
`;

// 规则AI检查组件
const RuleAICheck = () => {
  const { translate } = useIntlCustom();
  // 状态管理
  const [ruleType, setRuleType] = useState('');
  const [content, setContent] = useState('');

  // 处理检查按钮点击
  const handleCheck = () => {
    if (!ruleType || !content) {
      message.warning(translate('callParam', 'pleaseComplete'));
      return;
    }
    // TODO: 实现检查逻辑
    message.success(translate('callParam', 'startCheckMessage'));
  };

  return (
    <>
      <div className="app-block m-b">
        <RouterBar />
      </div>
      <Container>
        <Title>{translate('callParam', 'aiRulesTitle')}</Title>

        {/* 规则类型输入区域 */}
        <FormItem>
          <div className="label">{translate('callParam', 'ruleTypeLabel')}</div>
          <Input 
            placeholder={translate('callParam', 'ruleTypePlaceholder')} 
            value={ruleType} 
            onChange={(e) => setRuleType(e.target.value)} 
          />
        </FormItem>

        {/* 提示词输入区域 */}
        <FormItem>
          <div className="label">{translate('callParam', 'promptLabel')}</div>
          <Input.TextArea
            rows={6}
            placeholder={translate('callParam', 'promptPlaceholder')}
            value={content}
            onChange={(e) => setContent(e.target.value)}
          />
        </FormItem>

        {/* 操作按钮 */}
        <StyledButton type="primary" size="large" onClick={handleCheck}>
          {translate('callParam', 'startCheck')}
        </StyledButton>
      </Container>
    </>
  );
};

export default RuleAICheck;
