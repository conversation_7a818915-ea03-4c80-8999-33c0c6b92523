// 业务參數/參數/授權/授權回應碼參數
import React, { Suspense, useState } from "react";
import _ from "lodash";
import { pageConfig, dictEnum } from "./pageConfig";
import { OPERATE_TYPE } from "@/constants/publicConstant";
import useFormConfig from "./useFormConfig";
import {
  data1,
  data2,
  data3,
} from "@/pages/operationCenter/flowManage/dispatchManage/mockData";
const PageTemplate = React.lazy(
  () => import("@/components/templates/PageTemplate")
);
const DargeGraph = React.lazy(() => import("@/materials/dargeGraph"));

const NodeManagePage: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = {
    nodeCode: "",
    nodeCallName: "",
    nodeType: null,
    nodeStatus: null,
  };
  const [detailData, setDetailData] = useState<any>({});
  const [flowData, setFlowData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const {
    infoFormConfig,
    editableFormRef,
    setEditTableData,
    editRulesTableFormRef,
    setEditTRuleableData,
  } = useFormConfig(type);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: "paramIndex",
    showPagination: true,
    optionList: [
      OPERATE_TYPE.detail,
      OPERATE_TYPE.edit,
      {
        type: OPERATE_TYPE.flowNode,
      },
    ],
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
    props: {
      editTableRefList: {
        nodeManageList: editableFormRef,
        dispatchManageList: editRulesTableFormRef,
      },
    },
    isCustom: true,
    customChildren: {
      [OPERATE_TYPE.flowNode]: (
        <DargeGraph
          data={flowData}
          // @ts-ignore
          behaviors={["zoom-canvas", "drag-element", "drag-canvas"]}
          canNodeClick={false}
        />
      ),
    },
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    let flowData = {};
    if ([1, 2, 3].includes(row.paramIndex)) {
      flowData = data1;
    } else if (row.paramIndex === 4) {
      flowData = data2;
    } else if (row.paramIndex === 5) {
      flowData = data3;
    } else {
      flowData = data1;
    }
    setFlowData(flowData);

    setDetailData({ ...row, nodeAuth: row.nodeAuth?.split(",") });
    setType(editType);
    if (editType === OPERATE_TYPE.edit) {
      const newModeManageList = row?.nodeManageList.map((item, index) => ({
        ...item,
        id: index + 1,
      }));
      setEditTableData(newModeManageList as any[]);
      const newDispatchManageList = row?.dispatchManageList
        ? JSON.parse(row?.dispatchManageList).map((item, index) => ({
            ...item,
            id: index + 1,
          }))
        : [];
      setEditTRuleableData(newDispatchManageList);
    } else {
      setEditTableData([]);
      setEditTRuleableData([]);
    }
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(NodeManagePage);
