import { useEffect, useRef, useState } from "react";
import { EditableFormInstance } from "@ant-design/pro-components";
import store from "@/store";
import {
  COMPONENT_TYPE,
  FORMITEM_TYPE,
  OPERATE_TYPE,
} from "@/constants/publicConstant";
import DICT_CONSTANTS from "@/constants/dictConstants";
import { IFormConfig } from "@/types/IForm";
import useIntlCustom from "@/hooks/useIntlCustom";

const useFormConfig = (type: string) => {
  const { translate } = useIntlCustom();
  const [dictState] = store.useModel("dict");
  const [editType, setEditType] = useState<string>(type);
  const [showRuleconfig, setShowRuleconfig] = useState<boolean>(false);
  const [editTableData, setEditTableData] = useState<any[]>([]);
  const [editRuleTableData, setEditTRuleableData] = useState<any[]>([]);
  const editableFormRef = useRef<EditableFormInstance>();
  const editRulesTableFormRef = useRef<EditableFormInstance>();

  useEffect(() => {
    if (type !== OPERATE_TYPE.edit) {
      setShowRuleconfig(false);
    }
  }, [type]);

  // 可编辑表格操作列
  const editColumns = [
    {
      width: 120,
      key: "nodePriority",
      title: "nodePriority",
      dataIndex: "nodePriority",
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    {
      title: "nodeAttriId",
      dataIndex: "nodeAttriId",
      valueType: "select",
      showKey: true,
      valueEnum: () => {
        const result = {};
        dictState.dictMap?.nodeAttriId?.map((item) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
  ];
  // 可编辑表格操作列
  const editRuleColumns = [
    {
      key: "nodeRule",
      title: "nodeRule",
      dataIndex: "nodeRule",
      valueEnum: () => {
        const result = {};
        dictState.dictMap?.nodeRule?.map((item) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      fieldProps: {
        mode: "multiple", // 设置为多选模式
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
    // {
    //   key: "nodeRuleFactor",
    //   title: "nodeRuleFactorResult",
    //   dataIndex: "nodeRuleFactor",
    //   valueType: "select",
    //   showKey: true,
    //   width: 120,
    //   valueEnum: () => {
    //     const result = {};
    //     dictState.dictMap?.nodeRuleFactor?.map((item) => {
    //       const { key, value } = item;
    //       result[key] = { text: value };
    //     });
    //     return result;
    //   },
    //   formItemProps: (form, { rowIndex }) => ({
    //     rules: [{ required: !!rowIndex }],
    //   }),
    // },
    // {
    //   key: "operator",
    //   title: "operator",
    //   dataIndex: "operator",
    //   valueType: "select",
    //   showKey: true,
    //   width: 80,
    //   valueEnum: () => {
    //     const result = {};
    //     DICT_CONSTANTS.RULE_OPERATOR_TYPE?.map((item) => {
    //       const { key, value } = item;
    //       result[key] = { text: value };
    //       result[key] = { text: translate("rules", value) };
    //     });
    //     return result;
    //   },
    //   formItemProps: (form, { rowIndex }) => ({
    //     rules: [{ required: !!rowIndex }],
    //   }),
    // },
    // {
    //   key: "nodeRuleValue",
    //   title: "nodeRuleValue",
    //   dataIndex: "nodeRuleValue",
    //   showKey: true,
    //   width: 120,
    //   formItemProps: (form, { rowIndex }) => ({
    //     rules: [{ required: !!rowIndex }],
    //   }),
    // },
    // {
    //   title: "nextNode",
    //   dataIndex: "nextNode",
    //   // valueType: "select",
    //   showKey: true,
    //   width: 120,
    //   // valueEnum: () => {
    //   //   const result = {};
    //   //   dictState.dictMap?.nodeAttriId?.map((item) => {
    //   //     const { key, value } = item;
    //   //     result[key] = { text: value };
    //   //   });
    //   //   return result;
    //   // },
    //   formItemProps: (form, { rowIndex }) => ({
    //     rules: [{ required: !!rowIndex }],
    //   }),
    // },
  ];
  // 表单字段
  const initInfoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "baseInfo",
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: "nodeCode",
          label: "nodeCode",
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: "nodeCallName",
          label: "nodeCallName",
          rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "nodeAuth",
          label: "nodeAuth",
          rules: [{ required: true }],
          data: DICT_CONSTANTS.NODE_AUTH,
          mode: "multiple",
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "nodeType",
          label: "nodeType",
          data: DICT_CONSTANTS.NODE_TYPE,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "nodeStatus",
          label: "nodeStatus",
          data: DICT_CONSTANTS.NODE_STATUS,
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: "nodeFuncDesc",
          label: "nodeFuncDesc",
          rules: [{ required: true }],
        },
      ],
    },
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "nodeConfig",
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: "id",
      columns: editColumns,
      dataSource: editTableData,
      canEdit: true,
      editableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onAction: (optionType, row) => {
        setShowRuleconfig(row.nodeAttriId === "NodeFlowRules");
      },
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  const ruleConfig = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "nodeProStrategy",
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: "id",
      columns: editRuleColumns,
      dataSource: editRuleTableData,
      canEdit: true,
      editableFormRef: editRulesTableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];

  let infoFormConfig;
  if (type !== OPERATE_TYPE.edit) {
    infoFormConfig = initInfoFormConfig;
  } else if (type === OPERATE_TYPE.edit) {
    infoFormConfig = showRuleconfig
      ? initInfoFormConfig.concat(ruleConfig)
      : initInfoFormConfig;
  }

  return {
    infoFormConfig,
    editableFormRef,
    setEditTableData,
    editRulesTableFormRef,
    setEditTRuleableData,
  };
};

export default useFormConfig;
