/**
 * 详情组件
 */
import { lazy } from 'react';
import { data1, data2, data3 } from './mockData';

const FlowGraph = lazy(() => import('@/materials/flowGraph'));

const Detail = ({ type, data }) => {

  // 根据索引指向不同数据
  let flowData = {}
  if (data.paramIndex === 1) {
    flowData = data1
  } else if (data.paramIndex === 2) {
    flowData = data2
  } else if (data.paramIndex === 3) {
    flowData = data3
  }

  return (<FlowGraph data={flowData} />);
};

export default Detail;
