import { COMPONENT_TYPE, FORMITEM_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { IFormConfig } from '@/types/IForm';
import store from '@/store';

const useFormConfig = (type: string) => {
  const [dictState] = store.useModel("dict");
  // 表单字段
  const infoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: 'baseInfo',
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeAttriId',
          label: 'nodeAttriId',
          // rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'node<PERSON>ttriN<PERSON>',
          label: 'nodeAttriName',
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'nodeAttriType',
          label: 'nodeAttriType',
          data: DICT_CONSTANTS.NODE_ATTRI_TYPE,
          rules: [{ required: true }],
        },
        // {
        //   type: COMPONENT_TYPE.INPUT,
        //   name: 'nodeAttriCode',
        //   label: 'nodeAttriCode',
        //   rules: [{ required: true }],
        //   // disabled: type !== OPERATE_TYPE.create,
        // },
        {
          type: COMPONENT_TYPE.INPUT,
          name: 'nodeAttriUp',
          label: 'nodeAttriUp',
          rules: [{ required: true }],
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: 'status',
          label: 'status',
          data: DICT_CONSTANTS.PARAM_STATUS,
          rules: [{ required: true }],
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: 'nodeAttriDesc',
          label: 'nodeAttriDesc',
          rules: [{ required: true }],
          maxLength: 200,
        },
      ],
    },
  ];

  return {
    infoFormConfig,
  };
};

export default useFormConfig;
