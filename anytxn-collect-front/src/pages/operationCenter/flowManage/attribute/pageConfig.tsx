import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, RENDER_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'nodeAttribute',
  // 页面接口请求
  urlObj: {
    list: urlConstants.ATTRIBUTE_MANAGE.LIST,
    create: urlConstants.ATTRIBUTE_MANAGE.CREATE,
    edit: urlConstants.ATTRIBUTE_MANAGE.EDIT,
    delete: urlConstants.ATTRIBUTE_MANAGE.DELETE,
    serviceType: 'business',
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: 'nodeAttriName',
      label: 'nodeAttriName',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'nodeAttriType',
      label: 'nodeAttriType',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.NODE_ATTRI_TYPE,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'nodeAttriId',
      dataIndex: 'nodeAttriId',
      key: 'nodeAttriId',
      width: 120,
    },
    {
      title: 'nodeAttriName',
      dataIndex: 'nodeAttriName',
      key: 'nodeAttriName',
      width: 120,
    },
    {
      title: 'nodeAttriType',
      dataIndex: 'nodeAttriType',
      key: 'nodeAttriType',
      data: DICT_CONSTANTS.NODE_ATTRI_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'NODE_ATTRI_TYPE',
      width: 120,
    },
    {
      title: 'nodeAttriDesc',
      dataIndex: 'nodeAttriDesc',
      key: 'nodeAttriDesc',
      width: 220,
      render: (text) => <div style={{ wordWrap: 'break-word', whiteSpace: 'normal' }}>{text}</div>,
    },
    // {
    //   title: 'nodeAttriCode',
    //   dataIndex: 'nodeAttriCode',
    //   key: 'nodeAttriCode',
    //   valueType: RENDER_TYPE.Ellipsis,
    // },
    {
      width: 150,
      title: 'nodeAttriUp',
      dataIndex: 'nodeAttriUp',
      key: 'nodeAttriUp',
      valueType: RENDER_TYPE.Ellipsis,
      align: 'left',
    },
    {
      width: 100,
      key: 'status',
      title: 'status',
      dataIndex: 'status',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      key: 'updateUser',
      title: 'updateUser',
      dataIndex: 'updateUser',
      width: 150,
    },
    {
      key: 'updateTime',
      title: 'updateTime',
      dataIndex: 'updateTime',
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // nodeRule: DICT_CONSTANTS.DICT_ENUM_MAP.nodeRule
};
