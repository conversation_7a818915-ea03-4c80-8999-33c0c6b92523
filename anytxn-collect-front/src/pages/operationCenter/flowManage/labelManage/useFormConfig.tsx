import {
  COMPONENT_TYPE,
  FORMITEM_TYPE,
  OPERATE_TYPE,
} from "@/constants/publicConstant";
import DICT_CONSTANTS from "@/constants/dictConstants";
import { IFormConfig } from "@/types/IForm";
import store from "@/store";
import { useEffect, useRef, useState } from "react";
import { EditableFormInstance } from "@ant-design/pro-components";

const useFormConfig = (type: string, ruleData, detailData) => {
  const [dictState] = store.useModel("dict");
  const [editType, setEditType] = useState<string>(type);
  const [editRuleTableData, setEditTRuleableData] = useState<any[]>([
    { id: "1" },
  ]);
  const editRulesTableFormRef = useRef<EditableFormInstance>();

  // 可编辑表格操作列
  const editRuleColumns = [
    {
      key: "nodeRule",
      title: "nodeRule",
      dataIndex: "nodeRule",
      valueEnum: () => {
        const result = {};
        dictState.dictMap?.nodeRule?.map((item) => {
          const { key, value } = item;
          result[key] = { text: value };
        });
        return result;
      },
      fieldProps: {
        mode: "multiple", // 设置为多选模式
      },
      formItemProps: (form, { rowIndex }) => ({
        rules: [{ required: !!rowIndex }],
      }),
    },
  ];
  // 表单字段
  const initInfoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "baseInfo",
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: "tagId",
          label: "tagId",
          // rules: [{ required: true }],
          disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: "tagName",
          label: "tagName",
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "tagType",
          label: "tagType",
          data: DICT_CONSTANTS.LABEL_TYPE,
          rules: [{ required: true }],
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "nodeAttriId",
          label: "nodeAttriId",
          isint: "0",
          data: dictState.dictMap?.nodeAttriId,
          mode: "multiple",
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "tagSource",
          label: "tagSource",
          rules: [{ required: true }],
          data: DICT_CONSTANTS.LABEL_SOURCE,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "tagAttribute",
          label: "tagAttribute",
          rules: [{ required: true }],
          data: DICT_CONSTANTS.LABEL_ATTRIBUTE,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "status",
          label: "status",
          data: DICT_CONSTANTS.PARAM_STATUS,
          rules: [{ required: true }],
          // disabled: type !== OPERATE_TYPE.create,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "nodeRuleype",
          label: "nodeRuleype",
          data: dictState.dictMap?.nodeRule?.filter(
            (item) => item.ruleType === "LabelHanldle"
          ),
          isint: "0",
          mode: "multiple",
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: "tagDescription",
          label: "tagDescription",
          rules: [{ required: true }],
          maxLength: 200,
        },
      ],
    },
  ];

  const ruleConfig = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "nodeProStrategy",
    },
    {
      type: FORMITEM_TYPE.EditTable,
      rowKey: "id",
      columns: editRuleColumns,
      dataSource: editRuleTableData,
      canEdit: true,
      editableFormRef: editRulesTableFormRef,
      optionCount: 1,
      getOptionList: (row) => [
        {
          optionType: OPERATE_TYPE.edit,
          disabled: editType === OPERATE_TYPE.detail,
        },
      ],
      onCreate: (index) => ({ id: Date.now() + 1, isAdd: true }),
    },
  ];
  const newRuleData = ruleData || detailData || {};
  let infoFormConfig;
  if (type === OPERATE_TYPE.list) {
    infoFormConfig = initInfoFormConfig;
  } else {
    infoFormConfig = ruleData?.nodeAttriId?.includes("Node Flow Rules")
      ? initInfoFormConfig.concat(ruleConfig)
      : initInfoFormConfig;
  }

  return {
    infoFormConfig,
    editRulesTableFormRef,
  };
};

export default useFormConfig;
