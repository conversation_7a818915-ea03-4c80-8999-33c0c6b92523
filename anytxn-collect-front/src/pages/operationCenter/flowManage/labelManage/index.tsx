// 业务參數/參數/授權/授權回應碼參數
import React, { Suspense, useState } from "react";
import { pageConfig, dictEnum } from "./pageConfig";
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
} from "@/constants/publicConstant";
import useFormConfig from "./useFormConfig";
import { message, Space, Upload } from "antd";
import { UploadOutlined } from "@ant-design/icons";
import { GradientButton } from "@/components";
import useIntlCustom from "@/hooks/useIntlCustom";
const PageTemplate = React.lazy(
  () => import("@/components/templates/PageTemplate")
);

const LabelManagePage: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const { translate } = useIntlCustom();
  const resetValue = { tagName: "", tagId: "", status: null };
  const [detailData, setDetailData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const [ruleData, setRuleData] = useState({});
  const { infoFormConfig, editRulesTableFormRef } = useFormConfig(
    type,
    ruleData,
    detailData
  );

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: "paramIndex",
    showPagination: true,
    optionList: [
      {
        type: OPERATE_TYPE.dowload,
        onCustomize: (visible) => message.success("下载成功"),
      },
      { type: OPERATE_TYPE.detail },
      { type: OPERATE_TYPE.edit },
      { type: OPERATE_TYPE.delete },
    ],
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: { ...detailData, nodeRuleype: ["Full Repayment Strategy"] },
    intlPrefix: prefix,
    props: {
      onChange: (val, aaa) => {
        console.log("2222--- setRuleData(val)", val, aaa), setRuleData(val);
      },
      dispatchManageList: editRulesTableFormRef,
    },
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({
      ...row,
      nodeAttriId: row?.nodeAttriId?.split(","),
    });
    setType(editType);
    if (editType === OPERATE_TYPE.list) {
      setRuleData({});
    }
  };

  // 渲染按钮
  const cardExtra = () => {
    return (
      <Space style={{ margin: "0 12px" }}>
        <Upload>
          <GradientButton icon={<UploadOutlined />}>
            {translate(I18N_COMON_PAGENAME.COMMON, "upload")}
          </GradientButton>
        </Upload>
      </Space>
    );
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
        extra={cardExtra()}
      />
    </Suspense>
  );
};
export default React.memo(LabelManagePage);
