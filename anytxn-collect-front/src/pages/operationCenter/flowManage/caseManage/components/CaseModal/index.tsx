import React, { useState } from "react";
import CommonModal from "@/components/commonModal";
import { Table } from "antd";
import mockData from "../../configData";

const CaseModal = ({ visible, onCancel }) => {
  // 弹窗节点内容
  const content = () => (
    <Table
      rowKey="id"
      columns={mockData.columns.CALL_PERRSON_MODAL}
      dataSource={mockData.tableData.CALL_PERRSON_MODAL}
      rowSelection={{
        type: "radio",
        onChange: (selectedRowKeys: any, selectedRows: any) => {
          // console.log('33333--selectedRows--table', selectedRowKeys, selectedRows[0]);
        },
      }}
    />
  );
  return (
    <CommonModal
      type="CALL_PERRSON_MODAL"
      open={visible}
      content={content}
      onClose={() => onCancel(false)}
      onSubmit={() => onCancel(false)}
    />
  );
};
export default React.memo(CaseModal);
