import React, { useEffect, useState } from "react";
import { Drawer, Table, Tabs } from "antd";
import mockData from "../../configData";
import commonService from "@/services/common";
import useIntlCustom from "@/hooks/useIntlCustom";
import { I18N_COMON_PAGENAME } from "@/constants/publicConstant";

const prefix = I18N_COMON_PAGENAME.CALL_PARAM; // 定义国际化前缀

const CaseDrawer = ({ visible, detailData, onCancel }) => {
  const { translate } = useIntlCustom();
  const [activeKey, setActiveKey] = useState<string>("phoneCall");
  const [dataSource, setDatasource] = useState<any>({
    phoneCall: [],
    messageCall: [],
    repayment: [],
  });
  const tabsItems = [
    {
      key: "phoneCall",
      label: translate(prefix, "phoneCallRecord"), // 电话催收
      children: (
        <Table
          rowKey="id"
          scroll={{ x: "max-content" }}
          columns={mockData.columns.PHONE_CALL_COLUMNS}
          dataSource={dataSource[activeKey]}
        />
      ),
    },
    {
      key: "messageCall",
      label: translate(prefix, "messageCallRecord"), // 短信催收
      children: (
        <Table
          rowKey="id"
          scroll={{ x: "max-content" }}
          columns={mockData.columns.MSG_CALL_COLUMNS}
          dataSource={dataSource[activeKey]}
        />
      ),
    },
    {
      key: "repayment",
      label: translate(prefix, "repaymentRecord"), // 还款记录
      children: (
        <Table
          rowKey="id"
          scroll={{ x: "max-content" }}
          columns={mockData.columns.REPAYMENT_COLUMNS}
          dataSource={dataSource[activeKey]}
        />
      ),
    },
  ];
  useEffect(() => {
    getTableData();
  }, [activeKey]);

  // 获取列表数据
  const getTableData = async () => {
    const params = {
      url: { URL: mockData.URL[activeKey] },
      caseCode: detailData?.caseCode,
    };
    const res: any = await commonService.getEditPostBiz(params);
    setDatasource({
      ...dataSource,
      [activeKey]: res.data,
    });
  };

  // 弹窗节点内容
  const content = () => (
    <Tabs
      defaultActiveKey="phoneCall"
      activeKey={activeKey}
      items={tabsItems}
      onChange={(val: string) => setActiveKey(val)}
    />
  );

  return (
    <Drawer
      open={visible}
      title={translate(prefix, "collectionRecord")} // 催收记录
      width="80%"
      onClose={() => onCancel(false)}
    >
      {content()}
    </Drawer>
  );
};
export default React.memo(CaseDrawer);
