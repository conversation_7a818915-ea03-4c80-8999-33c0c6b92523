// 规则引擎-规则管理-授权
import { FC } from 'react';
import urlConstants from '@/constants/urlConstants';
import RulesPage from '../components/RulesPage';
import { optionList } from './pageConfig';

const AuthRule: FC = () => {
  return (
    <RulesPage
      ruleType="auth"
      cardTitle="authRule"
      urlObj={urlConstants.RULES_AUTH}
      defOptionList={optionList}
    />
  );
};

export default AuthRule;
