import { IUrlObj } from '@/materials/rules/types/IRules';
import { ICommonTableActionItem, IFormActionPermissionObj } from '@/types/ICommon';

/**
 * rulesPage的props约束接口
 */
export interface IRulesPageProps {
  /**
   * 规则类型
   */
  ruleType: 'limit' | 'auth';
  /**
   * 标题
   */
  cardTitle: string;
  /**
   * 接口对象
   */
  urlObj: IUrlObj;
  /**
   * 操作列配置
   */
  optionList?: ICommonTableActionItem[];
  /**
   * 操作列配置
   */
  defOptionList?: ICommonTableActionItem[];
  /**
   * 操作栏权限配置
   */
  formActionPermissionObj?: IFormActionPermissionObj;
}
