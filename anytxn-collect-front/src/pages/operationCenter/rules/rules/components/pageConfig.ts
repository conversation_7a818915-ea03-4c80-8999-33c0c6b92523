import { I18N_COMON_PAGENAME, OPERATE_TYPE } from '@/constants/publicConstant';

// 国际化前缀
export const prefix = I18N_COMON_PAGENAME.RULES;

// 重置查询条件
export const resetValue = { ruleType: null, ruleId: '', ruleName: '' };

// 操作列配置
export const optionList = [OPERATE_TYPE.detail, OPERATE_TYPE.copy, OPERATE_TYPE.edit];
// 传递给antv/table的参数
export const tableProps = {
  scroll: {
    x: 1500,
    y: 2000,
  },
};