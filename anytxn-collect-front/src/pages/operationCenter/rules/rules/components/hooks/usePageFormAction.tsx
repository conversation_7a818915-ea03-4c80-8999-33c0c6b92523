// formAction组件hooks
import { FormAction } from "@/components";
import { OPERATE_TYPE } from "@/constants/publicConstant";

const usePageFormAction = ({
  detailRef,
  extra,
  detailType,
  formActionPermissionObj,
  getTableData,
  handleCardBack,
  handleAction,
}) => {
  // 表单提交事件
  const handleCardSubmit = async () => {
    if (detailRef.current) {
      const res = await detailRef.current.onSubmit();
      if (res) {
        handleCardBack();
        getTableData();
      }
    }
  };

  // 渲染formAction组件
  const renderFormAction = () => {
    return [OPERATE_TYPE.detail, OPERATE_TYPE.list].includes(detailType) ? (
      <FormAction
        showCreate={detailType === OPERATE_TYPE.list}
        showSubmit={false}
        extra={extra()}
        permissionObj={formActionPermissionObj}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    ) : (
      <FormAction
        showSubmit
        showCreate={false}
        permissionObj={formActionPermissionObj}
        onSubmit={handleCardSubmit}
      />
    );
  };

  return { renderFormAction };
};

export default usePageFormAction;
