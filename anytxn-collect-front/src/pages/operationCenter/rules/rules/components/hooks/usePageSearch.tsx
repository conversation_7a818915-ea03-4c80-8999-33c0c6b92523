// search组件hooks
import { useState } from 'react';
import _ from 'lodash';
import { Search } from '@/components';
import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { prefix, resetValue } from '../pageConfig';

const usePageSearch = ({ ruleType, searchBtnDisabled, getTableData, handleAction }) => {
  // hooks变量
  const [searchParam, setSearchParam] = useState<any>({ ...resetValue });
  const ruleTypeList = _.get(_.find(DICT_CONSTANTS.RULE_TYPE, { key: ruleType }), 'children', []);
  // 查询条件字段
  const searchSource = [
    {
      value: 'ruleName',
      label: 'ruleName',
      type: COMPONENT_TYPE.INPUT,
      showCount: true,
      maxLength: 25,
    },
    {
      value: 'ruleId',
      label: 'ruleId',
      type: COMPONENT_TYPE.INPUT,
      showCount: true,
      maxLength: 20,
    },
    // {
    //   value: 'ruleType',
    //   label: 'ruleType',
    //   type: COMPONENT_TYPE.SELECT,
    //   data: ruleTypeList,
    //   showKey: false,
    //   width: 200,
    //   rules: [{ required: true }],
    // },
  ];

  // 查询事件
  const handleSearch = (searchValue: object): void => {
    setSearchParam(searchValue);
    getTableData({ searchValue });
  };

  // 渲染search组件
  const searchChildren = () => {
    return (
      <Search
        searchValue={searchParam}
        resetValue={resetValue}
        searchSource={searchSource}
        intlPrefix={prefix}
        buttonDisabled={searchBtnDisabled}
        onSearch={handleSearch}
        onCreate={() => handleAction(OPERATE_TYPE.create, {})}
      />
    );
  };

  return {
    searchParam,
    searchChildren,
  };
};

export default usePageSearch;
