import React, { useRef, useState } from "react";
import { Input, Button, message, Form, Select } from "antd";
import styled from "styled-components";
import { useForm } from "antd/es/form/Form";
import useIntlCustom from "@/hooks/useIntlCustom";

// 使用styled-components创建样式组件
const Container = styled.div`
  width: 100%;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const FormItem = styled.div`
  margin-bottom: 24px;

  .label {
    font-size: 14px;
    color: #4caf50;
    margin-bottom: 8px;
  }

  .ant-input {
    border-radius: 4px;
  }
`;

const StyledButton = styled(Button)`
  background: #7e57c2;
  margin-bottom: 24px;
  &:hover {
    background: #673ab7;
  }
`;

// 规则AI检查组件
const RuleAICheck = ({ isShowAiform }) => {
  const { translate } = useIntlCustom();
  const [content, setContent] = useState("");
  const formRef = useRef(null);
  const [form] = useForm();

  // 处理检查按钮点击
  const handleCheck = () => {
    // TODO: 实现检查逻辑
    message.success(translate('callParam', 'startCheckMessage'));
  };

  // 规则ai案件筛选项
  const formAiContent = () => {
    if (!isShowAiform) return;
    return (
      <Form
        ref={formRef}
        form={form}
        name="basic"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        autoComplete="off"
      >
        <Form.Item 
          label={translate('callParam', 'ruleTypeLabel')} 
          name="node" 
          rules={[{ required: true }]}
        >
          <Select
            placeholder={translate('callParam', 'selectPlaceholder')}
            style={{ width: 200 }}
            options={[
              { label: translate('callParam', 'nodeFlowRule'), value: "node" },
              { label: translate('callParam', 'labelMatchRule'), value: "label" },
              { label: translate('callParam', 'labelHandleRule'), value: "labelHandle" },
            ]}
          />
        </Form.Item>
      </Form>
    );
  };

  return (
    <Container>
      {formAiContent()}
      <StyledButton type="primary" size="large" onClick={handleCheck}>
        {translate('callParam', 'startCheck')}
      </StyledButton>

      <FormItem>
        <div className="label">{translate('callParam', 'checkResult')}</div>
        <Input.TextArea
          rows={6}
          value={content}
          onChange={(e) => setContent(e.target.value)}
        />
      </FormItem>
    </Container>
  );
};

export default RuleAICheck;
