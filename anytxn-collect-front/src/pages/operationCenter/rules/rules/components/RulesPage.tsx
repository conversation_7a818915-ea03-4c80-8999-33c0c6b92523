// 规则引擎-规则管理
import { FC, useState, useRef, ReactNode, lazy, useEffect } from "react";
import _ from "lodash";
import { Space, type TablePaginationConfig } from "antd";
import {
  OPERATE_TYPE,
  COMPONENT_TYPE,
  DEFAULT_PAGINATION,
} from "@/constants/publicConstant";
import { GradientButton, LayoutTemplate } from "@/components";
import CommonModal from "@/components/commonModal";
import common from "@/services/common";
import useIntlCustom from "@/hooks/useIntlCustom";
import { IDetailData } from "@/materials/rules/types/IRules";
import { setRulesDetailData } from "@/materials/rules/util/rulesUtil";
import AiContent from "./AiContent";
import { IRulesPageProps } from "./IRules";
import { prefix } from "./pageConfig";
import usePageSearch from "./hooks/usePageSearch";
import usePageTable from "./hooks/usePageTable";
import usePageFormAction from "./hooks/usePageFormAction";

const Detail = lazy(() => import("@/materials/rules"));

const RulesPage: FC<IRulesPageProps> = ({
  ruleType,
  cardTitle,
  urlObj,
  defOptionList,
  formActionPermissionObj,
}) => {
  const { translate } = useIntlCustom();
  // hooks变量
  const [listData, setListData] = useState<IDetailData[]>([]);
  const [detailType, setDetailType] = useState<string>(OPERATE_TYPE.list);
  const [detail, setDetail] = useState<IDetailData | object>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [childrenType, setChildrenType] = useState<string>(
    COMPONENT_TYPE.TABLE
  );
  const [cardShowBack, setCardShowBack] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<
    TablePaginationConfig | false
  >(false);
  const [searchBtnDisabled, setSearchBtnDisabled] = useState<boolean>(false);
  const [open, setOpen] = useState<boolean>(false);
  const [isShowAiform, setIsShowAiform] = useState<boolean>(false);
  const detailRef = useRef<any>(null);
  // @ts-ignore
  const optionList = defOptionList.concat({
    type: OPERATE_TYPE.aiHelp,
    onCustomize: (val) => {
      setIsShowAiform(false), setOpen(val);
    },
  });

  useEffect(() => {
    getTableData();
  }, []);

  // 获取表格数据
  const getTableData = async ({
    searchValue = searchParam,
    pagination = { ...DEFAULT_PAGINATION },
  } = {}): Promise<void> => {
    // if (!searchValue?.ruleType) {
    //   return;
    // }
    setLoading(true);
    try {
      const res = await common.getTableListData({
        searchValue,
        pagination,
        url: urlObj.LIST,
      });
      const { data, total } = res;
      setListData(data);
      total &&
        setPaginationConfig({
          showSizeChanger: true,
          showQuickJumper: true,
          total,
        });
    } catch (error) {
      setListData([]);
    } finally {
      setLoading(false);
    }
  };

  // 事件处理
  const handleAction = (type: string, row: IDetailData): void => {
    // 是否展示自定义操作按钮内容
    const isOnCustomize: any = optionList?.find(
      (item: any) => item?.type === type
    );
    if (isOnCustomize?.onCustomize) {
      return isOnCustomize.onCustomize(true);
    }
    setCardShowBack(true);
    setDetailType(type);
    setChildrenType(COMPONENT_TYPE.FORM);
    const detailData = setRulesDetailData(row);
    console.log("22222----detailData", detailData);
    setDetail(detailData);
    setSearchBtnDisabled(true);
  };

  // 返回事件
  const handleCardBack = (): void => {
    setChildrenType(COMPONENT_TYPE.TABLE);
    setDetailType(OPERATE_TYPE.list);
    setCardShowBack(false);
    setSearchBtnDisabled(false);
  };

  // search组件hooks
  const { searchParam, searchChildren } = usePageSearch({
    ruleType,
    searchBtnDisabled,
    getTableData,
    handleAction,
  });

  // table组件hooks
  const { tableChildren } = usePageTable({
    ruleType,
    listData,
    loading,
    paginationConfig,
    optionList,
    getTableData,
    handleAction,
  });

  // 渲染按钮
  const extra = () => {
    return (
      <Space style={{ margin: "0 12px" }}>
        <GradientButton
          onClick={() => {
            setIsShowAiform(true), setOpen(true);
          }}
        >
          {translate("callParam", "aiRulesCheck")}
        </GradientButton>
      </Space>
    );
  };

  // formAction组件hooks
  const { renderFormAction } = usePageFormAction({
    detailRef,
    extra,
    detailType,
    formActionPermissionObj,
    getTableData,
    handleCardBack,
    handleAction,
  });

  // 详情组件
  const formChildren = (): ReactNode => {
    return (
      <Detail
        ref={detailRef}
        data={detail as IDetailData}
        ruleType={ruleType}
        type={detailType}
        urlObj={urlObj}
      />
    );
  };

  return (
    <>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={formChildren()}
        childrenType={childrenType}
        type={detailType}
        intlPrefix={prefix}
        cardShowBack={cardShowBack}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
        onCardBack={handleCardBack}
      />
      {open && (
        <CommonModal
          type="AI_HELP_MODAL"
          open={open}
          content={() => <AiContent isShowAiform={isShowAiform} />}
          onClose={() => setOpen(false)}
          onSubmit={() => setOpen(false)}
        />
      )}
    </>
  );
};

export default RulesPage;
