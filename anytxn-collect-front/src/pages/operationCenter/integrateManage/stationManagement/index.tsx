// 业务參數/參數/授權/授權回應碼參數
import React, { Suspense, useState } from "react";
import { pageConfig, dictEnum } from "./pageConfig";
import { OPERATE_TYPE } from "@/constants/publicConstant";
import useFormConfig from "./useFormConfig";
import mockData from "./mockData";

const PageTemplate = React.lazy(
  () => import("@/components/templates/PageTemplate")
);

const StationManagement: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = { weightCoefficient: "", nodeAttriType: null };
  const [detailData, setDetailData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const { infoFormConfig } = useFormConfig(type);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    dataSource: mockData.listData,
    rowKey: "paramIndex",
    showPagination: true,
    optionList: [OPERATE_TYPE.detail, OPERATE_TYPE.edit],
  };

  // 表单配置
  const formConfig = {
    config: infoFormConfig,
    data: detailData,
    intlPrefix: prefix,
  };

  // 操作按钮配置
  const formActionConfig = {
    showCreate: false,
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({ ...row });
    setType(editType);
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
        // formActionConfig={formActionConfig}
      />
    </Suspense>
  );
};
export default React.memo(StationManagement);
