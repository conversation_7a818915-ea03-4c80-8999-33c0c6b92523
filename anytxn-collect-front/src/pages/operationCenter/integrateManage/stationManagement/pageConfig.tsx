import DICT_CONSTANTS from "@/constants/dictConstants";
import {
  COMPONENT_TYPE,
  RENDER_TYPE,
  I18N_COMON_PAGENAME,
} from "@/constants/publicConstant";
import urlConstants from "@/constants/urlConstants";
import { getRequestData } from "@/utils/urlUtil";

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: "stationManagement",
  // 页面接口请求
  urlObj: {
    list: urlConstants.STATION_MANAGEMENT.LIST,
    create: urlConstants.STATION_MANAGEMENT.CREATE,
    edit: urlConstants.STATION_MANAGEMENT.EDIT,
    delete: urlConstants.STATION_MANAGEMENT.DELETE,
    serviceType: "business",
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: "position",
      label: "position",
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.STATION,
    },
    {
      value: "weightCoefficient",
      label: "weightCoefficient",
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: "paramIndex",
      dataIndex: "paramIndex",
      key: "paramIndex",
      width: 80,
    },
    {
      title: "stationId",
      dataIndex: "stationId",
      key: "stationId",
    },
    {
      title: "position",
      dataIndex: "position",
      key: "position",
    },
    {
      title: "weightCoefficient",
      dataIndex: "weightCoefficient",
      key: "weightCoefficient",
      width: 120,
    },
    {
      key: "updateUser",
      title: "updateUser",
      dataIndex: "updateUser",
      width: 150,
    },
    {
      key: "updateTime",
      title: "updateTime",
      dataIndex: "updateTime",
      width: 160,
      valueType: RENDER_TYPE.DateTime,
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
