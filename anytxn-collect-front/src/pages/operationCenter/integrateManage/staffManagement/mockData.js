import { intlConst } from "@/hooks/useIntlCustom";

const prefix = "callParam";

export default {
  // 获取人员管理列表
  listData: [
    {
      paramIndex: "1",
      stationId: "ST001",
      staffName: "JACK",
      position: intlConst.formatMessage(prefix, "customerManager"),
      weightCoefficient: 1,
      updateUser: "admin",
      updateTime: "2024-03-15 10:30:25",
    },
    {
      paramIndex: "2",
      stationId: "ST002",
      staffName: "SPEED",
      position: intlConst.formatMessage(prefix, "collectionQA"),
      weightCoefficient: 1.2,
      updateUser: "admin",
      updateTime: "2024-03-15 11:20:15",
    },
    {
      paramIndex: "3",
      stationId: "ST003",
      staffName: "TOM",
      position: intlConst.formatMessage(prefix, "collectionSpecialist"),
      weightCoefficient: 1.5,
      updateUser: "system",
      updateTime: "2024-03-15 14:15:30",
    },
    {
      paramIndex: "4",
      stationId: "ST004",
      staffName: "SARAH",
      position: intlConst.formatMessage(prefix, "seniorCollector"),
      weightCoefficient: 2.5,
      updateUser: "admin",
      updateTime: "2024-03-15 16:45:10",
    },
    {
      paramIndex: "5",
      stationId: "ST005",
      staffName: "MIKE",
      position: intlConst.formatMessage(prefix, "expertCollector"),
      weightCoefficient: 1.8,
      updateUser: "system",
      updateTime: "2024-03-15 17:30:45",
    },
    {
      paramIndex: "6",
      stationId: "ST006",
      staffName: "TLUCY",
      position: intlConst.formatMessage(prefix, "collectionSupervisor"),
      weightCoefficient: 1.2,
      updateUser: "admin",
      updateTime: "2024-03-16 09:15:20",
    },
  ],
};
