/**
 * Created by ca<PERSON><PERSON> on 2025/6/6.
 */

import React, { useState } from "react";
import _ from "lodash";
import Cookies from "js-cookie";
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  InteractionOutlined,
  UserSwitchOutlined,
} from "@ant-design/icons";
import { Form, Input, Checkbox, Button, notification, Select } from "antd";
import { postData } from "@/utils/serviceUtil";
import Logo from "@/assets/images/logo2.png";
import { SESSION } from "@/constants/publicConstant";
import styles from "./index.module.css";
import useIntlCustom from "@/hooks/useIntlCustom";

const cname = Cookies.get("username") || "";
const cpwd = Cookies.get("password") || "";
const crole = Cookies.get("role") || "";
const cstation = Cookies.get("station") || "";
const inputStyle = { width: "320px" };

export default function Login() {
  const { translate } = useIntlCustom();
  const prefix = "login";
  // 内部变量
  const [loginData, setLoginData] = useState({
    username: cname,
    password: cpwd,
    role: crole,
    station: cstation,
    remember: !!cname,
  });

  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  // 事件处理
  const handleValuesChange = (changedValues, allValues) => {
    setLoginData(allValues);
    Cookies.set("role", allValues?.role);
    Cookies.set("station", allValues?.station);
  };

  const handleLogin = () => {
    form
      .validateFields()
      .then(async () => {
        // 1、登录，获取token
        // const result = await postData('/login', loginData);
        const result = { data: "123" };
        const token = result?.data;
        if (token) {
          sessionStorage.setItem(SESSION.token, token);
          sessionStorage.setItem(SESSION.codeIndex, "user");
          window.location.href = "/userCenter/workBench/workBench";
        } else {
          notification.error({ message: translate(prefix, "error.invalid") });
        }
      })
      .catch((err) => {
        console.log("Validation failed", err);
      });
  };

  const handleEntryPress = (e) => {
    if (e.key === "Enter") {
      handleLogin();
    }
  };

  return (
    <div className={styles.login}>
      <div className={styles.left}>
        <div className={styles.logo}>
          <img alt="logo" src={Logo} />
        </div>
        <div className={styles.desc}>
          <h1>{translate(prefix, "title")}</h1>
          <p>{translate(prefix, "description")}</p>
        </div>
        <div className={styles.copy}>
          <span>{translate(prefix, "copyright")}</span>
        </div>
      </div>
      <div className={styles.right}>
        <h2>{translate(prefix, "title")}</h2>
        <Form
          form={form}
          initialValues={loginData}
          layout="vertical"
          onValuesChange={_.debounce(handleValuesChange)}
        >
          <Form.Item
            name="username"
            label={translate(prefix, "username")}
            rules={[
              { required: true },
              {
                max: 30,
                message: translate(prefix, "validation.username.max"),
              },
            ]}
          >
            <Input
              allowClear
              placeholder={translate(prefix, "username.placeholder")}
              prefix={<UserOutlined />}
              style={inputStyle}
            />
          </Form.Item>
          <Form.Item
            name="password"
            label={translate(prefix, "password")}
            rules={[
              { required: true },
              {
                max: 30,
                message: translate(prefix, "validation.password.max"),
              },
              { min: 6, message: translate(prefix, "validation.password.min") },
            ]}
          >
            <Input.Password
              placeholder={translate(prefix, "password.placeholder")}
              prefix={<LockOutlined />}
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
              style={inputStyle}
              // onKeyDown={handleEntryPress}
            />
          </Form.Item>
          <Form.Item
            name="role"
            label={translate(prefix, "role")}
            rules={[{ required: true }]}
          >
            <Select
              allowClear
              style={inputStyle}
              prefix={<UserSwitchOutlined />}
              placeholder={translate(prefix, "role.placeholder")}
              options={[{ label: "Moon", value: "1" }]}
            />
          </Form.Item>
          <Form.Item
            name="station"
            label={translate(prefix, "station")}
            rules={[{ required: true }]}
          >
            <Select
              allowClear
              style={inputStyle}
              prefix={<InteractionOutlined />}
              placeholder={translate(prefix, "station.placeholder")}
              options={[{ label: "Speed", value: "2" }]}
            />
          </Form.Item>
          <div className="flex-row flex-align-center m-b">
            <Form.Item name="remember" valuePropName="checked" noStyle>
              <Checkbox className="primary-color">
                {translate(prefix, "remember")}
              </Checkbox>
            </Form.Item>
            {/* <a href="#/register" rel="external nofollow" className="m-l">
              {translate(prefix, "register")}
            </a> */}
          </div>
          <Button
            type="primary"
            shape="round"
            loading={loading}
            style={inputStyle}
            className="btn-color"
            onClick={handleLogin}
          >
            {loading
              ? translate(prefix, "button.loading")
              : translate(prefix, "button")}
          </Button>
        </Form>
      </div>
    </div>
  );
}
