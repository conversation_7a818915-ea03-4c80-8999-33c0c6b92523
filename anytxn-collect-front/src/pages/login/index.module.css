.login {
  height: 100%;
  display: flex;

  > .left {
    flex: 1 0 50%;
    position: relative;
    background: url(../../assets/images/bg-login.png) no-repeat;
    background-size: 100% 100%;
    background-position: 0 100%;

    > .logo {
      position: relative;
      top: 10%;
      left: 4%;
    }
    > .desc {
      position: relative;
      top: 20%;
      left: 8%;
      color: #fff;
      width: 60%;
      user-select: none;
      > h1 {
        color: #fff;
        font-size: 1.8rem;
      }
      > p {
        line-height: 1.8rem;
        font-size: 1rem;
        padding-top: 2%;
        opacity: 0.8;
      }
    }
    > .copy {
      position: absolute;
      bottom: 1rem;
      width: 90%;
      text-align: center;
      color: #fff;
      opacity: 0.7;
      user-select: none;
    }
  }
  > .right {
    display: flex;
    flex-direction: column;
    flex: 1 0 50%;
    align-items: center;
    justify-content: center;
    background: linear-gradient(rgba(24, 144, 255, 0.2), #fff);

    > h2 {
      margin-bottom: 2rem;
      font-size: 1.8rem;
    }
  }
}
