// 业务參數/用户管理
import React, { Suspense, useState } from "react";
import { pageConfig, dictEnum } from "./pageConfig";
import { OPERATE_TYPE } from "@/constants/publicConstant";
import useFormConfig from "./useFormConfig";

const PageTemplate = React.lazy(
  () => import("@/components/templates/PageTemplate")
);

const userManagePage: React.FC = () => {
  const { urlObj, prefix, searchSource, columns, cardTitle } = pageConfig;
  const resetValue = { tagName: "", tagId: "" };
  const [detailData, setDetailData] = useState<any>({});
  const [type, setType] = useState<string>(OPERATE_TYPE.list);
  const { InfoFormConfig } = useFormConfig(type, detailData);

  // 查询条件配置
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };

  // 表格配置
  const tableConfig = {
    columns,
    rowKey: "paramIndex",
    showPagination: true,
    // optionList: [
    //   {
    //     type: OPERATE_TYPE.dowload,
    //     onCustomize: (visible) => message.success("下载成功"),
    //   },
    //   { type: OPERATE_TYPE.detail },
    //   { type: OPERATE_TYPE.edit },
    //   { type: OPERATE_TYPE.delete },
    // ],
  };

  // 表单配置
  const formConfig = {
    config: InfoFormConfig,
    data: { ...detailData },
    intlPrefix: prefix,
  };

  // 列表按钮操作
  const handleAction = (editType: string, row: any) => {
    setDetailData({ ...row });
    setType(editType);
  };

  return (
    <Suspense>
      <PageTemplate
        searchConfig={searchConfig}
        tableConfig={tableConfig}
        formConfig={formConfig}
        urlObj={urlObj}
        cardTitle={cardTitle}
        intlPrefix={prefix}
        dictEnum={dictEnum}
        onAction={handleAction}
      />
    </Suspense>
  );
};
export default React.memo(userManagePage);
