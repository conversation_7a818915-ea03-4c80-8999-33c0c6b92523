import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  OPERATE_TYPE,
} from "@/constants/publicConstant";
import urlConstants from "@/constants/urlConstants";
import { getRequestData } from "@/utils/urlUtil";

// const getRequestData = (postData, type: string, rowData): object => {
//   switch (type) {
//     case OPERATE_TYPE.edit:
//     case OPERATE_TYPE.create:
//       return {
//         ...postData.formData,
//       };
//     default:
//       return { ...postData };
//   }
// };

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.AUTH_MANAGE,
  cardTitle: "positionManage",
  // 页面接口请求
  urlObj: {
    list: urlConstants.LABEL_MANAGE.LIST,
    create: urlConstants.LABEL_MANAGE.CREATE,
    edit: urlConstants.LABEL_MANAGE.EDIT,
    delete: urlConstants.LABEL_MANAGE.delete,
    serviceType: "business",
    getRequestData,
  },
  // 搜索
  searchSource: [
    {
      value: "postId",
      label: "postId",
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: "postName",
      label: "postName",
      type: COMPONENT_TYPE.INPUT,
    },
  ],

  // 列表
  columns: [
    {
      title: "paramIndex",
      dataIndex: "paramIndex",
      key: "paramIndex",
      prefix: I18N_COMON_PAGENAME.COMMON,
      width: 80,
    },
    {
      title: "postId",
      dataIndex: "postId",
      key: "postId",
      width: 100,
    },
    {
      title: "postName",
      dataIndex: "postName",
      key: "postName",
      width: 150,
    },
    {
      title: "superiorPost",
      dataIndex: "superiorPost",
      key: "superiorPost",
      width: 150,
    },
    {
      title: "postDesc",
      dataIndex: "postDesc",
      key: "postDesc",
      width: 250,
      render: (text) => (
        <div style={{ wordWrap: "break-word", whiteSpace: "normal" }}>
          {text}
        </div>
      ),
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {};
