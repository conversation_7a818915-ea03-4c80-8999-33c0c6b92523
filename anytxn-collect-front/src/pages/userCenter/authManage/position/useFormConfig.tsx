import {
  COMPONENT_TYPE,
  FORMITEM_TYPE,
  I18N_COMON_PAGENAME,
} from "@/constants/publicConstant";
import DICT_CONSTANTS from "@/constants/dictConstants";
import { IFormConfig } from "@/types/IForm";

const useFormConfig = (type: string, detailData) => {
  // 表单字段
  const InfoFormConfig: Array<IFormConfig> = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "baseInfo",
      prefix: I18N_COMON_PAGENAME.COMMON,
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: "postId",
          label: "postId",
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: "postName",
          label: "postName",
          rules: [{ required: true }],
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "superiorPost",
          label: "superiorPost",
          rules: [{ required: true }],
        },
      ],
    },
  ];

  return {
    InfoFormConfig,
  };
};

export default useFormConfig;
