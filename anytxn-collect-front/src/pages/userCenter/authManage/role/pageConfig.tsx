import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  RENDER_TYPE,
} from "@/constants/publicConstant";
import DICT_CONSTANTS from "@/constants/dictConstants";

// 角色状态枚举
export const roleStatusEunm = [
  { key: "Y", label: "enable" },
  { key: "N", label: "unEnable" },
];

// 挂载状态枚举
export const mountstatusEunm = [
  { key: "Y", label: "mounted" },
  { key: "N", label: "unmounted" },
];

export const pageConfig = {
  // 国际化前缀
  prefix: I18N_COMON_PAGENAME.AUTH_MANAGE,
  // 查询条件字段
  searchSource: [
    {
      value: "roleName",
      label: "roleName",
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: "roleStatus",
      label: "roleStatus",
      type: COMPONENT_TYPE.SELECT,
      data: roleStatusEunm.map((item) => ({
        key: item.key,
        value: item.label,
      })),
    },
    {
      value: "mountStatus",
      label: "mountStatus",
      type: COMPONENT_TYPE.SELECT,
      data: mountstatusEunm.map((item) => ({
        key: item.key,
        value: item.label,
      })),
    },
  ],
  // 页面标题
  cardTitle: "role",
  // 列表字段
  columns: [
    {
      title: "roleId",
      dataIndex: "roleId",
      key: "roleId",
      width: 60,
    },
    {
      title: "roleName",
      dataIndex: "roleName",
      key: "roleName",
      width: 200,
    },
    {
      key: "roleStatus",
      dataIndex: "roleStatus",
      title: "roleStatus",
      width: 80,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
    {
      key: "mountStatus",
      dataIndex: "mountStatus",
      title: "mountStatus",
      width: 100,
      valueType: RENDER_TYPE.MockDictionary,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],
  // 新增按钮权限
  formActionPermissionObj: {},
};
