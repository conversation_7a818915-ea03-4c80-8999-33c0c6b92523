import { memo, useEffect, useState, FC } from "react";
import _ from "lodash";
import useIntlCustom from "@/hooks/useIntlCustom";
import PageTemplate from "@/components/templates/PageTemplate";
import {
  COMPONENT_TYPE,
  FORMITEM_TYPE,
  OPERATE_TYPE,
} from "@/constants/publicConstant";
import { EyeOutlined, ProfileOutlined } from "@ant-design/icons";
// import { getList, getMountDataById } from '@/services/role';
import { getTreeList, getMenuInfo } from "@/services/menu";
import services from "@/services";
import urlConstants from "@/constants/urlConstants";
import { roleStatusEunm, pageConfig } from "./pageConfig";

const { prefix, formActionPermissionObj, searchSource, columns } = pageConfig;

const Role: FC = () => {
  // hook变量
  const { translate } = useIntlCustom();
  const [detailType, setDetailType] = useState<string>("");
  const [detailData, setDetailData] = useState<any>({});
  const [mountDetail, setMountDetail] = useState({ roleId: "" });
  const [roleEunm, setRoleEunm] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);
  const [menuList, setMenuList] = useState([]);
  const [menuTree, setMenuTree] = useState([]);
  // 内部变量
  const expandKeyList: any = [];
  const checkKeyList: any = [];
  const resetValue = {
    roleName: "",
    roleStatus: "",
    mountStatus: "",
  };
  const searchConfig = {
    searchSource,
    searchValue: { ...resetValue },
    resetValue,
  };
  const tableConfig = {
    columns,
    rowKey: "roleId",
    optionList: [
      { type: OPERATE_TYPE.edit },
      {
        type: "mountDetail",
        title: translate(prefix, "viewMount"),
        icon: <EyeOutlined />,
      },
      {
        type: "editMount",
        title: translate(prefix, "editMount"),
        icon: <ProfileOutlined />,
      },
    ],
    showPagination: true,
  };

  const detailFormConfig = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "role",
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: "roleId",
          label: "roleId",
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
          disabled: detailType === OPERATE_TYPE.edit,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: "roleName",
          label: "roleName",
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
        },
        {
          type: COMPONENT_TYPE.TEXTAREA,
          name: "roleDesc",
          label: "roleDesc",
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
        },
        {
          type: COMPONENT_TYPE.RADIO,
          name: "roleStatus",
          label: "roleStatus",
          rules: [{ required: true }],
          data: roleStatusEunm.map((item) => ({
            value: item.label,
            key: item.key,
          })),
          showKey: false,
        },
      ],
    },
  ];

  const formConfig = {
    config: detailFormConfig,
    data: detailData,
    intlPrefix: prefix,
    onChange: () => {},
  };

  // 角色挂载表单配置
  const detailMountFormConfig = [
    {
      type: FORMITEM_TYPE.FormHearder,
      title: "mountDetail",
    },
    {
      type: FORMITEM_TYPE.Row,
      data: [
        {
          type: COMPONENT_TYPE.INPUT,
          name: "roleId",
          label: "roleId",
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.INPUT,
          name: "roleName",
          label: "roleName",
          rules: [{ required: true, max: 32 }],
          maxLength: 32,
          disabled: true,
        },
        {
          type: COMPONENT_TYPE.SELECT,
          name: "copyRole",
          label: "copyObject",
          disabled: detailType !== "editMount",
          isint: "0",
          data: roleEunm,
        },
        {
          name: "roleMenuList",
          label: "menuPermissions",
          type: COMPONENT_TYPE.TREE,
          defaultExpandParent: true,
          checkable: true,
          disabled: detailType !== "editMount",
          expandedKeys: expandedKeys,
          autoExpandParent: autoExpandParent,
          checkedKeys: checkedKeys,
          selectedKeys: selectedKeys,
          checkStrictly: true,
          // selectedKeys: 'workbench',
          data: treeData,
        },
      ],
    },
  ];

  const formActionConfig = {
    showSubmit: detailType !== "mountDetail",
    permissionObj: formActionPermissionObj,
  };

  // 处理请求数据
  const getRequestData = (postData, type: string) => {
    switch (type) {
      case OPERATE_TYPE.create:
        return { ...postData.formData, mountStatus: "Y" };
      case OPERATE_TYPE.edit:
        return { ...postData.formData };
      case "editMount":
        const { formData } = postData;
        // 根据选中菜单id数组生成对应权限id数组数据
        const permissionArr: any = [];
        // formData.roleMenuList.map((item: any) => {
        //   const permissionItem = menuList.filter((val: any) => val.menuId === item)[0].permissionId;
        //   permissionArr.push(permissionItem);
        // });
        const result = {
          roleId: formData.roleId,
          // permissionId: permissionArr,
        };
        return result;
      default:
        return { ...postData };
    }
  };

  // 页面接口请求
  const urlObj = {
    list: urlConstants.RULES_AUTH.LIST,
    edit: urlConstants.RULES_AUTH.EDIT,
    create: urlConstants.RULES_AUTH.CREATE,
    // editMount: urlConstants.RULES_AUTH.EDIT_MOUNT,
    serviceType: "business",
    getRequestData,
    customFunc: { editMount: services.common.getEditPostBiz },
  };

  // 副作用
  useEffect(() => {
    if (detailType === "editMount" || detailType === "mountDetail") {
      if (mountDetail && mountDetail.roleId) {
        // 获取表单基础数据
        getFormDataById(mountDetail, true);
        // 菜单数据逻辑处理，获取所有按钮菜单数据
        // 数据处理
        getMenuList();
      }
    }
    if (detailType === "editMount") {
      // 获取角色数据
      getRoleList(mountDetail.roleId);
    }
  }, [detailType]);

  // 逻辑处理
  const getMenuList = async () => {
    const param = { body: { menuName: "", menuStatus: "Y" } };
    // 获取平台所有菜单按钮权限
    try {
      const result: any = await getTreeList(param);
      if (result) {
        setMenuTree(result);
        const temp = getsysMenuTree(result);
        // 菜单数据设置
        setTreeData(temp);
        // 将第一级菜单数据展开
        setExpandedKeys(expandKeyList);
      }
      // 获取菜单扁平化数据
      const data: any = await getMenuInfo(param);
      if (data) {
        setMenuList(data);
      }
    } catch (e) {
      setMenuTree([]);
      setMenuList([]);
    }
  };

  const getFormDataById = async (param, key) => {
    const body = {
      roleId: param.roleId,
    };
    // let formData = await getMountDataById({ body });
    // if (formData) {
    //   getCheckKeys(formData);
    // }
  };

  const getsysMenuTree = (result) => {
    return result.map((item) => {
      const res = { ...item };
      let { menuId, menuName, children } = item;
      res.key = menuId;
      res.title = menuName;
      // 需要删除的
      const deleteList = [
        "menuId",
        "parentMenuId",
        "menuName",
        "menuRouteUrl",
        "iconId",
        "type",
        "menuType",
        "orderSeq",
        "level",
        "menuStatus",
      ];
      deleteList.forEach((item) => delete res?.[item]);
      if (children && children.length > 0) {
        item.level === 1 && expandKeyList.push(res.key);
        res.children = getsysMenuTree(children);
      }
      return res;
    });
  };

  // 角色菜单选中
  const getCheckKeys = (data) => {
    data.map((item) => {
      checkKeyList.push(item.incId);
    });
    setCheckedKeys(checkKeyList);
  };

  // 获取角色列表
  const getRoleList = async (param) => {
    const urlParam = {
      roleStatus: "1",
      mountStatus: "1",
    };
    // let res = await getList(urlParam);
    // const list: any = [];
    // if (res && res.length > 0) {
    //   res.map((item) => {
    //     const dataItem = {
    //       key: item.roleName,
    //       value: item.roleId,
    //     };
    //     list.push(dataItem);
    //   });
    // }
    // setRoleEunm(list.filter((item) => item.value !== mountDetail.roleId));
  };

  // 渲染处理
  // 事件处理
  const handleExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };

  const handleCheck = (checkedKeysValue, ref) => {
    const { checked } = checkedKeysValue;
    setCheckedKeys(checked);
    ref.setFieldsValue({ roleMenuList: checkedKeys });
  };

  const handleLoad = (form) => {
    if (!_.isNil(form.form)) {
      form = form.form;
    }
    form.setFieldsValue({ roleMenuList: checkedKeys });
  };

  const handleChange = (obj) => {
    // 获取选中复制角色
    const { copyRole } = obj;
    const filterRes: any = roleEunm.filter(
      (item: any) => item.value === copyRole
    );
    // 根据复制角色id获取复制角色菜单数据并处理
    const param = {
      roleId: !_.isEmpty(filterRes) && filterRes[0].value,
    };
    getFormDataById(param, false);
  };

  const handleSelect = (key, ref) => {
    setSelectedKeys(key);
  };

  const handleAction = (type, data) => {
    if (type === OPERATE_TYPE.edit || type === OPERATE_TYPE.create) {
      setDetailType(type);
      setDetailData(data);
    } else {
      setDetailType(type);
      setMountDetail(data);
    }
  };

  // 角色挂载配置
  const mountFormConfig = {
    config: detailMountFormConfig,
    data: mountDetail,
    intlPrefix: prefix,
    props: {
      onExpand: handleExpand,
      onCheck: handleCheck,
      onSelect: handleSelect,
      onLoad: handleLoad,
    },
    onChange: handleChange,
  };

  return (
    <PageTemplate
      searchConfig={searchConfig}
      tableConfig={tableConfig}
      formConfig={
        detailType === OPERATE_TYPE.edit || detailType === OPERATE_TYPE.create
          ? formConfig
          : mountFormConfig
      }
      urlObj={urlObj}
      formActionConfig={formActionConfig}
      cardTitle="roleManage"
      intlPrefix={prefix}
      onAction={handleAction}
    />
  );
};
export default memo(Role);
