import DICT_CONSTANTS from '@/constants/dictConstants';
import { COMPONENT_TYPE, RENDER_TYPE, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import { getRequestData } from '@/utils/urlUtil';

export const pageConfig = {
  prefix: I18N_COMON_PAGENAME.CALL_PARAM,
  cardTitle: 'aduitManage',
  // 页面接口请求
  urlObj: {
    list: urlConstants.ADUIT_MANAGE.LIST,
    create: urlConstants.ADUIT_MANAGE.CREATE,
    edit: urlConstants.ADUIT_MANAGE.EDIT,
    serviceType: 'business',
    getRequestData,
  },
  resetValue: { aduitPage: '', aduitStatus: null },
  // 搜索
  searchSource: [
    {
      value: 'aduitPage',
      label: 'aduitPage',
      type: COMPONENT_TYPE.INPUT,
    },
    {
      value: 'aduitStatus',
      label: 'aduitStatus',
      type: COMPONENT_TYPE.SELECT,
      data: DICT_CONSTANTS.PARAM_STATUS,
    },
  ],

  // 列表
  columns: [
    {
      title: 'paramIndex',
      dataIndex: 'paramIndex',
      key: 'paramIndex',
      width: 80,
    },
    {
      title: 'aduitPage',
      dataIndex: 'aduitPage',
      key: 'aduitPage',
      width: 120,
    },
    {
      title: 'aduitUser',
      dataIndex: 'aduitUser',
      key: 'aduitUser',
      data: DICT_CONSTANTS.LABEL_TYPE,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'LABEL_TYPE',
      width: 120,
    },
    {
      width: 100,
      key: 'aduitStatus',
      title: 'aduitStatus',
      dataIndex: 'aduitStatus',
      data: DICT_CONSTANTS.PARAM_STATUS,
      valueType: RENDER_TYPE.MockDictionary,
      dictType: 'PARAM_STATUS',
    },
    {
      width: 150,
      key: 'aduitTime',
      title: 'aduitTime',
      dataIndex: 'aduitTime',
    },
    {
      key: 'applyUser',
      title: 'applyUser',
      dataIndex: 'applyUser',
      width: 120,
    },
    {
      width: 150,
      key: 'applyTime',
      title: 'applyTime',
      dataIndex: 'applyTime',
    },
  ],
};

// 需要查询的参数类型
export const dictEnum = {
  // crcdOrgNo: DICT_CONSTANTS.DICT_ENUM_MAP.crcdOrgNo,
};
