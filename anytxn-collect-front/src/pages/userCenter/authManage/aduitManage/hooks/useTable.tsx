// table组件hooks
import { CommonTable } from '@/components';
import { pageConfig } from '../pageConfig';
import { IPaniation } from '@/types/ICommon';

const useTable = ({ searchValue, listData, loading, paginationConfig, getTableData, handleAction }) => {
  const { prefix, columns } = pageConfig;
  // 页码改变事件
  const handleTableChange = (pagination: IPaniation): void => {
    const { current: currentPage, pageSize } = pagination;
    getTableData(searchValue, { currentPage, pageSize });
  };
  // 渲染组件
  const tableChildren = () => {
    return (
      <CommonTable
        rowKey="paramIndex"
        dataSource={listData}
        columns={columns}
        loading={loading}
        optionList={[]}
        paginationConfig={paginationConfig}
        intlPrefix={prefix}
        onAction={handleAction}
        onChange={handleTableChange}
        props={{
          rowSelection: {
            type: 'checkbox',
            onChange: (selectedRowKeys: string, selectedRows: any) => {
              // console.log('33333--selectedRows--table', selectedRowKeys, selectedRows[0]);
            },
          },
        }}
      />
    );
  };

  return { tableChildren };
};

export default useTable;
