// 參數/額度/額度節點
import { FC, useState, useEffect, useRef } from 'react';
import _ from 'lodash';
import { Space, Modal, type TablePaginationConfig, Form, Select } from 'antd';
import { GradientButton, LayoutTemplate } from '@/components';
import commonServices from '@/services/common';
import { OPERATE_TYPE, COMPONENT_TYPE, DEFAULT_PAGINATION, I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import urlConstants from '@/constants/urlConstants';
import useDictStore from '@/hooks/useDictStore';
import { dictEnum, pageConfig } from './pageConfig';
import useSearch from './hooks/useSearch';
import useTable from './hooks/useTable';
import useIntlCustom from '@/hooks/useIntlCustom';
import TextArea from 'antd/es/input/TextArea';

const { prefix, cardTitle } = pageConfig;

const LimitNode: FC = () => {
  // hooks变量
  const { translate } = useIntlCustom();
  const [listData, setListData] = useState<object[]>([]);
  const [detailType, setDetailType] = useState<string>(OPERATE_TYPE.list);
  const [detail, setDetail] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [cardShowBack, setCardShowBack] = useState<boolean>(false);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);
  const [searchBtnDisabled, setSearchBtnDisabled] = useState<boolean>(false);
  useDictStore(dictEnum);

  // 副作用
  useEffect(() => {
    getTableData();
  }, []);
  // 逻辑处理
  const getTableData = async (searchValue = { keyValue: '', value: '' }, pagination = { ...DEFAULT_PAGINATION }) => {
    setLoading(true);
    try {
      const param = {
        url: urlConstants.ADUIT_MANAGE.LIST,
        pagination,
        searchValue: { [searchValue.keyValue]: searchValue?.value },
      };
      const res = await commonServices.getTableListData(param);
      const { data, total } = res;
      Array.isArray(data) && setListData(data);
      total && setPaginationConfig({ showSizeChanger: true, showQuickJumper: true, total });
    } catch (error) {
      setListData([]);
    } finally {
      setLoading(false);
    }
  };
  // 事件处理
  // 操作列事件
  const handleAction = (type: string, row: any) => {
    setCardShowBack(true);
    setDetailType(type);
    setDetail(row);
    setSearchBtnDisabled(true);
  };
  // 返回事件
  const handleCardBack = () => {
    setDetailType(OPERATE_TYPE.list);
    setCardShowBack(false);
    setSearchBtnDisabled(false);
  };

  // search组件hooks
  const { searchValue, searchChildren } = useSearch({ searchBtnDisabled, getTableData, handleAction });
  // table组件hooks
  const { tableChildren } = useTable({ listData, loading, searchValue, paginationConfig, getTableData, handleAction });

  // 渲染按钮
  const renderFormAction = () => {
    return (
      <Space>
        <GradientButton type="primary" size="middle" onClick={() => handleSubmit('reject')}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'reject')}
        </GradientButton>
        <GradientButton type="primary" size="middle" onClick={() => handleSubmit('pass')}>
          {translate(I18N_COMON_PAGENAME.COMMON, 'pass')}
        </GradientButton>
      </Space>
    );
  };

  // 提交
  const handleSubmit = async (subType: string) => {
    Modal.confirm({
      title: subType === 'pass' ? '审批通过' : '审批拒绝',
      content: (
        <Form
          name="basic"
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          style={{ maxWidth: 240 }}
          initialValues={{ remember: true }}
          // onFinish={onFinish}
          // onFinishFailed={onFinishFailed}
          autoComplete="off"
        >
          <Form.Item label="审批意见" name="aduitDesc" rules={[{ required: true }]}>
            <TextArea />
          </Form.Item>
        </Form>
      ),
    });
  };
  return (
    <>
      <LayoutTemplate
        searchChildren={searchChildren()}
        tableChildren={tableChildren()}
        formChildren={null}
        childrenType={COMPONENT_TYPE.TABLE}
        type={detailType}
        intlPrefix={prefix}
        cardShowBack={cardShowBack}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
        onCardBack={handleCardBack}
      />
    </>
  );
};

export default LimitNode;
