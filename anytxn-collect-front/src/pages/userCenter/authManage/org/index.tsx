import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON> } from "antd";
import styles from "./index.module.css";
import useIntlCustom from "@/hooks/useIntlCustom";

const OrgPage = () => {
  const { translate } = useIntlCustom();
  const prefix = "authManage";

  const treeData = [
    {
      title: translate(prefix, "orgTreeRoot"),
      key: "0",
      children: [
        {
          title: translate(prefix, "orgTreeCreditCard"),
          key: "0-0",
          children: [
            {
              title: translate(prefix, "orgTreeOps"),
              key: "0-0-0",
              children: [
                { title: translate(prefix, "orgTreeDecision"), key: "0-0-0-0" },
                {
                  title: translate(prefix, "orgTreeOperation"),
                  key: "0-0-0-1",
                },
              ],
            },
          ],
        },
        { title: translate(prefix, "orgTreeSystem"), key: "0-1" },
        { title: translate(prefix, "orgTreeHead"), key: "0-2" },
        { title: translate(prefix, "orgTreeItem13"), key: "0-3" },
      ],
    },
  ];

  const orgDetail = {
    id: "000001",
    name: translate(prefix, "orgTreeDecision"),
    parent: translate(prefix, "orgTreeOps"),
    type: translate(prefix, "orgType"),
    count: 9,
  };

  const [selectedKey, setSelectedKey] = useState<string>("0-0-0-0");

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* 左侧树形菜单 */}
        <div className={styles.sidebar}>
          <div className={styles.treeTitle}>{translate(prefix, "orgTree")}</div>
          <Tree
            treeData={treeData}
            defaultExpandAll
            selectedKeys={[selectedKey]}
            onSelect={(keys) => setSelectedKey(keys[0]?.toString() || "")}
            style={{ background: "#fff" }}
          />
        </div>
        {/* 右侧详情 */}
        <div className={styles.detail}>
          <Card className={styles.card} bodyStyle={{ padding: 32 }}>
            <div className={styles.cardContent}>
              <div className={styles.cardContentItem}>
                {translate(prefix, "orgId")}：{orgDetail.id}
              </div>
              <div className={styles.cardContentItem}>
                {translate(prefix, "orgName")}：{orgDetail.name}
              </div>
              <div className={styles.cardContentItem}>
                {translate(prefix, "parentOrg")}：{orgDetail.parent}
              </div>
              <div className={styles.cardContentItem}>
                {translate(prefix, "orgType")}：{orgDetail.type}
              </div>
              <div>
                {translate(prefix, "orgCount")}：{orgDetail.count}
              </div>
            </div>
            <div className={styles.cardActions}>
              <Button type="primary" size="large" className={styles.actionBtn}>
                {translate(prefix, "edit")}
              </Button>
              <Button
                type="primary"
                danger
                size="large"
                className={styles.actionBtn}
              >
                {translate(prefix, "delete")}
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrgPage;
