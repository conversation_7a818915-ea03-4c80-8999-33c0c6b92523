.container {
  display: flex;
  height: 100vh;
  background: #f5f6fa;
  justify-content: center;
  align-items: center;
}

.content {
  flex: 1;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  display: flex;
  overflow: hidden;
  padding: 24px;
  min-height: 0;
  height: 100%;
}

.sidebar {
  width: 40%;
  border-right: 1px solid #f0f0f0;
  padding: 32px 24px;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
}

.treeTitle {
  font-weight: 600;
  font-size: 18px;
  margin-bottom: 24px;
}

.detail {
  width: 60%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
}

.card {
  width: 380px;
  min-height: 340px;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  border: none;
  background: #fff;
}

.cardContent {
  font-size: 18px;
  margin-bottom: 24px;
  font-weight: 500;
}

.cardContentItem {
  margin-bottom: 16px;
}

.cardActions {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}

.actionBtn {
  width: 120px;
}
