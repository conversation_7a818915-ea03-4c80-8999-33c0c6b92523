/* MessageBench 100%还原样式 */
.messageBenchRoot {
  background: #fff;
  padding: 24px;
  height: 100vh;
  overflow-y: auto;
}
.topBar {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.topBarTabs {
  flex: 1;
}
.topBarRight {
  margin-left: auto;
  display: flex;
  gap: 8px;
}
.flexRow {
  display: flex;
}
.flexAlignCenter {
  align-items: center;
}
.flexGap24 {
  gap: 24px;
}
.flexGap32 {
  gap: 32px;
}
.flexGap8 {
  gap: 8px;
}
.flex2 {
  flex: 2;
  min-width: 0;
}
.flex1 {
  flex: 1;
  min-width: 0;
}
.flex1Min320 {
  flex: 1;
  min-width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.font16Bold {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
}
.font16BoldLeft {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
  align-self: flex-start;
}
.font32Bold {
  font-size: 32px;
  font-weight: 700;
  margin-right: 8px;
}
.font18 {
  font-size: 18px;
  font-weight: 400;
}
.marginLeft32 {
  margin-left: 32px;
}
.marginBottom8 {
  margin-bottom: 8px;
}
.marginBottom16 {
  margin-bottom: 16px;
}
.marginTop16 {
  margin-top: 16px;
}
.marginTop24 {
  margin-top: 24px;
}
.marginTop40 {
  margin-top: 40px;
}
.minWidth220 {
  min-width: 220px;
}
.bgGray {
  background: #fafbfc;
}
.radius8 {
  border-radius: 8px;
}
.padding16 {
  padding: 16px;
}
.width24 {
  width: 24px;
  text-align: center;
}
.fontWeight700 {
  font-weight: 700;
}
.fontWeight500 {
  font-weight: 500;
}
.fontWeight400 {
  font-weight: 400;
}
.color333 {
  color: #333;
}
.color999 {
  color: #999;
}
.color1890ff {
  color: #1890ff;
}
.flex1Span {
  flex: 1;
}
.inlineDot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}
.inlineDot8 {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}
.inlineDot12 {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 4px;
  vertical-align: middle;
}
.font12 {
  font-size: 12px;
}
.bgWhite {
  background: #fff;
}
.radius8 {
  border-radius: 8px;
}
.padding16 {
  padding: 16px;
}
