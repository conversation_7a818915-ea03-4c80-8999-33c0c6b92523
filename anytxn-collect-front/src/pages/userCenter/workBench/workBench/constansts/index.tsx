import { intlConst } from "@/hooks/useIntlCustom";

const prefix = "callParam";

const WORKBENCH_CONSTANTS: any = {
  HEADER_PHONE_ENUMS: [
    { key: "PhoneOutlined", value: intlConst.formatMessage(prefix, "answer") },
    { key: "WhatsAppOutlined", value: intlConst.formatMessage(prefix, "dial") },
    {
      key: "AudioMutedOutlined",
      value: intlConst.formatMessage(prefix, "hold"),
    },
    {
      key: "CommentOutlined",
      value: intlConst.formatMessage(prefix, "consult"),
    },
    { key: "ApiOutlined", value: intlConst.formatMessage(prefix, "transfer") },
    {
      key: "MessageOutlined",
      value: intlConst.formatMessage(prefix, "conference"),
    },
    { key: "StopOutlined", value: intlConst.formatMessage(prefix, "busy") },
    {
      key: "CheckSquareOutlined",
      value: intlConst.formatMessage(prefix, "ready"),
    },
    {
      key: "CustomerServiceOutlined",
      value: intlConst.formatMessage(prefix, "afterProcess"),
    },
    {
      key: "CheckOutlined",
      value: intlConst.formatMessage(prefix, "signIn"),
      color: "#32b372",
    },
  ],
  BADGES_DATA: [
    {
      count: "100",
      shape: "square",
      size: "large",
      text: intlConst.formatMessage(prefix, "totalCases"),
    },
    {
      count: "28",
      shape: "square",
      size: "large",
      text: intlConst.formatMessage(prefix, "pendingCases"),
    },
    {
      count: "3",
      shape: "square",
      size: "large",
      text: intlConst.formatMessage(prefix, "completedCases"),
      color: "#32b372",
    },
  ],
  TABLE_MAP_DATA: [
    {
      icon: "AppstoreOutlined",
      rowKey: "id",
      title: intlConst.formatMessage(prefix, "performanceList"),
      columns: "columns1",
      dataSource: "dataSource1",
    },
    {
      icon: "AppstoreOutlined",
      rowKey: "id",
      title: intlConst.formatMessage(prefix, "performanceList"),
      columns: "columns2",
      dataSource: "dataSource2",
    },
    {
      icon: "AppstoreOutlined",
      rowKey: "id",
      title: intlConst.formatMessage(prefix, "performanceList"),
      columns: "columns3",
      dataSource: "dataSource3",
    },
    {
      icon: "AppstoreOutlined",
      rowKey: "id",
      title: intlConst.formatMessage(prefix, "performanceList"),
      columns: "columns4",
      dataSource: "dataSource4",
    },
  ],
  DSCRIPTION_ITEMS: [
    {
      key: "1",
      label: intlConst.formatMessage(prefix, "status"),
      children: intlConst.formatMessage(prefix, "collected"),
    },
    { key: "2", label: intlConst.formatMessage(prefix, "mode"), children: "" },
    {
      key: "3",
      label: intlConst.formatMessage(prefix, "contact"),
      children: "张三",
    },
    {
      key: "5",
      label: intlConst.formatMessage(prefix, "activity"),
      children: "",
    },
    {
      key: "6",
      label: intlConst.formatMessage(prefix, "relation"),
      children: intlConst.formatMessage(prefix, "spouse"),
    },
    {
      key: "7",
      label: intlConst.formatMessage(prefix, "direction"),
      children: "",
    },
    {
      key: "8",
      label: intlConst.formatMessage(prefix, "source"),
      children: "",
    },
    {
      key: "4",
      label: intlConst.formatMessage(prefix, "duration"),
      children: "00:12:12",
    },
  ],
  ECHARTS_LAYOUT_STYLES: {
    width: "100%",
    height: "320px",
    background: "#ffffff",
    paddingTop: "1rem",
    paddingBottom: "1rem",
  },
  // 折线图
  ECHARTS_OPTIONS: {
    title: {
      text: intlConst.formatMessage(prefix, "caseTrendChart"),
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: [
        intlConst.formatMessage(prefix, "totalPendingCases"),
        intlConst.formatMessage(prefix, "newCollectionCases"),
        intlConst.formatMessage(prefix, "exitCollectionCases"),
        intlConst.formatMessage(prefix, "completedCases"),
      ],
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: intlConst.formatMessage(prefix, "totalPendingCases"),
        type: "line",
        data: [66, 59, 76, 52, 43, 46, 50],
        label: {
          show: true, // 显示标签
          position: "top", // 标签位置
        },
      },
      {
        name: intlConst.formatMessage(prefix, "newCollectionCases"),
        type: "line",
        data: [33, 23, 28, 14, 19, 20, 26],
        label: {
          show: true, // 显示标签
          position: "top", // 标签位置
        },
      },
      {
        name: intlConst.formatMessage(prefix, "exitCollectionCases"),
        type: "line",
        data: [22, 23, 29, 24, 12, 13, 12],
        label: {
          show: true, // 显示标签
          position: "top", // 标签位置
        },
      },
      {
        name: intlConst.formatMessage(prefix, "completedCases"),
        type: "line",
        data: [12, 13, 19, 14, 12, 13, 12],
        label: {
          show: true, // 显示标签
          position: "top", // 标签位置
        },
      },
    ],
  },
  // 饼图
  ECHARTS_PIE_OPTIONS: {
    title: {
      text: intlConst.formatMessage(prefix, "caseDistributionChart"),
      left: "center",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "left",
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: "50%",
        data: [
          {
            value: 1048,
            name: intlConst.formatMessage(prefix, "phoneCollectionCases"),
          },
          {
            value: 735,
            name: intlConst.formatMessage(prefix, "smsCollectionCases"),
          },
          {
            value: 580,
            name: intlConst.formatMessage(prefix, "outsourcingCases"),
          },
          {
            value: 484,
            name: intlConst.formatMessage(prefix, "writeOffCases"),
          },
          {
            value: 300,
            name: intlConst.formatMessage(prefix, "litigationCases"),
          },
        ],
        label: {
          show: true, // 显示标签
          formatter: "{b}: {c} ({d}%)", // 标签格式，{b} 表示名称，{c} 表示数值，{d} 表示百分比
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  },
  // 柱状图
  ECHARTS_BAR_OPTIONS: {
    title: {
      text: intlConst.formatMessage(prefix, "teamCaseDistributionChart"),
    },
    legend: {},
    tooltip: {},
    dataset: {
      source: [
        [
          "product",
          intlConst.formatMessage(prefix, "teamA"),
          intlConst.formatMessage(prefix, "teamB"),
          intlConst.formatMessage(prefix, "teamC"),
          intlConst.formatMessage(prefix, "teamD"),
        ],
        [
          intlConst.formatMessage(prefix, "phoneCollectionTeam"),
          43.3,
          85.8,
          93.7,
          66.5,
        ],
        [
          intlConst.formatMessage(prefix, "outsourcingTeam"),
          83.1,
          73.4,
          55.1,
          88.8,
        ],
        [
          intlConst.formatMessage(prefix, "litigationTeam"),
          86.4,
          65.2,
          82.5,
          99,
        ],
      ],
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    xAxis: { type: "category" },
    yAxis: {},
    series: [
      { type: "bar", label: { show: true, position: "top" } },
      { type: "bar", label: { show: true, position: "top" } },
      { type: "bar", label: { show: true, position: "top" } },
      { type: "bar", label: { show: true, position: "top" } },
    ],
  },
};
export default WORKBENCH_CONSTANTS;
