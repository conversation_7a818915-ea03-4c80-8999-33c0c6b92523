import { FC, memo } from "react";
import { Carousel } from "antd";
import { AccountBookOutlined, AppstoreOutlined } from "@ant-design/icons";
import styles from "./index.module.css";
import { pageConfig } from "./pageConfig";
import { Divider, Table } from "antd";
import EchartsComponent from "@/components/echartsComponent";
import WORKBENCH_CONSTANTS from "../../constansts/index";
import { intlConst } from "@/hooks/useIntlCustom";

const ContentCom: FC = () => {
  const {
    columns1,
    columns2,
    columns3,
    columns4,
    prefix,
    dataSource1,
    dataSource2,
    dataSource3,
  } = pageConfig;
  return (
    <div className={`flex-row ${styles.content}`}>
      <div className={styles.contentLeft}>
        <Carousel autoplay dots={{ className: styles.carouselDtoss }}>
          <EchartsComponent
            options={WORKBENCH_CONSTANTS.ECHARTS_OPTIONS}
            layoutStyle={WORKBENCH_CONSTANTS.ECHARTS_LAYOUT_STYLES}
          />
          <EchartsComponent
            options={WORKBENCH_CONSTANTS.ECHARTS_PIE_OPTIONS}
            layoutStyle={WORKBENCH_CONSTANTS.ECHARTS_LAYOUT_STYLES}
          />
          <EchartsComponent
            options={WORKBENCH_CONSTANTS.ECHARTS_BAR_OPTIONS}
            layoutStyle={WORKBENCH_CONSTANTS.ECHARTS_LAYOUT_STYLES}
          />
        </Carousel>
        <div className={styles.table}>
          <div className={`${styles.tableContent} ${styles.tableCallStyle}`}>
            <AppstoreOutlined /> <span>{intlConst.formatMessage("callParam", "performanceList")}</span>
            <Divider className={styles.divider} />
            <Table
              size="small"
              rowKey="aa1"
              dataSource={dataSource1}
              columns={columns1}
              className={styles.tableContentFont}
            />
          </div>
          <div className="flex-row">
            <div
              className={`${styles.tableCallStyle} ${styles.tableContent} ${styles.tableCallStyleLeft}`}
            >
              <AccountBookOutlined /> <span>{intlConst.formatMessage("callParam", "realTimeRepayment")}</span>
              <Divider className={styles.divider} />
              <Table
                size="small"
                rowKey="bb1"
                dataSource={dataSource2}
                columns={columns2}
                className={styles.tableContentFont}
              />
            </div>
            <div className={`${styles.tableCallStyle} ${styles.tableContent}`}>
              <span className={styles.tableContentNum}>7</span>
              <span>{intlConst.formatMessage("callParam", "pendingApprovalItems")}</span>
              <Divider className={styles.divider} />
              <Table
                size="small"
                rowKey="cc1"
                dataSource={dataSource3}
                columns={columns3}
                className={styles.tableContentFont}
              />
            </div>
          </div>
        </div>
      </div>
      <div className={styles.contentRight}>
        <div className={`${styles.rightText} ${styles.knowledge}`}>
          {intlConst.formatMessage("callParam", "knowledgeBase")}
        </div>
        <div className={`${styles.rightText} ${styles.task}`}>
          <span>0</span>{intlConst.formatMessage("callParam", "pendingQualityTasks")}
        </div>
        <div className={`${styles.rightText} ${styles.limit}`}>
          {intlConst.formatMessage("callParam", "largeSumRepayment")}
        </div>
        <div className={`${styles.rightText} ${styles.congritulation}`}>
          <p className={styles.remark}>
            {intlConst.formatMessage("callParam", "noLargeRepayment")}
          </p>
        </div>
        <div className={`${styles.rightText} ${styles.message}`}>
          <span>0</span>{intlConst.formatMessage("callParam", "unreadNotifications")}
        </div>
      </div>
    </div>
  );
};

export default memo(ContentCom);
