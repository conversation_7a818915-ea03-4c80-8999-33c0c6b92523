import { FC, memo } from "react";
import { Badge, Descriptions } from "antd";
import WOR<PERSON>BENCH_CONSTANTS from "../../constansts/index";
import styles from "./index.module.css";
import RouterBar from "@/layouts/header/RouterBar";

const HeaderCom: FC = () => {
  return (
    <div
      className={`app-block m-b flex-row flex-justify-between flex-align-center ${styles.header}`}
    >
      <RouterBar />
      {/* <div className={`flex-row flex-justify-between ${styles.left}`}> */}
      {/* <div className={styles.leftForm}>
          <Form
            name="basic"
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 16 }}
            style={{ maxWidth: 240 }}
            initialValues={{ remember: true }}
            // onFinish={onFinish}
            // onFinishFailed={onFinishFailed}
            autoComplete="off"
          >
            <Form.Item<FieldType> label="联系号码" name="username">
              <Input />
            </Form.Item>
            <Form.Item<FieldType> label="外送号码" name="password">
              <Input />
            </Form.Item>
          </Form>
        </div>
        <div className={styles.cneterIcon}>
          {WORKBENCH_CONSTANTS.HEADER_PHONE_ENUMS?.map((item: any) => {
            return (
              <div
                className={`flex-col flex-align-center flex-justify-around ${styles.icons}`}
              >
                <p style={{ fontSize: 16, color: item.color }}>
                  {iconList[item.key].render()}
                </p>
                <p>{item.value}</p>
              </div>
            );
          })}
        </div> */}
      <div className={styles.cneterText}>
        {/* <Descriptions
          bordered={false}
          size="small"
          column={4}
          items={WORKBENCH_CONSTANTS.DSCRIPTION_ITEMS}
          style={{ width: "500px" }}
        /> */}
      </div>
      {/* </div> */}
      <div className={styles.right}>
        {WORKBENCH_CONSTANTS.BADGES_DATA?.map((item: any) => {
          return (
            <span className={styles.badge}>
              <Badge count={item.count} style={{ backgroundColor: item.color }}>
                <div
                  className={styles.caseCount}
                  // style={{
                  //   padding: "0.5rem",
                  //   background: "#d9d9d9",
                  //   borderRadius: "0.4rem",
                  //   fontSize: "0.8rem",
                  // }}
                >
                  {item.text}
                </div>
              </Badge>
            </span>
          );
        })}
      </div>
    </div>
  );
};

export default memo(HeaderCom);
