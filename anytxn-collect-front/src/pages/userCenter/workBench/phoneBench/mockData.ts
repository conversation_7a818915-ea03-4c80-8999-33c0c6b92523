export default {
  formData: {
    infoForm1: {
      name: "<PERSON>", // Name
      gender: "Male", // Gender
      branchNumber: "0001", // Branch Number
      billDay: "08", // Billing Day
      englishName: "Jack", // English Name
      totalDebt: "0.00", // Total Debt
      riskLevel: "Low", // Risk Level
      idNumber: "1********0********", // ID Number
      badMark: false, // Bad Mark
      delayDays: 0, // Delay Days
      reAuditFlag: false, // Re-audit Flag
      permanentCreditLimit: "10000.00", // Permanent Credit Limit
      activeInstallmentFlag: 0, // Active Installment Flag
      largeAccountFlag: false, // Large Account Flag
      twentyFourMonthDelayStatus: "Normal", // 24-month Delay Status
      memo: "", // Memo
      companyName: "Zhao Weilun Technology Company", // Company Name
      collectionRemark: "", // Collection Remark
      customerId: "CUST123456", // Customer ID
      vipLevel: "Ordinary", // VIP Level
    },
    infoForm2: {
      name: "<PERSON>",
      gender: "Male",
      branchNumber: "0001",
      subBranchNumber: "0002", // Default value
      documentValidity: "2025-12-31", // Default value
      birthDate: "1990-01-01", // Default value
      residenceDuration: "5 years", // Default value
      occupation: "Software Engineer", // Default value
      companyName: "Zhao Weilun Company", // Default value
      abCustomerGroup: "A", // Default value
      currentBadFlagDomain: "None", // Default value
      accountAuthorization: "Authorized", // Default value
      lockCode: "None", // Default value
      oldLockCodeStartDate: "2020-01-01", // Default value
      mobileNumber: "***********", // Default value
      email: "<EMAIL>", // Default value
      documentType: "ID Card", // Default value
      education: "Bachelor's Degree", // Default value
      major: "Computer Science", // Default value
      maritalStatus: "Unmarried", // Default value
      cardholderType: "Individual", // Default value
      temporaryCreditLimit: "5000", // Default value
      cardholderDifferenceSwitch: "Off", // Default value
      statusChangeDate: "2023-01-01", // Default value
      guarantorPhone: "***********", // Default value
      deposit: "10000", // Default value
    },
    infoForm3: {
      homePhone: "010-********", // Default value
      mobilePhone: "***********", // Default value
      workPhone1: "020-********", // Default value
      workPhone2: "020-********", // Default value
      workPhone3: "020-34567890", // Default value
      emergencyContact1Name: "Zhang San", // Default value
      emergencyContact1Phone: "13900000001", // Default value
      emergencyContact2Name: "Li Si", // Default value
      emergencyContact2Phone: "13900000002", // Default value
      householdAddress: "Chaoyang District, Beijing", // Default value
      homeAddress: "Haidian District, Beijing", // Default value
      workAddress: "Fengtai District, Beijing", // Default value
      currentResidence: "Chaoyang District, Beijing", // Default value
      billingAddress: "Haidian District, Beijing", // Default value
    },
    infoForm4: {
      "jiGouHao": "101",
      kaZhuanXinZhangHuBiZhong: ['USD', 'EUR', 'RMB', 'JPY'][Math.floor(Math.random() * 4)],
      xinYongKaChiKaRenHao: Math.floor(Math.random() * 1e16).toString().padStart(16, '0'),
      zhangHuFengSuoMa: `FREEZE${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      zhangHuFengSuoMaYuanYinMa: `REASON${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      zhangHuDanRi: Math.floor(Math.random() * 31) + 1,
      zhangHuDongJieZhuangTai: ['Frozen', 'Unfrozen'][Math.floor(Math.random() * 2)],
      mianShouWeiYueJinBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      cuiShouWeiWaiBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      zhangHuRuCuiCiShu: Math.floor(Math.random() * 10),
      benQiZuiDiHuanKuanShengYu: (Math.random() * 1000).toFixed(2),
      "150TianYanQiJinE": (Math.random() * 1000).toFixed(2),
      yanQiQiShu: Math.floor(Math.random() * 10),
      "150TianYanQiCiShu": Math.floor(Math.random() * 10),
      qiChuXiaoFeiShengYuJinE: (Math.random() * 1000).toFixed(2),
      weiGuaXianHuanKuanJinE: (Math.random() * 1000).toFixed(2),
      zhangHuQuXianJinELeiJi: (Math.random() * 1000).toFixed(2),
      zhangHuShangCiQuXianRiQi: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      yiGuaXiaoFeiDaiTiaoYuE: (Math.random() * 1000).toFixed(2),
      teShuChanPinBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      zhangHuZuiGaoJinERiQi: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      shangQiHuanKuanDaoQiRiQi: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      zhangHuHao: `ACCOUNT${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      kaZhuanXinZhangHuXuHao: `SERIAL${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      zhangHuRongChaKaiGuanBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      jiuZhangHuFengSuoMa: `OLD_FREEZE${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      jiuZhangHuFengSuoMaYuanYinMa: `OLD_REASON${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      zhangHuShengRiYouXiaoRi: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      zhangHuDongJieYuanYinMa: `FREEZE_REASON${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      mianShouChaoXianJinBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      zhangHuHeXinBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      zhangHuShangCiYanQiRiQi: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      "30TianYanQiJinE": (Math.random() * 1000).toFixed(2),
      "180TianYanQiJinE": (Math.random() * 1000).toFixed(2),
      "30TianYanQiCiShu": Math.floor(Math.random() * 10),
      "180TianYanQiCiShu": Math.floor(Math.random() * 10),
      tiXianQiChuYuE: (Math.random() * 1000).toFixed(2),
      duiXianHuanKuanYuanYuE: (Math.random() * 1000).toFixed(2),
      zhangHuMianXiBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      shangCiXiaoFeiJinE: (Math.random() * 1000).toFixed(2),
      yiGuaXianDaiTiaoYuE: (Math.random() * 1000).toFixed(2),
      shangCiJiaoYiRiQi: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      dangQianJingJiRiQi: new Date().toISOString().split('T')[0],
      zhangHuPin: `PRODUCT${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      zhangHuZhuangTai: ['Active', 'Inactive', 'Frozen'][Math.floor(Math.random() * 3)],
      zhuangTaiGaiBianRiQi: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      danYiHuoBiJieSuanBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      zhangHuFengSuoMaBeiZhu: `REMARK${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      ziDongHuanKuanZhangHu: `AUTO_ACCOUNT${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      chunNianFeiYanQiBiaoShi: [true, false][Math.floor(Math.random() * 2)],
      ruCuiYuanYin: `REASON${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
      chuCuiRiQi: new Date(Date.now() + Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      "120TianYanQiJinE": (Math.random() * 1000).toFixed(2),
      "120TianYanQiCiShu": Math.floor(Math.random() * 10),
      kuanXianRiQi: new Date(Date.now() + Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
      zhangHuXiaoFeiJinELeiJi: (Math.random() * 1000).toFixed(2),
      shangCiQuXianJinE: (Math.random() * 1000).toFixed(2),
      dangQianHuanKuanRongShiBiao: [true, false][Math.floor(Math.random() * 2)],
      zhangHuZuiGaoYuE: (Math.random() * 1000).toFixed(2),
      mianJiChaoXianJin: (Math.random() * 1000).toFixed(2)
    },
    infoForm5: {
        "jiGouHao": "123456",
        "kaDengJi": "GOLD",
        "teShuChanPinBiaoShi": "SPECIAL",
        "chiKaRenJiGouHao": "654321",
        "kaPianFengSuoMa": "BLOCKED",
        "kaPianShangYiFengSuoMa": "UNBLOCKED",
        "huanKaXinBiZhong": "USD",
        "nianFeiZheKouBiaoShi": "DISCOUNT",
        "miMaCuoWuCiShu": 3,
        "kaPianXingMingJianCheng": "ZHANGS",
        "shenQingShuLiuShuiBianHao": "APP20240001",
        "yingXiaoRenYuanGongHao": "EMP789",
        "guanBiRiQi": "2024-10-15",
        "kaPianJiaoYiFanWeiBiaoShi": "DOMESTIC",
        "zhangHao": "62258801********",
        "zhuFuKaBiaoShi": "PRIMARY",
        "kaBanDaiMa": "CARDV1",
        "chiKaRenHao": "CUST123",
        "kaPianFengSuoYuanYinMa": "LOST",
        "kaPianShangYiFengSuoYuanYinMa": "FRAUD",
        "huanKaXinChanPinBianHao": "PROD789",
        "mianShouNianFeiBiaoShi": "FREE",
        "miMaCuoWuRiQi": "2024-09-01",
        "chiKaRenXingMing": "Zhang San",
        "tongLuDaiMa": "CHANNEL01",
        "yingXiaoRenYuanXingMing": "Li Si",
        "kaPianFeiYongCanShuBianMa": "FEE001",
        "kaPianJiHuoZhuangTai": "ACTIVE",
        "biZhong": "CNY",
        "kaPianZhiLiang": "HIGH",
        "youXiaoQi": "2026-12-31",
        "fuShuChiKaRenJiGouHao": "BRANCH02",
        "kaPianFengSuoMaWeiHuRiQi": "2024-08-15",
        "kaPianShangYiFengSuoMaJieShuRiQi": "2024-07-20",
        "huanKaXinKaHao": "62258809********",
        "nianFeiShouQuRiQi": "2024-01-01",
        "miMaBiaoShi": "SET",
        "keYinMingCheng": "ZS",
        "lingKaFangShi": "BRANCH",
        "xiaoKaDaoQiRiQi": "2025-12-31",
        "kaPianChuLiCanShuBianMa": "PROC001",
        "kaPianJiHuoRiQi": "2024-01-15",
        "kaChanPin": "CREDIT",
        "kaPianZhuangTaiGaiBianRi": "2024-01-10",
        "zhiKaZuoYeBiaoShi": "COMPLETE",
        "zhiKaRiQi": "2023-12-20",
        "shangYiZhiKaRiQi": "2023-12-10",
        "zhiKaQingQiuLeiJi": 2,
        "jiuKaKaHao": "6225880000001234",
        "kaPianJieShuRiQi": "2026-12-31",
        "mianBiaoShi": "SMALLPAY",
        "lianMingKaHeZuoFangBianMa": "PARTNER01",
        "xiaoKaRenYuanXingMing": "Wang Wu",
        "kaPianJiHuoFangShi": "ONLINE"
      }
  },
  // Right anchor data
  anchorData: [
    {
      key: "infoForm1",
      href: "#infoForm1",
      title: "Collection Information",
    },
    {
      key: "infoForm2",
      href: "#infoForm2",
      title: "Customer Information",
    },
    {
      key: "infoForm3",
      href: "#infoForm3",
      title: "Contact Information",
    },
    {
      key: "infoForm4",
      href: "#infoForm4",
      title: "Account Information",
    },
    {
      key: "infoForm5",
      href: "#infoForm5",
      title: "Card Information",
    },
  ],
  // Left menu data
  segmentedData: [
    {
      label: {
        backgroundColor: "#f56a00",
        text: "Transfer",
        title: "Transfer Case",
      },
      value: "value-1",
    },
    {
      label: {
        backgroundColor: "#FFEB3B",
        text: "Advance",
        title: "Advance Payment",
      },
      value: "value-2",
    },
    {
      label: {
        backgroundColor: "#4CAF50",
        text: "Stop",
        title: "Stop Payment",
      },
      value: "value-3",
    },
    {
      label: { backgroundColor: "#FF9800", text: "Reduce", title: "Reduction" },
      value: "value-4",
    },
    {
      label: {
        backgroundColor: "#2196F3",
        text: "Guarantee",
        title: "Guarantee Deposit Offset Debt",
      },
      value: "value-5",
    },
    {
      label: {
        backgroundColor: "#87d068",
        text: "Same",
        title: "Same-Name Account Adjustment",
      },
      value: "value-6",
    },
    {
      label: {
        backgroundColor: "#f56a00",
        text: "Remove",
        title: "Remove Strong Collection Mark",
      },
      value: "value-7",
    },
    {
      label: {
        backgroundColor: "#FFEB3B",
        text: "Negotiate",
        title: "Negotiate Installment Plan",
      },
      value: "value-8",
    },
    {
      label: {
        backgroundColor: "#4CAF50",
        text: "Light",
        title: "Light Financial Order",
      },
      value: "value-9",
    },
    {
      label: {
        backgroundColor: "#FF9800",
        text: "Repair",
        title: "Information Repair",
      },
      value: "value-10",
    },
    {
      label: { backgroundColor: "#2196F3", text: "SMS", title: "SMS Message" },
      value: "value-11",
    },
    {
      label: {
        backgroundColor: "#87d068",
        text: "WeChat",
        title: "WeChat Message",
      },
      value: "value-12",
    },
    {
      label: {
        backgroundColor: "#4CAF50",
        text: "APP",
        title: "APP Operation",
      },
      value: "value-13",
    },
    {
      label: {
        backgroundColor: "#FF9800",
        text: "Email",
        title: "Email Message",
      },
      value: "value-14",
    },
    {
      label: {
        backgroundColor: "#2196F3",
        text: "Letter",
        title: "Letter Message",
      },
      value: "value-15",
    },
  ],
};
