// 用户中心/短信催收工作台
import React, { FC, memo, Suspense, useEffect, useState } from "react";
import { Drawer } from "antd";
import CallBench from "@/materials/callBench";
import commonService from "@/services/common";
import DebtCollectionCom from "../components/DebtCollectionCom";
import urlConstants from "@/constants/urlConstants";
import mockDataDarge from "./../../../sourceManage/case/caseInfo/mockData";
import mockData from "./mockData";

const DargeGraph = React.lazy(() => import("@/materials/dargeGraph"));

const debtData = {
  title: "dialPhone",
  actionType: "PHONE_CALL_MODAL",
};

const MessageBench: FC = () => {
  const [open, setOpen] = useState<boolean>(false);
  const [formData, setFormData] = useState<any>({});

  useEffect(() => {
    getCaseFormData();
  }, []);

  // 获取案件信息
  const getCaseFormData = async () => {
    const res: any = await commonService.getEditPostBiz({
      url: urlConstants.CASE_BASE_INFO.LIST,
      model: "D",
    });
    const data =
      res?.data?.filter(
        (item) => item.currState === "P" || item.caseState === "1"
      ) || [];
    setFormData(data[0]);
  };

  return (
    <Suspense>
      {/* <Affix offsetTop={160} className={styles.affix}>
        <Button type="primary" onClick={() => setOpen(true)}>
          节点流程预览
        </Button>
      </Affix> */}
      {formData && (
        <CallBench
          dataSource={{
            ...mockData.formData,
            infoForm1: { ...formData },
            infoForm2: { ...formData },
            infoForm3: { ...formData },
          }}
          workBenchName={1}
          onSubmit={() => getCaseFormData()}
        />
      )}
      <DebtCollectionCom
        debtData={debtData}
        formData={formData}
        onSubmit={() => getCaseFormData()}
      />
      {/* <Drawer
        title="流程图"
        width={600}
        onClose={() => setOpen(false)}
        open={open}
      >
        <DargeGraph data={mockDataDarge} />
      </Drawer> */}
    </Suspense>
  );
};

export default memo(MessageBench);
