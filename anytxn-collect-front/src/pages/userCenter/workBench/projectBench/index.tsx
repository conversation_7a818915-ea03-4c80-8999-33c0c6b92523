// 用户中心/短信催收工作台
import React, { FC, memo, Suspense, useEffect, useState } from "react";
import CallBench from "@/materials/callBench";
import commonService from "@/services/common";
import mockData from "./mockData";
import DebtCollectionCom from "../components/DebtCollectionCom";
import urlConstants from "@/constants/urlConstants";

const debtData = {
  title: "dialPhone",
  actionType: "PHONE_CALL_MODAL",
};

const ProjectBench: FC = () => {
  const [formData, setFormData] = useState<any>({});
  useEffect(() => {
    getCaseFormData();
  }, []);

  // 获取案件信息
  const getCaseFormData = async () => {
    const res: any = await commonService.getEditPostBiz({
      url: urlConstants.CASE_BASE_INFO.LIST,
      model: "Z",
    });
    const data =
      res?.data?.filter(
        (item) => item.currState === "P" || item.caseState === "1"
      ) || [];
    setFormData(data[0]);
  };

  return (
    <Suspense>
      {formData && (
        <CallBench
          dataSource={{
            ...mockData.formData,
            infoForm1: { ...formData },
            infoForm2: { ...formData },
            infoForm3: { ...formData },
          }}
          workBenchName={6}
          onSubmit={() => getCaseFormData()}
        />
      )}
      <DebtCollectionCom
        debtData={debtData}
        formData={formData}
        onSubmit={() => getCaseFormData()}
      />
    </Suspense>
  );
};

export default memo(ProjectBench);
