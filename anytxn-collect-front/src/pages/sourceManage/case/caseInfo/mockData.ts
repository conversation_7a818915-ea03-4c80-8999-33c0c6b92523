export default {
  nodes: [
    {
      id: "0",
      label: "开始",
    },
    {
      id: "1",
      color: "#4CAF50",
      label: "1.1-预处理",
      data: {
        flowchartName: "预处理",
        nodeExecStatus: "成功",
        nodeExecCount: 1,
        lastExecDuration: "111ms",
        lastExecTime: "2025-03-03  08:00:013:113456",
        nextNode: "2-决策数据加载",
        nodeProgram: [
          "账户数据同步批处理",
          "卡片数据同步批处理",
          "持卡人数据同步批处理",
          "交易信息同步批处理",
          "运营中心处理",
        ],
      },
    },
    {
      id: "2",
      color: "#4CAF50",
      label: "决策数据加载",
      data: {
        flowchartName: "2-决策数据加载",
        nodeExecStatus: "成功",
        nodeExecCount: 1,
        lastExecDuration: "90ms",
        lastExecTime: "2025-03-03  08:00:013:113456",
        nextNode: "4-核销规则",
        nodeProgram: ["信用评分节点", "逾期风险预测节点", "失联修复文件节点"],
      },
    },
    {
      id: "3",
      color: "#4CAF50",
      label: "核销规则",
      data: {
        flowchartName: "4-核销规则",
        nodeExecStatus: "成功",
        nodeExecCount: 2,
        lastExecDuration: "18ms",
        lastExecTime: "2025-03-03  08:00:015:113456",
        nextNode: "5.2-客户标签处理",
        nodeProgram: ["核销规则", "运营中心处理"],
      },
    },
    {
      id: "4",
      color: "#4CAF50",
      label: "客户标签处理",
      data: {
        flowchartName: "5.2-客户标签处理",
        nodeExecStatus: "成功",
        nodeExecCount: 1,
        lastExecDuration: "9ms",
        lastExecTime: "2025-03-03  08:00:016:156123",
        nextNode: "6-客户催收标识处理",
        nodeProgram: ["上客户标签处理", "下客户标签处理", "运营中心处理"],
      },
    },
    {
      id: "5",
      color: "#4CAF50",
      label: "客户催收标识处理",
      data: {
        flowchartName: "6-客户催收标识处理",
        nodeExecStatus: "成功",
        nodeExecCount: 1,
        lastExecDuration: "1ms",
        lastExecTime: "2025-03-03  08:00:016:156123",
        nextNode: "7-分板块规则",
        nodeProgram: [
          "上客户催收标识处理",
          "下客户催收标识处理",
          "运营中心处理",
        ],
      },
    },
    {
      id: "6",
      color: "#4CAF50",
      label: "分板块规则",
      data: {
        flowchartName: "7-分板块规则",
        nodeExecStatus: "成功",
        nodeExecCount: 1,
        lastExecDuration: "16ms",
        lastExecTime: "2025-03-03  08:00:018:218991",
        nextNode: "8.1-短信催收队列",
        nodeProgram: [
          "上客户催收标识处理",
          "下客户催收标识处理",
          "运营中心处理",
        ],
      },
    },
    {
      id: "7",
      color: "#FFEB3B",
      label: "短信催收队列",
      data: {
        flowchartName: "8.1-短信催收队列",
        nodeExecStatus: "成功",
        nodeExecCount: 1,
        lastExecDuration: "16ms",
        lastExecTime: "2025-03-03  08:00:018:218991",
        nextNode: "8.1-短信催收队列",
        nodeProgram: ["短信催收队列处理", "运营中心处理"],
      },
    },
    {
      id: "8",
      color: "#FFEB3B",
      label: "短信催收工作台",
      data: {
        flowchartName: "短信催收工作台",
        nodeExecStatus: "执行中",
        nodeExecCount: 0,
        nodeProgram: [
          "查询卡账客信息处理",
          "查询行动历史处理",
          "查询联系人信息处理",
          "查询短信模板处理",
          "调用短信平台接口处理",
          "运营中心处理",
        ],
      },
    },
  ],
  edges: [
    {
      id: "edge-102",
      source: "0",
      target: "1",
    },
    {
      id: "edge-161",
      source: "1",
      target: "2",
    },
    {
      id: "edge-237",
      source: "2",
      target: "3",
    },
    {
      id: "edge-253",
      source: "3",
      target: "4",
    },
    {
      id: "edge-133",
      source: "4",
      target: "5",
    },
    {
      id: "edge-320",
      source: "5",
      target: "6",
    },
    {
      id: "edge-355",
      source: "6",
      target: "7",
    },
    {
      id: "edge-13",
      source: "7",
      target: "8",
    },
    {
      id: "edge-32018",
      source: "8",
      target: "9",
    },
    {
      id: "edge-3551",
      source: "9",
      target: "10",
    },
    {
      id: "edge-1331",
      source: "10",
      target: "11",
    },
    {
      id: "edge-3201",
      source: "11",
      target: "12",
    },
    {
      id: "edge-35501",
      source: "12",
      target: "13",
    },
    {
      id: "edge-13302",
      source: "13",
      target: "14",
    },
    {
      id: "edge-32003",
      source: "14",
      target: "15",
    },
    {
      id: "edge-35504",
      source: "15",
      target: "16",
    },
    {
      id: "edge-35505",
      source: "16",
      target: "17",
    },
    {
      id: "edge-35506",
      source: "17",
      target: "18",
    },
    {
      id: "edge-35507",
      source: "18",
      target: "19",
    },
    {
      id: "edge-35508",
      source: "19",
      target: "20",
    },
    {
      id: "edge-35509",
      source: "20",
      target: "21",
    },
    {
      id: "edge-35510",
      source: "21",
      target: "22",
    },
    {
      id: "edge-35511",
      source: "22",
      target: "23",
    },
    {
      id: "edge-35512",
      source: "23",
      target: "24",
    },
    {
      id: "edge-35513",
      source: "24",
      target: "25",
    },
    {
      id: "edge-35514",
      source: "25",
      target: "26",
    },
    {
      id: "edge-35515",
      source: "26",
      target: "27",
    },
    {
      id: "edge-35516",
      source: "27",
      target: "28",
    },
    {
      id: "edge-35517",
      source: "28",
      target: "29",
    },
    {
      id: "edge-35518",
      source: "29",
      target: "30",
    },
    {
      id: "edge-35519",
      source: "30",
      target: "31",
    },
    {
      id: "edge-35520",
      source: "31",
      target: "32",
    },
    {
      id: "edge-35521",
      source: "32",
      target: "33",
    },
    {
      id: "edge-35522",
      source: "33",
      target: "34",
    },
    {
      id: "edge-35523",
      source: "34",
      target: "35",
    },
  ],
};
