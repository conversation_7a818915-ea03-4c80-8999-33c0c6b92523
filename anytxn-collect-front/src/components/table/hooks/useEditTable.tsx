import _ from 'lodash';
import useIntlCustom from '@/hooks/useIntlCustom';
import { EDITTABLE_VALUE_TYPE, I18N_COMON_PAGENAME, NOTIFICATION_TYPE, OPERATE_TYPE } from '@/constants/publicConstant';
import { getActionColumnWidth, renderButton } from '@/utils/comUtil';
import { IEditableColumns } from '@/types/IForm';
import { ReactNode } from 'react';

const useEditTable = ({
  intlPrefix,
  rowKey,
  optionCount,
  orderFiled,
  editableKeys,
  tableData,
  setTableData,
  getOptionList,
  onAction,
}) => {
  // hooks变量
  const { translate, getEditTableSelectOption, openNotificationTip } = useIntlCustom();
  // 渲染按钮
  const buttonRender = (row, index, action): ReactNode => {
    const optionList = getOptionList(row);
    return optionList.map((optionObj) => {
      const { optionType, disabled = false, loading = false } = optionObj;
      const defaultParam = { type: optionType, disabled, loading };
      switch (optionType) {
        // 编辑
        case OPERATE_TYPE.edit:
          return renderButton({
            ...defaultParam,
            onClick: () => {
              action?.startEditable?.(row[rowKey]);
              onAction(optionType, row);
            },
          });
        // 查看详情
        case OPERATE_TYPE.detail:
          return renderButton({ ...defaultParam, onClick: () => onAction(optionType, row) });
        // 删除
        case OPERATE_TYPE.delete:
          return renderButton({
            ...defaultParam,
            onClick: () => {
              setTableData((preValue: Array<any>) => preValue.filter((item: any) => item[rowKey] !== row[rowKey]));
              onAction(optionType, row);
            },
          });
        // 复制
        case OPERATE_TYPE.copy:
          return renderButton({
            ...defaultParam,
            onClick: () => {
              const val = JSON.parse(JSON.stringify(tableData));
              val.push({ ...row, [`${rowKey}`]: Date.now(), isAdd: true });
              setTableData(val);
              action?.startEditable?.(val.at(-1)[rowKey]);
              onAction(optionType, row);
            },
          });
        // 上移/下移
        case OPERATE_TYPE.up:
        case OPERATE_TYPE.down:
          return renderButton({
            ...defaultParam,
            onClick: () => {
              handleSwitchOrder(index, optionType);
              onAction(optionType, row);
            },
          });
        default:
          return <></>;
      }
    });
  };
  // 处理列表数据
  const getColumns = (columns): IEditableColumns[] => {
    const res = columns.map((col) => {
      const result = { ...col };
      const { prefix = intlPrefix, title, valueType, data, showKey = false, optionPrefix } = col;
      const titleLabel = translate(prefix, title);
      let otherParam = {};
      // select下拉框国际化
      if (valueType === EDITTABLE_VALUE_TYPE.SELECT && !_.isNil(data)) {
        const valueEnum = getEditTableSelectOption(data, showKey, _.isNil(optionPrefix) ? prefix : optionPrefix);
        otherParam = { valueEnum };
        delete result.data;
        delete result.showKey;
      }
      return { ...result, ...otherParam, title: titleLabel };
    });
    if (optionCount) {
      res.push({
        title: translate(I18N_COMON_PAGENAME.COMMON, 'option'),
        valueType: 'option',
        align: 'center',
        width: getActionColumnWidth(optionCount),
        render: (text, row, index, action) => buttonRender(row, index, action),
      });
    }
    return res;
  };
  // 处理数据排序上移下移
  const handleSwitchOrder = (index: number, optionType: string): void => {
    // 是否有正在编辑行
    if (editableKeys.length) {
      openNotificationTip('common', NOTIFICATION_TYPE.WARNING, 'formEditing');
      return;
    }
    // 上移时判断是否第一条数据
    if (optionType === OPERATE_TYPE.up && index === 0) {
      openNotificationTip('common', NOTIFICATION_TYPE.WARNING, 'firstRow');
      return;
    }
    if (optionType === OPERATE_TYPE.down && index === tableData.length - 1) {
      openNotificationTip('common', NOTIFICATION_TYPE.WARNING, 'lastRow');
      return;
    }
    // 要交换的第二个元素的索引
    let changeIndex = optionType === OPERATE_TYPE.up ? index - 1 : index + 1;
    setTableData((preTableData) => {
      const originRow = { ...preTableData[index] };
      const changeRow = { ...preTableData[changeIndex] };
      const changeRowoParam = orderFiled ? { [orderFiled]: changeRow?.[orderFiled] } : {};
      const orginRowoParam = orderFiled ? { [orderFiled]: originRow?.[orderFiled] } : {};
      const result = [...preTableData]; // 创建数组的一个副本
      const [item1, item2] = [result[index], result[changeIndex]]; // 解构赋值获取要交换的元素
      result[index] = { ...item2, ...orginRowoParam };
      result[changeIndex] = { ...item1, ...changeRowoParam };
      return result;
    });
  };

  return {
    getColumns,
  };
};

export default useEditTable;
