import React, { FC, useEffect, useState } from "react";
import { Table } from "antd";
import _ from "lodash";
import useIntlCustom from "@/hooks/useIntlCustom";
import { I18N_COMON_PAGENAME, REQUEST_TYPE } from "@/constants/publicConstant";
import {
  getActionColumnWidth,
  renderColumnByType,
  showTotal,
} from "@/utils/comUtil";
import { ICommonTableProps } from "./type/ICommonTable";
import styles from "./table.module.css";

const PaginationCom = React.lazy(() => import("./Pagination"));

// 默认分页数据
const defaultPagination = { currentPage: 1, defaultPageSize: 50, pageSize: 50 };

const CommonTable: FC<ICommonTableProps> = ({
  rowKey = "incId",
  columns = [],
  optionList = [],
  paginationConfig = { ...defaultPagination },
  dataSource = [],
  loading = false,
  intlPrefix,
  components,
  serviceType = "param",
  props = {},
  onAction = () => {},
  onChange = () => {},
}) => {
  const { translate, renderActionColumn } = useIntlCustom();
  const [tableData, setTableData] = useState<any[]>([]);

  useEffect(() => {
    Array.isArray(dataSource) ? setTableData(dataSource) : setTableData([]);
  }, [dataSource]);
  // 处理数据
  const setColumns = (columns) => {
    const result = columns.map((column: any) => {
      column = { width: 120, align: "center", ellipsis: true, ...column };
      let { title, key, prefix, children } = column;
      // 可以每列单独传入prefix，否则用公共的intlPrefix
      column.prefix = prefix || intlPrefix;
      title = translate(column.prefix, title);
      // 根据valueType格式化数据
      const tempColumn = renderColumnByType(column);
      delete tempColumn.valueType;
      if (Array.isArray(children)) {
        tempColumn.children = setColumns(children);
      }
      return { ...tempColumn, title };
    });

    const optionColumn = optionList.length
      ? [
          {
            title: translate(I18N_COMON_PAGENAME.COMMON, "option"),
            dataIndex: "option",
            key: "option",
            align: "center",
            fixed: "right",
            width: getActionColumnWidth(optionList.length),
            render: (_, row) =>
              renderActionColumn(optionList, (type: string) =>
                onAction(type, row, optionList)
              ),
          },
        ]
      : [];

    return [...result, ...optionColumn];
  };

  const pagination =
    typeof paginationConfig === "object"
      ? { ...defaultPagination, ...paginationConfig, showTotal }
      : paginationConfig;
  const col: Array<any> = setColumns(columns);

  // 参数前端自己实现分页，走以下逻辑
  const paginationComNode = {
    param: (
      <PaginationCom
        tableData={tableData}
        handleTableChange={(val) => onChange?.(val)}
      />
    ),
    business: <></>,
    mock: <></>,
  };

  return (
    <>
      <Table
        className={
          serviceType === REQUEST_TYPE.param ? styles.commonParamTable : ""
        }
        tableLayout="fixed"
        rowKey={rowKey}
        components={components}
        dataSource={tableData}
        columns={col}
        loading={loading}
        // serviceType 有值说明不需要前端自己做分页，没值值需要前端实现分页功能
        pagination={serviceType === REQUEST_TYPE.param ? false : pagination}
        scroll={{
          x: "max-content",
          y: 2000,
        }}
        onChange={onChange}
        {...props}
      />
      {paginationComNode[serviceType]}
    </>
  );
};

export default CommonTable;
