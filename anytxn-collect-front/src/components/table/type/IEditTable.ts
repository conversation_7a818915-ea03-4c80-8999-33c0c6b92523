import { IEditableColumns } from '@/types/IForm';

/**
 * EditTable的props约束接口
 */
export interface IEditTableProps {
  /**
   * EditTable实例
   */
  ref?: any;
  /**
   * 表格唯一索引
   */
  rowKey: string;
  /**
   * 列表配置
   */
  columns: Array<IEditableColumns>;
  /**
   * 表格数据源
   */
  dataSource: Array<any>;
  /**
   * 国际化模块前缀
   */
  intlPrefix: string;
  /**
   * 是否可编辑
   */
  canEdit?: boolean;
  /**
   * 限制可编辑单行还是多行
   */
  editableType?: 'single' | 'multiple';
  /**
   * 是否展示新增按钮
   */
  showCreate?: boolean;
  /**
   * 操作列按钮数量
   */
  optionCount?: number;
   /**
   * 表格是否正在加载
   */
  loading?: boolean;
  /**
   * 上移下移功能需要交换的字段
   */
  orderFiled?: string;
  editTableType?: string;
  /**
   * 获取操作列
   */
  getOptionList?: (row) => [];
  /**
   * 新增事件
   */
  onCreate?: () => object;
  /**
   * 操作列回调
   */
  onAction?: (type, data) => void;
  /**
   * EditTable挂载回调
   */
  afterMount?: (form) => void;
  /**
   * EditTable挂载回调
   */
  onFormChange?: (key, row, originRow) => void;
  /**
   * EditTable挂载回调
   */
  onFormSave?: (key, row, originRow) => void;
}
