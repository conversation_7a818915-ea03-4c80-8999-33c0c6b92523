/**
 * 统一封装的树状表格
 * update by heshan on 2024/12/28.
 */
import { Button, Table } from 'antd';
import React, { useState } from 'react';
import { SwapOutlined } from '@ant-design/icons';
import useIntlCustom from '@/hooks/useIntlCustom';
import { getActionColumnWidth, renderColumnByType } from '@/utils/comUtil';
import { ITreeTableProps } from './type/ITreeTable';

const TreeTable: React.FC<ITreeTableProps> = ({
  rowKey = 'id',
  dataSource = [],
  columns = [],
  intlPrefix,
  optionList = [],
  showCollapsed = true,
  loading = false,
  props,
  onAction = () => {},
}) => {
  // hook变量
  const { translate, renderActionColumn } = useIntlCustom();
  const [expanded, setExpanded] = useState(false);
  const [expandedRowKeys, setExpandedRowKeys] = useState(['']);

  // 逻辑处理
  // 处理列数据
  const setColumns = () => {
    const result = columns.map((column: any) => {
      let { valueType, dictType, title, key, data, prefix } = column;
      title = key === 'option' ? title : translate(prefix || intlPrefix, title);
      if (!valueType) {
        return { align: 'center', ellipsis: true, ...column, title };
      }
      const tempColumn = renderColumnByType(column);
      delete tempColumn.valueType;
      return { ...tempColumn, title };
    });

    const optionColumn = optionList.length
      ? [
          {
            title: translate('common', 'option'),
            dataIndex: 'option',
            key: 'option',
            align: 'center',
            fixed: 'right',
            width: getActionColumnWidth(optionList.length),
            render: (_, row) => renderActionColumn(optionList, (type: string) => onAction(type, row)),
          },
        ]
      : [];

    return [...result, ...optionColumn];
  };
  // 设置列
  const col: Array<any> = setColumns();
  // 获取树层数据的key
  const getTreeKey = (data) => {
    const keys: Array<any> = [];
    // 递归方法
    const recursionData = (childrenData) => {
      childrenData.forEach((item) => {
        keys.push(item[rowKey]);
        if (item.children && item.children.length > 0) {
          recursionData(item.children);
        }
      });
    };
    recursionData(data);
    return keys;
  };
  // 事件处理
  // 处理全部折叠和全部展开
  const handleCollapsed = () => {
    setExpanded(!expanded);
    const tableData = JSON.parse(JSON.stringify(dataSource));
    if (!expanded) {
      setExpandedRowKeys(getTreeKey(tableData));
    } else {
      setExpandedRowKeys([]);
    }
  };
  // 处理单个展开和折叠
  const handleExpand = (expanded, record) => {
    if (expanded) {
      setExpandedRowKeys([...expandedRowKeys, record.key]);
    } else {
      setExpandedRowKeys(expandedRowKeys.filter((t) => t !== record.key));
    }
  };
  return (
    <>
      {showCollapsed ? (
        <Button type="text" icon={<SwapOutlined />} style={{ justifyContent: 'flex-start' }} onClick={handleCollapsed}>
          {expanded ? translate('common', 'unexpanded') : translate('common', 'expanded')}
        </Button>
      ) : null}
      <Table
        size="small"
        rowKey={rowKey}
        columns={col}
        loading={loading}
        dataSource={dataSource}
        expandable={{
          expandedRowKeys: expandedRowKeys,
          onExpand: (expanded, record) => handleExpand(expanded, record),
        }}
        pagination={false}
        scroll={{
          y: '100%',
        }}
        {...props}
      />
    </>
  );
};

export default TreeTable;
