import React, { FC, useRef, useEffect } from 'react';
import * as echarts from 'echarts';

const EchartsComponent: any = ({ options, layoutStyle = { width: '100%', height: '360px' } }) => {
  const chartsRef = useRef(null);

  useEffect(() => {
    const chartInstance = echarts.init(chartsRef.current);
    chartInstance.setOption(options);
    return () => {
      if (chartInstance) {
        chartInstance.dispose();
      }
    };
  }, [options]);

  return <div className="app-container" ref={chartsRef} style={layoutStyle} />;
};
export default EchartsComponent;
