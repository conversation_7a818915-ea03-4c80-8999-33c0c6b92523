/**
 * Created by ca<PERSON>un on 2025/6/6.
 */
import React from "react";

interface IGridProp {
  children?: React.ReactNode;
  isBottom?: boolean;
  isRight?: boolean;
  style?: React.CSSProperties;
}

const GridRow: React.FC<IGridProp> = ({ children, isBottom, style }) => (
  <div
    className={isBottom ? "flex-row flex-1 m-r-s" : "flex-row flex-1 m-r-s m-b"}
    style={style}
  >
    {children}
  </div>
);

const GridCol: React.FC<IGridProp> = ({ children, isRight, style }) => (
  <div
    className={isRight ? "flex-col flex-1 m-l-s" : "flex-col flex-1 m-r-s"}
    style={style}
  >
    {children}
  </div>
);

export { GridRow, GridCol };
