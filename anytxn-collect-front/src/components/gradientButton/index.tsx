/**
 * Created by ca<PERSON><PERSON> on 2024/6/1.
 * update by he<PERSON> on 2024/12/28.
 */

import _ from "lodash";
import { Button, ConfigProvider } from "antd";
import { themeColor } from "@/constants/styleConstant";
import { LOCAL } from "@/constants/publicConstant";
import { IButtonProps } from "./IGradientButton";

// 主题
const theme = localStorage.getItem(LOCAL.THEME) || "";

export default ({
  children,
  color = theme || themeColor.green,
  type,
  size,
  icon,
  style,
  disabled = false,
  loading = false,
  onClick = () => {},
}: IButtonProps) => (
  <ConfigProvider
    theme={{
      components: {
        Button: {
          colorPrimary: color,
        },
      },
    }}
  >
    <Button
      type={type || "primary"}
      size={size || "middle"}
      disabled={disabled}
      loading={loading}
      icon={icon}
      style={style}
      onClick={_.debounce(onClick)}
    >
      {children}
    </Button>
  </ConfigProvider>
);
