/*外层布局*/
.g6-layout {
  display: flex;
  flex: 1;
  flex-direction: row;
  overflow-y: auto;
  margin-bottom: 1rem;
  border: 1px solid var(--gray-three);
}
/*左侧节点菜单栏*/
.g6-layout > .g6-panel {
  background: #eee;
  border-right: 1px dashed var(--gray-three);
  min-width: 6rem;
  overflow: auto;
}
.g6-layout > .g6-panel > ul {
  display: block;
  list-style-type: disc;
  margin-block-start: 1rem;
  margin-block-end: 1rem;
  margin-inline-start: 0;
  margin-inline-end: 0;
  padding-inline-start: 0;
}
.g6-layout > .g6-panel > ul > li {
  display: flex;
  align-items: center;
  border: 1px solid rgba(140, 140, 140, 0.5);
  border-radius: 0.25rem;
  margin: 0.5rem 0.25rem;
  padding: 0.5rem;
  height: 2.5rem;
  list-style-type: none;
}
.g6-layout > .g6-panel > ul > li:hover {
  background: var(--color-one);
  border: 1px solid var(--color-two);
  cursor: move;
}
.g6-layout > .g6-panel > ul > li > img {
  margin-right: 0.5rem;
}
/*画布容器*/
.g6-layout > .g6-container {
  overflow: hidden;
  padding: 0.5rem;
  border: 1px solid var(--gray-three);
}
/*自定义工具栏*/
.g6-custom-toolbar {
  background-color: var(--gray-two);
  border: 1px solid var(--gray-light);
  margin-top: 0.2rem;
  margin-left: 0.2rem;
}
/*覆盖内置工具栏子项样式 g6-toolbar-item*/
.g6-custom-toolbar > .g6-toolbar-item {
  width: 1.2rem !important;
  height: 1.2rem !important;
  padding: 0.5rem 0.6rem !important;
}

