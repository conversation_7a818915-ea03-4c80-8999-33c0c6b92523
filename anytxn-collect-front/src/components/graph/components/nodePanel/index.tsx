/**
 * 节点配置面板组件
 */
import { FC } from 'react';
import { INodePanelProps } from "../../IGraph";

const NodePanel: FC<INodePanelProps> = ({ nodeList = [], onDragNodeEnd = (data) => {} }) => {
  // 事件处理
  const handleDragStart = (e, item) => {
    let data: any = {};
    Object.assign(data, item);
    data.x = e.clientX;
    data.y = e.clientY;
  };
  const handleDragEnd = (e, item) => {
    // todo 处理边界问题
    let data: any = {};
    Object.assign(data, item);
    data.x = e.clientX;
    data.y = e.clientY;
    onDragNodeEnd(data);
  };

  // 组件渲染
  return (
    <div className="g6-panel">
      <ul>
        {nodeList.map((item) => {
          return (
            <li
              key={item.nodeType}
              draggable
              onDragStart={e => handleDragStart(e, item)}
              onDragEnd={e => handleDragEnd(e, item)}
            >
              <img alt="图标" width="20" height="20" src={item.src} />
              <span>{item.label}</span>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default NodePanel;
