import { FC, useEffect, useRef, useState } from "react";
import { Graph, NodeEvent, EdgeEvent, CanvasEvent } from "@antv/g6";
import { IG6Props } from "./IGraph";
import NodePanel from "./components/nodePanel";
import { nodeDefaultConfig, edgeDefaultConfig } from "./common/config";
import { addNode } from "./common/nodeUtil";
import "./assets/graph.css";

const gap = 2; // 间隙误差像素，但和padding没有完全匹配
const nodePanelWidth = 96 + gap; // 左侧节点菜单宽度
const G6: FC<IG6Props> = ({
  dragNodeList,
  options,
  onRender,
  onDestroy,
  bindEvents = {},
}) => {
  // 内部变量
  const graphRef = useRef<Graph>();
  const refLayout = useRef<HTMLDivElement>(null);
  const refContainer = useRef<HTMLDivElement>(null);
  const [layoutHeight, setLayoutHeight] = useState(0);
  const [layoutWidth, setLayoutWidth] = useState(0);

  // 副作用
  useEffect(() => {
    // 初始化布局大小、渲染画布、绑定事件
    initLayoutSize();
    // 移除销毁画布
    return () => {
      const graph = graphRef.current;
      if (graph) {
        graph?.destroy();
        onDestroy?.();
        graphRef.current = undefined;
      }
    };
  }, []);
  useEffect(() => {
    // 初始化画布、绑定事件
    if (layoutWidth && layoutHeight && options) {
      const graph = initGraph();
      graphRef.current = graph;
      graph
        .render()
        .then(() => {
          onRender?.(graph);
          bindEvent();
        })
        .catch((e) => console.log("graph-render-error", e));
    }
  }, [layoutWidth, layoutHeight, options]);

  // 逻辑处理
  const initLayoutSize = () => {
    if (refLayout.current) {
      const styles = window.getComputedStyle(refLayout.current);
      setLayoutHeight(parseInt(styles.height));
      setLayoutWidth(parseInt(styles.width));
    }
  };
  const initGraph = () => {
    const graph = new Graph({
      // 画布挂在元素
      container: refContainer.current!,
      width: layoutWidth - (dragNodeList ? nodePanelWidth : gap),
      height: layoutHeight - gap,
      // 动画（可传入）
      animation: false,
      // 节点配置（可传入）
      node: { ...nodeDefaultConfig },
      // 边（可传入）
      edge: { ...edgeDefaultConfig },
      // 布局配置（可传入）
      // layout: {...layoutConfig},
      // 居中显示（可传入）
      // autoFit: "center",
      // 自动调整大小（可传入）
      autoResize: true,
      // 行为列表（可传入）
      behaviors: ["zoom-canvas", "drag-element", "drag-canvas"],
      // 可覆盖上述子项
      ...options,
    });
    return graph;
  };

  // 事件处理
  const handleDragNodeEnd = (data) => {
    const graph = graphRef.current;
    if (graph) {
      console.log("handleDragNodeEnd", data);
      const model = addNode(graph, data);
      graph.addNodeData([model]);
      graph.render();
    }
  };

  // 穷举所有绑定事件
  const bindEvent = () => {
    const graph = graphRef.current;
    // 监听节点单击事件
    bindEvents.onNodeClick &&
      graph?.on(NodeEvent.CLICK, bindEvents.onNodeClick);
    bindEvents.onNodeDbClick &&
      graph?.on(NodeEvent.DBLCLICK, bindEvents.onNodeDbClick);
    // 监听右键菜单事件
    // bindEvents.onNodeContextMenu && graph.on(NodeEvent.CONTEXT_MENU, bindEvents.onNodeContextMenu);
    // bindEvents.onEdgeContextMenu && graph.on(EdgeEvent.CONTEXT_MENU, bindEvents.onEdgeContextMenu);
    // bindEvents.onCanvasContextMenu && graph.on(CanvasEvent.CONTEXT_MENU, bindEvents.onCanvasContextMenu);
    // 监听节点拖拽事件
    // graph.on('node:dragstart', (e) => {
    //     const { item } = e;
    //     graph.setItemState(item, 'active', true);
    // });
    // graph.on('node:dragend', (e) => {
    //     const { item } = e;
    //     graph.setItemState(item, 'active', false);
    // });
    // graph.on('node:drag', (e) => {
    //     const { item, target } = e;
    //     const model = item.getModel();
    //     model.x = target.x;
    //     model.y = target.y;
    //     graph.updateItem(item, model);
    // });

    // 监听连接桩事件
    bindEvents.onPortDragStart &&
      graph.on("port:dragstart", bindEvents.onPortDragStart);
    bindEvents.onPortDragEnd &&
      graph.on("port:dragend", bindEvents.onPortDragEnd);
    bindEvents.onPortDrag && graph.on("port:drag", bindEvents.onPortDrag);
    bindEvents.onPortConnect &&
      graph.on("port:connect", bindEvents.onPortConnect);
  };

  return (
    <div ref={refLayout} className="g6-layout">
      {dragNodeList && (
        <NodePanel nodeList={dragNodeList} onDragNodeEnd={handleDragNodeEnd} />
      )}
      <div ref={refContainer} className="g6-container" />
    </div>
  );
};

export default G6;
