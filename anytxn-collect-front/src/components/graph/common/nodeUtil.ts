import { NODE_TYPE } from '@/constants/graphConstants';
import { decimalToBase64 } from "@/utils/mathUtil";

/**
 * 创建新ID：前缀+时间戳转成64进制
 * todo 可能加10ms定时器能最大限度减少重复
 * @param prefix
 * @returns {string}
 */
export const createNewId = (prefix) => {
  return `${prefix}-${decimalToBase64(new Date().valueOf())}`
}
/**
 * 新增节点数据
 */
export const addNode = (graph, data, transform = true) => {
  const { label, type, x, y, src = '' } = data;

  // 将视图坐标转换为画布坐标
  const canvasPoint = transform ? graph?.getCanvasByClient([x, y]) || [0, 0] : [x, y];

  const otherStyle = type === NODE_TYPE.IMAGE ? { src } : {};

  return {
    id: createNewId('node'),
    label,
    type,
    style: { x: canvasPoint[0], y: canvasPoint[1], ...otherStyle },
    data,
  };
};
