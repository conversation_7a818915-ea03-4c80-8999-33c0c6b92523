/**
 * 无弹层，无搜索，内容区域直接展示form或者其他展示内容
 */

import React from 'react';
import RouterBar from '@/layouts/header/RouterBar';
import { FormCard } from '@/components';
import useIntlCustom from '@/hooks/useIntlCustom';
import { I18N_COMON_PAGENAME } from '@/constants/publicConstant';
import { IPageFormTemplate } from './types/IPageFormTemplate';
import styles from './templates.module.css';
import cls from 'classnames';

const PageFormTemplate: React.FC<IPageFormTemplate> = ({ prefix, title, extra, children }) => {
  const intlPrefix = prefix || I18N_COMON_PAGENAME.COMMON;
  const { formateHtmlText } = useIntlCustom();

  return (
    <>
      <div className={cls(styles.pageRouterBar, 'm-b')}>
        <RouterBar />
      </div>
      <FormCard
        className={styles.pageContent}
        showAction={false}
        title={formateHtmlText(intlPrefix, title)}
        extra={extra}
      >
        {children}
      </FormCard>
    </>
  );
};
export default PageFormTemplate;
