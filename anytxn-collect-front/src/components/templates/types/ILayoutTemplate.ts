/**
 * LayoutTemplate组件props约束接口
 */
export interface ILayoutTemplateProps {
  /**
   * 搜索框组件
   */
  searchChildren?: React.ReactNode;
  /**
   * 表格组件
   */
  tableChildren?: React.ReactNode;
  /**
   * 表单组件
   */
  formChildren?: React.ReactNode;
  /**
   * 表单操作类型
   */
  type?: string;
  /**
   * 下方组件渲染类型
   */
  childrenType: string;
  /**
   * 国际化前缀
   */
  intlPrefix: string;
  /**
   * 是否展示表单上方按钮
   */
  isShowCardExtra?: boolean | undefined;
  /**
   * 是否展示返回按钮
   */
  cardShowBack: boolean;
  /**
   * 控制样式为overflow:'auto'/'hidden'
   */
  shouldScroll?: boolean;
  /**
   * 下方组件标题
   */
  cardTitle: React.ReactNode | string;
  /**
   * 下方Card组件的extra属性
   */
  cardExtra?: React.ReactNode;
  /**
   * 返回事件回调
   */
  onCardBack?: () => void;
}
