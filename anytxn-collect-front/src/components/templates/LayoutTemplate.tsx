import React, { Suspense } from 'react';
import _ from 'lodash';
import { Spin, Divider } from 'antd';
import { useLocation } from 'ice';
import { motion, AnimatePresence } from 'framer-motion';
import RouterBar from '@/layouts/header/RouterBar';
import { COMPONENT_TYPE } from '@/constants/publicConstant';
import FormCard from '../form/FormCard';
import styles from './templates.module.css';
import { ILayoutTemplateProps } from './types/ILayoutTemplate';

const LayoutTemplate: React.FC<ILayoutTemplateProps> = ({
  searchChildren,
  tableChildren,
  formChildren,
  type = '',
  childrenType,
  intlPrefix,
  isShowCardExtra,
  cardShowBack = false,
  shouldScroll = true,
  cardTitle = '',
  cardExtra,
  onCardBack,
}) => {
  const cardChildren = {
    [COMPONENT_TYPE.TABLE]: tableChildren,
    [COMPONENT_TYPE.FORM]: formChildren,
  };
  const location = useLocation();

  return (
    <AnimatePresence>
      <motion.div
        key={location.pathname}
        layoutScroll
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.8, ease: 'easeInOut' }}
        className="flex-col flex-1 height100"
      >
        <>
          <div className="app-block m-b">
            {_.isNil(isShowCardExtra) ? (
              <>
                <RouterBar />
                <Divider className="m-tb-0" />
              </>
            ) : null}
            {searchChildren}
          </div>
          <Suspense fallback={<Spin size="large" />}>
            <FormCard
              title={cardTitle}
              type={type}
              showBack={cardShowBack}
              extra={cardExtra}
              onBack={onCardBack}
              showAction={false}
              intlPrefix={intlPrefix}
              shouldScroll={shouldScroll}
              isShowCardExtra={isShowCardExtra}
              className={styles.layoutContent}
            >
              {cardChildren[childrenType]}
            </FormCard>
          </Suspense>
        </>
      </motion.div>
    </AnimatePresence>
  );
};

export default LayoutTemplate;
