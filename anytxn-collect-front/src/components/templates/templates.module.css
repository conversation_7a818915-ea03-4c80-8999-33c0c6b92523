.layoutContent {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layoutContent :global {
  .ant-card {
    padding-right: 0;
  }
  .ant-card-head,
  .ant-card-body {
    padding: 0;
  }
  .ant-card-body {
    height: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .ant-table-wrapper {
      height: 100%;
      .ant-spin-nested-loading {
        height: 100%;
      }
      .ant-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        .ant-table {
          flex: 1;
          overflow: hidden;
          padding-bottom: 1rem;
          .ant-table-container {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            .ant-table-body {
              height: 100%;
              /* 底部滚动条高度 */
              padding-bottom: 0.5px;
            }
            .ant-table-content {
              height: 100%;
              margin-bottom: 10px;
            }
          }
        }
        .ant-pagination {
          margin-top: 0;
        }
      }
    }
  }
  .ant-table-tbody > tr > td {
    padding: 0.5rem;
  }
}
.pageRouterBar {
  border-radius: 0.5rem;
  padding: 0 1.5rem;
  box-shadow: 0 0.15rem 0.3rem rgba(0, 0, 0, 0.06), 0 0 0.45rem rgba(0, 0, 0, 0.03);
  background: var(--gray-one);
}
.pageContent :global {
  padding: 0;
  overflow: hidden;
  height: 100%;
}
