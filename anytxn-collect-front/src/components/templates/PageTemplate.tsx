import React, { useEffect, useState, useRef, Suspense } from 'react';
import { Spin } from 'antd';
import _ from 'lodash';
import { OPERATE_TYPE, COMPONENT_TYPE, DEFAULT_PAGINATION } from '@/constants/publicConstant';
import { LayoutTemplate } from '@/components';
import { isGetTableData } from '@/utils/comUtil';
import useDictStore from '@/hooks/useDictStore';
import { IPageTemplateProps } from './types/IPageTemplate';
import usePageSearch from './hooks/usePageSearch';
import usePageUrl from './hooks/usePageUrl';
import usePageTable from './hooks/usePageTable';
import usePageForm from './hooks/usePageForm';
import usePageFormAction from './hooks/usePageFormAction';

const PageTemplate: React.FC<IPageTemplateProps> = ({
  searchConfig,
  tableConfig,
  formConfig,
  formActionConfig,
  urlObj,
  cardTitle,
  intlPrefix,
  dictEnum,
  extra,
  fomrExtra,
  onAction = () => {},
}) => {
  // hooks变量
  // 操作类型
  const [type, setType] = useState<string>('');
  // 操作行数据
  const [rowData, setRowData] = useState<object>({});
  // 页面下方展示的组件类型
  const [childrenType, setChildrenType] = useState<string>(COMPONENT_TYPE.TABLE);
  // 分页配置
  const [pagination, setPagination] = useState({ ...DEFAULT_PAGINATION });
  // 是否展示返回按钮
  const [cardShowBack, setCardShowBack] = useState<boolean>(false);
  const formRef = useRef<any>(null);
  // 页面需要查询的字典或参数字段
  useDictStore(dictEnum);

  // 请求接口hooks
  const { tableData, tableLoading, paginationConfig, getTableData, getRequestFunc } = usePageUrl({
    searchConfig,
    tableConfig,
    urlObj,
  });
  // search组件hooks
  const { searchValue, setSearchBtnDisabled, renderSearch } = usePageSearch({
    searchConfig,
    intlPrefix,
    pagination,
    getTableData,
  });

  // table组件hooks
  const { renderTable, handleAction } = usePageTable({
    tableConfig,
    urlObj,
    pagination,
    paginationConfig,
    tableData,
    tableLoading,
    intlPrefix,
    setType,
    setRowData,
    setChildrenType,
    setCardShowBack,
    setSearchBtnDisabled,
    setPagination,
    getRequestFunc,
    getTableData,
    onAction,
  });

  // form组件hooks
  const { renderForm } = usePageForm({ formRef, formConfig, type });

  // 返回事件
  const handleCardBack = (): void => {
    setType('');
    setChildrenType(COMPONENT_TYPE.TABLE);
    setCardShowBack(false);
    setSearchBtnDisabled(false);
    onAction(OPERATE_TYPE.list, {});
  };
  // formAction组件hooks
  const { renderFormAction } = usePageFormAction({
    formRef,
    type,
    rowData,
    searchValue,
    pagination,
    childrenType,
    urlObj,
    formConfig,
    formActionConfig,
    extra,
    fomrExtra,
    getTableData,
    handleAction,
    handleCardBack,
  });

  // 副作用
  useEffect(() => {
    // 查询条件有必输，首次则不掉用接口/或者是参数首次不调用,common组件会调用分页查询数据
    if (isGetTableData(searchConfig)) return;
    getTableData();
  }, []);

  return (
    <Suspense fallback={<Spin size="large" />}>
      <LayoutTemplate
        searchChildren={renderSearch()}
        tableChildren={renderTable()}
        formChildren={renderForm()}
        childrenType={childrenType}
        type={type}
        intlPrefix={intlPrefix}
        isShowCardExtra={_.get(formConfig, 'props.isShowCardExtra')}
        cardShowBack={cardShowBack}
        cardTitle={cardTitle}
        cardExtra={renderFormAction()}
        onCardBack={handleCardBack}
      />
    </Suspense>
  );
};

export default PageTemplate;
