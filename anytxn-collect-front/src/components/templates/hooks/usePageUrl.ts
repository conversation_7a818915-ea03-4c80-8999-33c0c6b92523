// 处理pageTemplate请求
import { useState } from 'react';
import { TablePaginationConfig } from 'antd';
import _ from 'lodash';
import { DEFAULT_PAGINATION, OPERATE_TYPE, REQUEST_TYPE } from '@/constants/publicConstant';
import services from '@/services';
import { IUsePageUrl } from '../types/IPageTemplate';

const usePageUrl = ({ searchConfig, tableConfig, urlObj }: IUsePageUrl) => {
  const [tableLoading, setTableLoading] = useState(false);
  const [tableData, setTableData] = useState<object[]>([]);
  const [paginationConfig, setPaginationConfig] = useState<TablePaginationConfig | false>(false);
  // 定义接口请求，拿到真实请求servece,（mock 本地使用，后面不需要）
  const REQUEST_TYPE_OBJ = {
    list: {
      param: services.common.getTableListData,
      business: services.common.getTableListDataBiz,
    },
    edit: {
      param: services.common.getEditPost,
      business: services.common.getEditPostBiz,
    },
    create: {
      param: services.common.getEditPost,
      business: services.common.getEditPostBiz,
    },
    detail: {
      param: services.common.getTableListData,
      business: services.common.getCommonListData,
    },
    delete: {
      param: services.common.getEditPost,
      business: services.common.getEditPostBiz,
    },
  };

  // 请求service，拿到真正的接口，通用接口不满足，可以通过requestFuncMap传操作方法
  const getRequestFunc = (requestType: string): any => {
    // 判断根据requestType是否能拿到数据，增加自定义逻辑处理
    const keyArr = Object.keys(REQUEST_TYPE_OBJ);
    if (!keyArr.includes(requestType)) {
      const { customFunc } = urlObj;
      return customFunc?.[requestType];
    }
    const { serviceType = REQUEST_TYPE.param, requestFuncMap } = urlObj;
    return requestFuncMap?.[requestType] || REQUEST_TYPE_OBJ[requestType][serviceType];
  };

  // 查询页面表格数据
  const getTableData = async (pagination = { ...DEFAULT_PAGINATION }, searchData = {}): Promise<void> => {
    // demo使用，有传入dataSource则不调用接口
    if (tableConfig?.dataSource) return;
    try {
      const { getRequestData } = urlObj;
      const { defaultParam = {}, resetValue } = searchConfig || {};
      const getListFunc = getRequestFunc(OPERATE_TYPE.list);
      const params = {
        url: urlObj?.list,
        searchValue: { ...resetValue, ...searchData },
        ...defaultParam,
      };
      // 是否需要额外处理传参
      const getPostData = getRequestData ? getRequestData({ ...params }, OPERATE_TYPE.list) : params;
      if (!getListFunc) {
        setTableData([]);
        return;
      }
      setTableLoading(true);
      // 增加分页参数
      const otherParam = tableConfig?.showPagination ? { pagination } : {};
      const res = await getListFunc({
        ...getPostData,
        ...otherParam,
      });
      const { data, total } = res;
      Array.isArray(data) && setTableData(data);
      tableConfig?.showPagination && setPaginationConfig({ showSizeChanger: true, showQuickJumper: true, total });
    } catch (error) {
      console.error('error=', error);
      setTableData([]);
    } finally {
      setTableLoading(false);
    }
  };
  return { tableData, tableLoading, paginationConfig, getTableData, getRequestFunc };
};

export default usePageUrl;
