// pageTemplate的表单组件
import _ from 'lodash';
import { FormTemplate } from '@/components';
import { OPERATE_TYPE } from '@/constants/publicConstant';
import { ICustomFormConfig, IFormConfig, IUsePageForm } from '../types/IPageTemplate';

const usePageForm = ({ formRef, formConfig, type }: IUsePageForm) => {
  // 渲染组件
  const renderForm = () => {
    // 没有传formConfig则不渲染
    if (_.isNil(formConfig)) {
      return <></>;
    }
    const { config = [], data, intlPrefix = '', onChange, props, showMaintenance = true } = formConfig as IFormConfig;
    const { isCustom = false, customChildren } = formConfig as ICustomFormConfig;
    const isCustomize =
      isCustom &&
      customChildren &&
      ![OPERATE_TYPE.detail, OPERATE_TYPE.create, OPERATE_TYPE.edit, OPERATE_TYPE.delete].includes(type);
    // 是否使用自定义的组件
    return isCustomize ? (
      customChildren[type]
    ) : (
      <FormTemplate
        ref={formRef}
        config={config}
        initialData={data}
        loading={false}
        intlPrefix={intlPrefix}
        canEdit={type !== OPERATE_TYPE.detail}
        showMaintenance={showMaintenance}
        onChange={onChange}
        {...props}
      />
    );
  };

  return { renderForm };
};

export default usePageForm;
