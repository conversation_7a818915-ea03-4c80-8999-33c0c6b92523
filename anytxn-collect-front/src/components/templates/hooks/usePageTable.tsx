import { CommonTable } from '@/components';
import {
  COMPONENT_TYPE,
  I18N_COMON_PAGENAME,
  NOTIFICATION_TYPE,
  OPERATE_TYPE,
  REQUEST_TYPE,
} from '@/constants/publicConstant';
import useIntlCustom from '@/hooks/useIntlCustom';
import services from '@/services';
import { IUsePageTable } from '../types/IPageTemplate';
import { ICommonTableActionItem } from '@/types/ICommon';

const usePageTable = ({
  tableConfig,
  urlObj,
  pagination,
  paginationConfig,
  tableData,
  tableLoading,
  intlPrefix,
  setType,
  setRowData,
  setChildrenType,
  setCardShowBack,
  setSearchBtnDisabled,
  setPagination,
  getTableData,
  onAction,
}: IUsePageTable) => {
  const { openNotificationTip } = useIntlCustom();
  // 删除
  const deleteData = async (data: object, type: string): Promise<void> => {
    try {
      const { delete: deleteUrl, getRequestData } = urlObj;
      const deleteFunc = services.common.getEditPostBiz;
      const getPostData = getRequestData ? getRequestData(data, type) : data;
      if (!deleteFunc) {
        return;
      }
      const result = await deleteFunc({
        url: deleteUrl,
        ...getPostData,
      });
      if (result) {
        getTableData(pagination);
        openNotificationTip(I18N_COMON_PAGENAME.COMMON, NOTIFICATION_TYPE.SUCCESS, 'deleteSuccess', 1);
      } else {
        openNotificationTip(I18N_COMON_PAGENAME.COMMON, NOTIFICATION_TYPE.ERROR, 'deleteFailed');
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 操作事件
  const handleAction = async (actionType: any, row: object, optionList: Array<string | ICommonTableActionItem>) => {
    await onAction(actionType, row);
    // 删除
    if (actionType === OPERATE_TYPE.delete) {
      return deleteData(row, OPERATE_TYPE.delete);
    }
    setType(actionType);
    setRowData(row);
    // 是否展示自定义操作按钮内容
    const isOnCustomize: any = optionList?.find((item: any) => item?.type === actionType);
    if (isOnCustomize?.onCustomize) {
      return isOnCustomize.onCustomize(true);
    }
    setChildrenType(COMPONENT_TYPE.FORM);
    setCardShowBack(true);
    setSearchBtnDisabled(true);
  };

  // 改变分页事件
  const handleTableChange = (pagination) => {
    const { current: currentPage, pageSize } = pagination;
    setPagination({ currentPage, pageSize });
    getTableData({ currentPage, pageSize });
  };

  // 渲染表格
  const renderTable = () => {
    const {
      rowKey,
      columns = [],
      optionList = [OPERATE_TYPE.detail, OPERATE_TYPE.edit, OPERATE_TYPE.delete],
      props,
      dataSource,
    } = tableConfig || {};

    const { serviceType = REQUEST_TYPE.param } = urlObj;

    return (
      <CommonTable
        rowKey={rowKey}
        serviceType={serviceType}
        columns={columns}
        optionList={optionList}
        paginationConfig={paginationConfig === false ? paginationConfig : { ...paginationConfig, ...pagination }}
        dataSource={dataSource || tableData}
        loading={tableLoading}
        intlPrefix={intlPrefix}
        onAction={handleAction}
        onChange={handleTableChange}
        props={props}
      />
    );
  };
  return {
    renderTable,
    handleAction,
  };
};

export default usePageTable;
