import { FC, memo } from "react";
import config from "./config";
import { Modal } from "antd";
import useIntlCustom from "@/hooks/useIntlCustom";
import { I18N_COMON_PAGENAME } from "@/constants/publicConstant";

interface IModalComType {
  type: string;
  open: boolean;
  content: any;
  onClose: () => void;
  onSubmit: () => void;
}

const ModalCom: FC<IModalComType> = ({
  type,
  open,
  content,
  onClose,
  onSubmit,
}) => {
  const { translate } = useIntlCustom();
  const handleOk = () => {
    onSubmit?.();
  };

  return (
    <Modal
      width="55%"
      open={open}
      centered
      destroyOnClose
      maskClosable={false}
      {...config[type]}
      title={translate(I18N_COMON_PAGENAME.CALL_PARAM, config[type].title)}
      onCancel={onClose}
      onOk={handleOk}
    >
      {content()}
    </Modal>
  );
};

export default memo(ModalCom);
