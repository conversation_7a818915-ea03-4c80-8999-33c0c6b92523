/**
 * 描述组件
 * update by he<PERSON> on 2024/12/28.
 */
import React from 'react';
import { Descriptions } from 'antd';
import _ from 'lodash';
import useIntlCustom from '@/hooks/useIntlCustom';
import { renderValueByType } from '@/utils/comUtil';
import { IComDescriptionItem, IComDescriptionProps } from './ITabDescription';

const TabDescription: React.FC<IComDescriptionProps> = ({ descriptionsItems, bordered, size, prefix, num }) => {
  // hook变量
  const { translate } = useIntlCustom();
  // 逻辑处理
  // 格式化数据
  const formatDescriptionsItems = () => {
    let arr: Array<any> = [];
    for (let i in descriptionsItems) {
      const itemType = typeof descriptionsItems[i];
      const item = descriptionsItems[i];
      // 对象和字符串分别处理
      if (item && itemType === 'object') {
        const item: IComDescriptionItem = descriptionsItems[i] as IComDescriptionItem;
        let childrenVal: any = '';
        // 判断prefixKey属性是否存在，并做数据处理
        let itemPrefix = _.isNil(item?.prefixKey) ? prefix : item.prefixKey;
        if (!_.isNil(item?.context)) {
          childrenVal = renderValueByType(item.context, { ...item, prefix: itemPrefix });
        }
        const labelVal = itemPrefix ? translate(itemPrefix, i) : i;
        const props = _.isNil(item.props) ? {} : { ...item.props };
        arr.push({
          key: i,
          label: labelVal,
          children: childrenVal,
          ...props,
        });
      } else {
        arr.push({
          key: i,
          label: translate(prefix, i),
          children: descriptionsItems[i],
        });
      }
    }
    return arr;
  };
  return (
    <Descriptions
      bordered={bordered}
      size={size}
      column={num || 3}
      items={formatDescriptionsItems()}
      style={{ width: '380px' }}
    />
  );
};

export default TabDescription;
