import { TOptionItem } from '@/types/TCommon';

// TabDescription组件入参
export interface IComDescriptionProps {
  descriptionsItems: Record<string, string | number | IComDescriptionItem> | object;
  prefix: string; // 国际化key
  num?: number;
  size?: any;
  bordered?: boolean;
}

export interface IComDescriptionItem {
  /**
   * 字段单独配置的国际化前缀
   */
  prefixKey?: string;
  /**
   * 需要格式化的值内容
   */
  context: string;
  /**
   * 格式化的类型
   */
  valueType: string;
  /**
   * 需要国际化的枚举值
   */
  data?: TOptionItem[];
  /**
   * 传递给items的属性
   */
  props?: object;
}
