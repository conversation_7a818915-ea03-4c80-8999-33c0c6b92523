/**
 * todo 未完成
 */
import { Col, Form, Row } from 'antd';
import styleConstant from '@/constants/styleConstant';
import { IFormList } from '@/types/IForm';
import useIntlCustom from '@/hooks/useIntlCustom';

const useFormList = ({ renderOneFields, intlPrefix }) => {
  // hooks变量
  const { translate } = useIntlCustom();
  const renderFormList = (data: IFormList) => {
    const { listName, showButton = false, renderButton } = data;
    return (
      <Form.List name={listName}>
        {(fields, { add, remove }) => {
          return (
            <>
              {fields.map((field) => {
                return (
                  <Row key={field.key}>
                    {data.data?.map((item) =>
                      item.hideItem && item.hideItem(field) ? null : (
                        <Col key={field.key + item.name} span={11} offset={1}>
                          <Form.Item
                            name={[field.name, item.name]}
                            label={[translate(intlPrefix, item.label)]}
                            rules={item.rules}
                          >
                            {item.type ? (
                              renderOneFields(item)
                            ) : (
                              <div style={{ ...styleConstant.width100, textAlign: 'center' }}>{item.text}</div>
                            )}
                          </Form.Item>
                        </Col>
                      ),
                    )}
                    {showButton && renderButton && renderButton(field, add, remove)}
                  </Row>
                );
              })}
            </>
          );
        }}
      </Form.List>
    );
  };
  return { renderFormList };
};

export default useFormList;
