// todo: 与graph合并
import { FC, useEffect, useRef, useState } from 'react';
import { Graph } from '@antv/g6';
import { getFontSize } from '@/utils/comUtil';
import styleConstant from '@/constants/styleConstant';
import CustomNode from './shape/CustomNode';
import { IRulesGraphProps } from './IRulesGraph';
import './shape';

// 默认样式
const defaultConfig = {
  nodeSep: getFontSize('6rem'),
  rankSep: getFontSize('6rem'),
  edgeStoke: styleConstant.grayMain,
};

const RulesGraph: FC<IRulesGraphProps> = ({
  id,
  data,
  config = {},
  nodeProps = {},
  afterMounted = () => {},
  setSize,
  onNodeClick = () => {},
  onValueChange = () => {},
}) => {
  const [siderBtnDom, setSiderBtnDom] = useState<any>();
  const ref = useRef<HTMLDivElement>(null);
  let graph;
  const { edgeConfig = {}, nodeConfig = {}, layoutConfig = {} } = config;

  // 副作用
  useEffect(() => {
    renderGraph();
    bindEvent();
    return () => {
      removeEvent();
    };
  }, []);

  // 逻辑处理
  const renderGraph = () => {
    if (!graph && ref.current) {
      graph = new Graph({
        container: ref.current, // 画布
        data, // 画布数据
        animation: false, // 动画
        // 布局
        layout: {
          type: 'dagre',
          rankdir: 'LR', // H / V / LR / RL / TB / BT
          rankSep: defaultConfig.rankSep,
          ...layoutConfig,
        },
        // 节点配置
        node: {
          type: 'react',
          style: (d: any) => {
            const size = setSize ? setSize(d) : [200, 48];
            return {
              component: <CustomNode graph={graph} data={d} onChange={onValueChange} {...nodeProps} />,
              // size: [120, 38],
              size,
            };
          },
          ...nodeConfig,
        },
        // 边
        edge: {
          type: 'custom-polyline',
          style: {
            stroke: defaultConfig.edgeStoke,
          },
          ...edgeConfig,
        },
        behaviors: ['drag-canvas'], // 画布交互事件
        autoResize: true, // 自动调整大小
      });
    }
    graph.render();
    afterMounted(graph);
  };

  // 绑定事件
  const bindEvent = () => {
    graph.on('node:click', handleNodeClick);
    const siderBtnDom = document.getElementsByClassName('sider-collapsed-button');
    if (siderBtnDom && siderBtnDom.length) {
      setSiderBtnDom(siderBtnDom[0]);
      siderBtnDom[0].addEventListener('click', handleSiderClick);
    }
  };

  // 移除事件
  const removeEvent = () => {
    graph.off('node:click', handleNodeClick);
    siderBtnDom && siderBtnDom.removeEventListener('click', handleSiderClick);
  };

  // 事件处理
  // 节点点击事件
  const handleNodeClick = (e) => {
    onNodeClick(e);
  };
  // 左侧菜单栏展开收缩事件
  const handleSiderClick = (e) => {
    graph?.onResize();
  };

  return <div ref={ref} id={id} style={{ ...styleConstant.width100, ...styleConstant.height100 }} />;
};

export default RulesGraph;
