.wrapper {
  /* width: max-content; */
  height: 100%;
  position: relative;
  border-radius: 5px;
  box-shadow: 5px 5px 10px var(--gray-main);
  .leftButton {
    width: 3rem;
    height: 100%;
    border: 0;
    border-radius: 5px;
    background-color: var(--gray-main);
    .hoverSetButton {
      font-size: 1.5rem;
      color: var(--gray-one);
    }
  }
  .rightWrapper {
    width: 100%;
    height: 100%;
    border-radius: 5px;
  }
  .rightInput {
    width: 100%;
    border: none;
  }
  .rightSelect {
    border-width: 0;
    box-shadow: none;
  }
}
