// 直角折线
import { BaseEdge } from '@antv/g6';

class PolylineEdge extends BaseEdge {
  getKeyPath(): any {
    const { x: sourceX, y: sourceY, size: sourceSize } = this.sourceNode.attributes;
    const { x: targetX, y: targetY, size: targetSize } = this.targetNode.attributes;

    let startX = sourceX + sourceSize[0];
    let startY = sourceY + sourceSize[1] / 2;
    let endX = targetX;
    let endY = targetY + targetSize[1] / 2;

    return [
      ['M', startX, startY],
      ['L', (startX + endX) / 2, startY],
      ['L', (startX + endX) / 2, endY],
      ['L', targetX, endY],
    ];
  }
}

export default PolylineEdge;
