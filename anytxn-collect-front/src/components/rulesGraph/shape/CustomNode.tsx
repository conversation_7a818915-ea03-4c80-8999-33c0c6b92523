// 自定义React节点
import { useEffect, useState, cloneElement } from 'react';
import _ from 'lodash';
import { SettingFilled } from '@ant-design/icons';
import { Button, Dropdown, InputNumber, Select } from 'antd';
import { IntlProvider } from 'react-intl';
import { messages } from '@/locales';
import styleConstant, { themeMap, greenTheme } from '@/constants/styleConstant';
import { COMPONENT_TYPE, LANGUAGE_LIST, LOCAL } from '@/constants/publicConstant';
import { ICustomNodeProps } from '../IRulesGraph';
import styles from './customNode.module.css';

const locale = localStorage.getItem(LOCAL.LOCALE) || LANGUAGE_LIST.CN.locale;
const theme: string | null = localStorage.getItem(LOCAL.THEME);
const themeColor = theme ? themeMap[theme] : greenTheme;

const defaultLeftStyle = {
  color: styleConstant.grayOne,
  backgroundColor: themeColor.colorMain,
};

const CustomNode = (props: ICustomNodeProps) => {
  const { graph, data, canEdit, getMenuItems = (data) => [], onChange, onMenuClick = () => {} } = props;
  const { config = {}, data: nodeData, rightChildren } = data;
  const { type, style = {}, valueMenuItems = [], valueKey = 'value', showSearch = false } = config;
  const { nodeNo } = nodeData;
  const [comValue, setComValue] = useState<string>('');
  const [selectValue, setSelectValue] = useState<string>('');
  const [isHover, setIsHover] = useState<boolean>(false);
  let { wapperStyle = {}, leftStyle = {}, rightWrapperStyle = {}, inputStyle = {}, selectStyle = {} } = style;
  leftStyle = { ...defaultLeftStyle, ...leftStyle };
  selectStyle = { ...selectStyle };

  useEffect(() => {
    const key = nodeData[valueKey];
    type === COMPONENT_TYPE.INPUT_NUMBER ? setComValue(key) : setSelectValue(key);
  }, []);

  // 更新数据
  const handleChange = (key) => {
    const temp = _.find(valueMenuItems, { key });
    setSelectValue(key);
    onChange(graph, { ...temp, [valueKey]: key }, data);
  };
  const handleValueChange = (value) => {
    setComValue(value);
    onChange(graph, { [valueKey]: value }, data);
  };
  const handleBtnHover = (hover: boolean): void => {
    if (!canEdit) {
      return;
    }
    setIsHover(hover);
  };

  // 左边菜单
  const menuProps = {
    items: getMenuItems(data),
    onClick: (e) => onMenuClick(graph, e, data),
  };

  const compChildren = {
    [COMPONENT_TYPE.INPUT_NUMBER]: (
      <InputNumber
        value={comValue}
        disabled={!canEdit}
        className={styles.rightInput}
        style={inputStyle}
        onChange={handleValueChange}
      />
    ),
    [COMPONENT_TYPE.SELECT]: (
      <Select
        value={selectValue}
        showSearch={showSearch}
        optionFilterProp="label"
        options={valueMenuItems}
        variant="borderless"
        disabled={!canEdit}
        className={styles.rightSelect}
        style={selectStyle}
        onChange={handleChange}
      />
    ),
  };

  return (
    <IntlProvider locale={locale} messages={messages[locale]}>
      <div className={`flex-row flex-align-center ${styles.wrapper}`} style={wapperStyle}>
        <Dropdown menu={menuProps} trigger={['click']}>
          <Button
            type="link"
            size="large"
            color="default"
            variant="text"
            disabled={!canEdit}
            className={`flex-row flex-justify-center flex-align-center ${styles.leftButton}`}
            style={leftStyle}
            onMouseEnter={() => handleBtnHover(true)}
            onMouseLeave={() => handleBtnHover(false)}
          >
            {nodeNo && !isHover ? nodeNo : <SettingFilled className={styles.hoverSetButton} />}
          </Button>
        </Dropdown>
        {/* 传入ReactNode, 并注入props */}
        {rightChildren ? (
          cloneElement(rightChildren, props)
        ) : (
          <div
            className={`flex-1 flex-row flex-justify-center flex-align-center m-lr ${styles.rightWrapper}`}
            style={rightWrapperStyle}
          >
            {compChildren[type]}
          </div>
        )}
      </div>
    </IntlProvider>
  );
};

export default CustomNode;
