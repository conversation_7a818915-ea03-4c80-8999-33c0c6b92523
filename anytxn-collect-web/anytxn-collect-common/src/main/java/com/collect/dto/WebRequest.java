package com.collect.dto;


import com.collect.constant.PageInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;
import java.util.Optional;

/**
 * The type Web request.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @date 2024 /11/1 11:26
 */
public class WebRequest<T> {

    private WebRequestHeader header;

    /**
     * 数据结构.
     * 用于调用API 网关的处理
     */
    private T body;

    /**
     * Gets header.
     *
     * @return the header
     */
    public WebRequestHeader getHeader() {
        return header;
    }

    /**
     * Sets header.
     *
     * @param header the header
     */
    public void setHeader(final WebRequestHeader header) {
        this.header = header;
    }

    /**
     * Gets body.
     *
     * @return the body
     */
    public T getBody() {
        return body;
    }

    /**
     * Sets body.
     *
     * @param body the body
     */
    public void setBody(final T body) {
        this.body = body;
    }

    /**
     * Gets page info.
     *
     * @return the page info
     */
    public PageInfo getPageInfo() {

        ObjectMapper objectMapper = new ObjectMapper();
        Map objectMap = objectMapper.convertValue(body, Map.class);
        return PageInfo.page((Integer) Optional.ofNullable(objectMap.get("start")).orElse(1), (Integer) Optional.ofNullable(objectMap.get("pageSize")).orElse(50));
//        WebRequestHeader requestHeader = Optional.ofNullable(header).orElse(new WebRequestHeader());
//        return PageInfo.page(Optional.ofNullable(requestHeader.getPageNo()).orElse(1), Optional.ofNullable(requestHeader.getPageSize()).orElse(15));
    }
}
