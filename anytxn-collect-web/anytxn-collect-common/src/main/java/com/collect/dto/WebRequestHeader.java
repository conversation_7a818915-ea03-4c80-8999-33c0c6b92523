package com.collect.dto;

/**
 * 统一的请求体的header.
 *
 * <AUTHOR>
 * @date 2024 /11/1 11:28
 */
public class WebRequestHeader {

    /**
     * msgId.
     */
    private String msgId;

    /**
     * 当前页码.
     * @mock 1
     */
    private Integer pageNo;

    /**
     * 每页数据.
     * @mock 15
     */
    private Integer pageSize;

    /**
     * Gets msg id.
     *
     * @return the msg id
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * Sets msg id.
     *
     * @param msgId the msg id
     */
    public void setMsgId(final String msgId) {
        this.msgId = msgId;
    }

    /**
     * Gets page no.
     *
     * @return the page no
     */
    public Integer getPageNo() {
        return pageNo;
    }

    /**
     * Sets page no.
     *
     * @param pageNo the page no
     */
    public void setPageNo(final Integer pageNo) {
        this.pageNo = pageNo;
    }

    /**
     * Gets page size.
     *
     * @return the page size
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * Sets page size.
     *
     * @param pageSize the page size
     */
    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }
}
