package com.collect.dto;


import com.collect.constant.PageInfo;

/**
 * The type Web response dto.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @date 2024 /12/19 15:14
 */
public class WebResponseDO<T> {

    private final PageInfo pageInfo;

    private final T data;

    /**
     * Instantiates a new Web response do.
     *
     * @param pageInfo the page info
     * @param data     the data
     */
    public WebResponseDO(final PageInfo pageInfo, final T data) {
        this.pageInfo = pageInfo;
        this.data = data;
    }

    /**
     * Of web response do.
     *
     * @param <T>  the type parameter
     * @param data the data
     * @return the web response do
     */
    public static <T> WebResponseDO<T> of(final T data) {
        return new WebResponseDO<>(null, data);
    }

    /**
     * Of web response do.
     *
     * @param <T>      the type parameter
     * @param pageInfo the page info
     * @param data     the data
     * @return the web response do
     */
    public static <T> WebResponseDO<T> of(final PageInfo pageInfo, final T data) {
        return new WebResponseDO<>(pageInfo, data);
    }

    /**
     * Gets page info.
     *
     * @return the page info
     */
    public PageInfo getPageInfo() {
        return pageInfo;
    }

    /**
     * Gets data.
     *
     * @return the data
     */
    public T getData() {
        return data;
    }
}
