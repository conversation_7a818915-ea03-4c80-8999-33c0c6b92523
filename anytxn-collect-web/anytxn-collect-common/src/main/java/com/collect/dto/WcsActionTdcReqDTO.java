package com.collect.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/13 17:06
 **/
public class WcsActionTdcReqDTO {
    private Integer id; // 自增ID
    private String createUser; // 创建人
    private String updateUser; // 修改人
    private String orgCustNbr; // 客户号
    private String caseCode; // 案件编号
    private String caseResultFlag; // 案件催收小结标识
    private String contactName; // 姓名
    private String phoneNmbr; // 电话号码
    private String relation; // 关系
    private String actionCode; // 联系结果
    private String contactRemarks; // 沟通备注
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private LocalDateTime reviewDate; // 复核时间
    private BigDecimal commitmentAmount; // 承诺金额
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private LocalDateTime commitmentDate; // 承诺时间
    private String dailFlag; // 是否继续拨号
    private String callDuration; // 通话时长
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private LocalDateTime startTime; // 通话开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private LocalDateTime endTime; // 通话结束时间
    private String audioCode; // 录音编号

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", shape = JsonFormat.Shape.STRING)
    private LocalDateTime actionDate; // 行动日期
    private String obType; // 外呼类型 0-外呼 1-ivr
    private String telType; // 电话类型 h住宅电话 m手机 o单位电话 b备用电话 s小灵通 f老家电
    private String callType; // 通话方向 1-呼入 2-呼出 3-转接

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }


    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }


    public String getOrgCustNbr() {
        return orgCustNbr;
    }

    public void setOrgCustNbr(String orgCustNbr) {
        this.orgCustNbr = orgCustNbr;
    }

    public String getCaseCode() {
        return caseCode;
    }

    public void setCaseCode(String caseCode) {
        this.caseCode = caseCode;
    }

    public String getCaseResultFlag() {
        return caseResultFlag;
    }

    public void setCaseResultFlag(String caseResultFlag) {
        this.caseResultFlag = caseResultFlag;
    }

    public String getContactName() {
        return contactName;
    }

    public void setContactName(String contactName) {
        this.contactName = contactName;
    }

    public String getPhoneNmbr() {
        return phoneNmbr;
    }

    public void setPhoneNmbr(String phoneNmbr) {
        this.phoneNmbr = phoneNmbr;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getActionCode() {
        return actionCode;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public String getContactRemarks() {
        return contactRemarks;
    }

    public void setContactRemarks(String contactRemarks) {
        this.contactRemarks = contactRemarks;
    }

    public LocalDateTime getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(LocalDateTime reviewDate) {
        this.reviewDate = reviewDate;
    }

    public BigDecimal getCommitmentAmount() {
        return commitmentAmount;
    }

    public void setCommitmentAmount(BigDecimal commitmentAmount) {
        this.commitmentAmount = commitmentAmount;
    }

    public LocalDateTime getCommitmentDate() {
        return commitmentDate;
    }

    public void setCommitmentDate(LocalDateTime commitmentDate) {
        this.commitmentDate = commitmentDate;
    }

    public String getDailFlag() {
        return dailFlag;
    }

    public void setDailFlag(String dailFlag) {
        this.dailFlag = dailFlag;
    }

    public String getCallDuration() {
        return callDuration;
    }

    public void setCallDuration(String callDuration) {
        this.callDuration = callDuration;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getAudioCode() {
        return audioCode;
    }

    public void setAudioCode(String audioCode) {
        this.audioCode = audioCode;
    }

    public LocalDateTime getActionDate() {
        return actionDate;
    }

    public void setActionDate(LocalDateTime actionDate) {
        this.actionDate = actionDate;
    }

    public String getObType() {
        return obType;
    }

    public void setObType(String obType) {
        this.obType = obType;
    }

    public String getTelType() {
        return telType;
    }

    public void setTelType(String telType) {
        this.telType = telType;
    }

    public String getCallType() {
        return callType;
    }

    public void setCallType(String callType) {
        this.callType = callType;
    }
}
