package com.collect.dto;

/**
 * 工作台设置表实体类
 */
public class WorkBenchConfigReqDTO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 工作台名称
     */
    private String workBenchName;

    /**
     * 辅助功能
     */
    private String auxiliaryFunctions;

    /**
     * 展示模块
     */
    private String workBenchModel;

    /**
     * 催收信息Tab字段
     */
    private String callFields;

    /**
     * 客户信息Tab字段
     */
    private String customFields;

    /**
     * 联系信息Tab字段
     */
    private String contactField;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 创建人
     */
    private String createUser;

    // Getter/Setter 方法省略...

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getAuxiliaryFunctions() {
        return auxiliaryFunctions;
    }

    public void setAuxiliaryFunctions(String auxiliaryFunctions) {
        this.auxiliaryFunctions = auxiliaryFunctions;
    }

    public String getWorkBenchName() {
        return workBenchName;
    }

    public void setWorkBenchName(String workBenchName) {
        this.workBenchName = workBenchName;
    }

    public String getWorkBenchModel() {
        return workBenchModel;
    }

    public void setWorkBenchModel(String workBenchModel) {
        this.workBenchModel = workBenchModel;
    }

    public String getCallFields() {
        return callFields;
    }

    public void setCallFields(String callFields) {
        this.callFields = callFields;
    }

    public String getCustomFields() {
        return customFields;
    }

    public void setCustomFields(String customFields) {
        this.customFields = customFields;
    }

    public String getContactField() {
        return contactField;
    }

    public void setContactField(String contactField) {
        this.contactField = contactField;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }
}