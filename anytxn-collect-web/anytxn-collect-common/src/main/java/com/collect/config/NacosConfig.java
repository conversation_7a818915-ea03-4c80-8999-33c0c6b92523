package com.collect.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/11/28 下午3:19
 */
@Configuration
public class NacosConfig {
    @Value("${spring.cloud.nacos.discovery.server-addr}")
    private String nacosServerAddr;
    @Value("${spring.cloud.nacos.discovery.namespace}")
    private String namespace;
    @Value("${app.app-name}")
    private String service;

    public String getNacosServerAddr() {
        return nacosServerAddr;
    }
    public String getNamespace() {
        return namespace;
    }

    public String getService() {
        return service;
    }
}
