package com.collect.util;

import com.anytxn.base.utils.JsonUtils;
import com.anytxn.base.utils.Uuid;
import com.anytxn.base.utils.json.JsonMap;
import com.collect.constant.WebConstant;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:53
 **/
public class HttpUtil {

    private static final RestTemplate restTemplate = new RestTemplate();
    private static final String dataKey = "body";
    /**
     * 发送POST请求
     *
     * @param url 请求的URL
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @param <T> 响应类型
     * @return 响应实体
     */
    public static <T> ResponseEntity<T> sendPostRequest(String url, String requestBody, Class<T> responseType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JsonMap jsonMap = JsonUtils.toJsonMap(requestBody);
        JsonMap body = jsonMap.getJsonMap(dataKey);
        Map<String, Object> requestBodyMap = buildParamBody(body, url);
        HttpEntity<Object> requestEntity = new HttpEntity<>(requestBodyMap, headers);
        return restTemplate.postForEntity(url, requestEntity, responseType);
    }

    private static Map<String, Object> buildParamBody(JsonMap body, String url){
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> headerMap = new HashMap<>();
        headerMap.put(WebConstant.GID, Uuid.generateUuid());
        headerMap.put(WebConstant.SRC_SYSTEM, WebConstant.SRC_SYSTEM_VALUE);
        headerMap.put(WebConstant.MSG_ID, url);
        headerMap.put(WebConstant.PATH, url);
        map.put(WebConstant.HEADER, headerMap);
        map.put(WebConstant.PARAM_SEQ_NO, Uuid.generateUuid());
        map.putAll(body);
        return map;
    }
}
