package com.collect.util;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12 17:55
 **/
public class PaginationUtil {
    /**
     * 对列表数据进行分页处理
     *
     * @param data     原始数据列表
     * @param start    分页起始位置
     * @param pageSize 每页大小
     * @return 分页后的数据列表
     */
    public static <T> List<T> paginate(List<T> data, int start, int pageSize) {
        if (data == null || data.isEmpty()) {
            return List.of();
        }

        int fromIndex = Math.max(0, start);
        int toIndex = Math.min(fromIndex + pageSize, data.size());

        return data.subList(fromIndex, toIndex);
    }
}
