package com.collect.util;

import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.anytxn.base.spring.SpringUtils;
import com.collect.config.NacosConfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:28
 **/
public class NacosServiceUtil {
    private static final Logger log = LoggerFactory.getLogger(NacosServiceUtil.class);
    private static String nacosAddr ;
    private static String namespace ;

    static {
        NacosConfig nacosConfig = SpringUtils.getBean(NacosConfig.class);
        nacosAddr = nacosConfig.getNacosServerAddr();
        namespace = nacosConfig.getNamespace();
        log.info("静态初始化: nacosAddr={}, namespace={}", nacosAddr, namespace);
    }

    /**
     * 获取服务实例列表
     * @param serviceName 服务名称
     * @return 服务实例列表
     */
    public static List<Instance> getServiceInstances(String serviceName) {
        try {
            log.info("开始获取服务实例，服务名: {}, Nacos地址: {}, 命名空间: {}", serviceName, nacosAddr, namespace);
            Properties properties = new Properties();
            properties.setProperty("serverAddr", nacosAddr);
            properties.setProperty("namespace", namespace);

            // 创建naming服务
            NamingService namingService = NamingFactory.createNamingService(properties);

            // 获取服务实例列表
            List<Instance> instances = namingService.selectInstances(serviceName, true);

            if (CollectionUtils.isEmpty(instances)) {
                return Collections.emptyList();
            }

            return instances;
        } catch (NacosException e) {
            throw new RuntimeException("获取服务列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取服务实例列表的第一个实例，如果列表为空则返回 null
     * @param serviceName 服务名称
     * @return 第一个服务实例或 null
     */
    public static Instance getFirstInstance(String serviceName) {
        List<Instance> instances = getServiceInstances(serviceName);
        if (CollectionUtils.isEmpty(instances)) {
            return null;
        }
        return instances.get(0);
    }
}
