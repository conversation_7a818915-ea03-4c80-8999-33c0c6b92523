package com.collect.assembly;

import com.collect.dto.WorkBenchConfigReqDTO;
import com.collect.entity.WorkBenchConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WorkBenchConfigAssembler {
    static WorkBenchConfigAssembler of() {
        return Mappers.getMapper(WorkBenchConfigAssembler.class);
    }

    WorkBenchConfigPO do2poObject(WorkBenchConfigReqDTO workBenchConfigReqDTO);
}
