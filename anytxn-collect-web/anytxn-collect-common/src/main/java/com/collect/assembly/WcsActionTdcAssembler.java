package com.collect.assembly;

import com.collect.dto.WcsActionTdcReqDTO;
import com.collect.entity.WcsActionTdcPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WcsActionTdcAssembler {
    static WcsActionTdcAssembler of() {
        return Mappers.getMapper(WcsActionTdcAssembler.class);
    }

    WcsActionTdcPO do2poObject(WcsActionTdcReqDTO wcsActionTdcReqDTO);
}
