package com.collect.assembly;

import com.anytxn.base.lang.mapstruct.BaseEnumMapStruct;

import com.collect.entity.RuleVariableDO;
import com.collect.entity.WebRuleVariablePO;
import com.collect.enums.RuleExecuteType;
import com.collect.enums.RuleValueType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * The interface Rule variable assembler.
 *
 * <AUTHOR>
 * @date 2024 /12/17 14:06
 */
@Mapper(uses = {BaseEnumMapStruct.class, RuleVariableAssembler.class})
public interface RuleVariableAssembler {

    /**
     * Po 2 do list.
     *
     * @param vp the vp
     * @return the list
     */
    @Mapping(target = "ruleExecuteType", source = "ruleExecuteType", qualifiedByName = "ruleExecuteTypeFromCode")
    @Mapping(target = "valueType", source = "valueType", qualifiedByName = "ruleValueTypeFromCode")
    RuleVariableDO po2doObject(WebRuleVariablePO vp);

    /**
     * Do 2 po object web rule variable po.
     *
     * @param pojo the pojo
     * @return the web rule variable po
     */
    @Mapping(target = "ruleExecuteType", source = "ruleExecuteType", qualifiedByName = "ruleExecuteTypeToCode")
    @Mapping(target = "valueType", source = "valueType", qualifiedByName = "enumToCode")
    WebRuleVariablePO do2poObject(RuleVariableDO pojo);


    @Named("ruleExecuteTypeToCode")
    static Integer ruleExecuteTypeToCode(RuleExecuteType[] types) {
        if (types == null || types.length == 0) {
            return null;
        }
        return RuleExecuteType.from(types);
    }

    /**
     * Rule execute type from code rule execute type.
     *
     * @param code the code
     * @return the rule execute type
     */
    @Named("ruleExecuteTypeFromCode")
    static RuleExecuteType[] ruleExecuteTypeFromCode(Integer code) {
        return RuleExecuteType.to(code);
    }

    /**
     * Rule value type from code rule value type.
     *
     * @param code the code
     * @return the rule value type
     */
    @Named("ruleValueTypeFromCode")
    static RuleValueType ruleValueTypeFromCode(Integer code) {
        return RuleValueType.getRuleValueType(code);
    }

    /**
     * Of mapper.
     *
     * @return the mapper
     */
    static RuleVariableAssembler of() {
        return Mappers.getMapper(RuleVariableAssembler.class);
    }
}
