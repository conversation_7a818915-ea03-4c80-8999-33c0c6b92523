package com.collect.assembly;

import com.collect.dto.WcsBasRepaymentReqDTO;
import com.collect.entity.WcsBasRepaymentPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WcsBasRepaymentAssembler {
    static WcsBasRepaymentAssembler of() {
        return Mappers.getMapper(WcsBasRepaymentAssembler.class);
    }

    WcsBasRepaymentPO do2poObject(WcsBasRepaymentReqDTO wcsBasRepaymentReqDTO);
}
