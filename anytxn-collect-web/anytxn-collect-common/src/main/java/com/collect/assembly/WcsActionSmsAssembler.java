package com.collect.assembly;

import com.collect.dto.WcsActionSmsReqDTO;
import com.collect.entity.WcsActionSmsPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface WcsActionSmsAssembler {
    static WcsActionSmsAssembler of() {
        return Mappers.getMapper(WcsActionSmsAssembler.class);
    }

    WcsActionSmsPO do2poObject(WcsActionSmsReqDTO wcsActionSmsReqDTO);
}
