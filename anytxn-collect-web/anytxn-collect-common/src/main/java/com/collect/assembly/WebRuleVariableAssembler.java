package com.collect.assembly;

import com.collect.dto.RuleVariableReqDTO;
import com.collect.entity.RuleVariableDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * The type Web rule variable assembler.
 *
 * <AUTHOR>
 * @date 2024 /12/19 15:20
 */
@Mapper
public interface WebRuleVariableAssembler {

    /**
     * Of web rule variable assembler.
     *
     * @return the web rule variable assembler
     */
    static WebRuleVariableAssembler of() {
        return Mappers.getMapper(WebRuleVariableAssembler.class);
    }

    /**
     * Dto to do object rule variable do.
     *
     * @param ruleVariableReqDTO the rule variable req dto
     * @return the rule variable do
     */
    RuleVariableDO dtoToDoObject(RuleVariableReqDTO ruleVariableReqDTO);

    /**
     * Dto to do object rule variable req dto.
     *
     * @param ruleVariableReqDTO the rule variable req dto
     * @return the rule variable req dto
     */
    List<RuleVariableReqDTO> doToDtoList(List<RuleVariableDO> ruleVariableReqDTO);
}
