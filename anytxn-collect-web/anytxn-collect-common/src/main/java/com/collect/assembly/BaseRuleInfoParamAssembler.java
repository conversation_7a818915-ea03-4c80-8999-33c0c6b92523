package com.collect.assembly;

import com.collect.dto.BaseRuleInfoParamReqDTO;
import com.collect.entity.BaseRuleInfoParamPO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface BaseRuleInfoParamAssembler {
    static BaseRuleInfoParamAssembler of() {
        return Mappers.getMapper(BaseRuleInfoParamAssembler.class);
    }

    BaseRuleInfoParamPO do2poObject(BaseRuleInfoParamReqDTO baseRuleInfoParamReqDTO);
}
