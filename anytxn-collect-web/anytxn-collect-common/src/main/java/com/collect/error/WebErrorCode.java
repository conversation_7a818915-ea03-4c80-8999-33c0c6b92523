package com.collect.error;

import com.anytxn.base.error.ErrorCode;

/**
 * ErrorCode.
 *
 * <AUTHOR>
 * @date 2024 /11/1 14:41
 */
public enum WebErrorCode implements ErrorCode {

    /**
     * Not found web error code.
     * 无权访问
     */
    B1300001("B1300001", "B1300001"),

    /**
     * 认证失败.
     */
    B1300002("B1300002", "B1300002"),

    /**
     * 刷新token 失败.
     */
    B1300003("B1300003", "B1300003"),

    /**
     * B 1300004 web error code.
     */
    B1300004("B1300004", "B1300004"),

    /**
     * 菜單名字不能為空
     */
    B1300005("B1300005", "B1300005"),

    /**
     * 菜單保存失敗
     */
    B1300006("B1300006", "B1300006"),

    /**
     * 權限標示不能為空
     */
    B1300007("B1300007", "B1300007"),
    /**
     * 已存在相同数据.
     */
    B1300008("B1300008", "B1300008"),

    /**
     * 不存在相当的数据.
     */
    B1300009("B1300009", "B1300009"),
    /**
     * 没有分页条件
     */
    B1300010("B1300010", "B1300010"),
    /**
     * 调用API 网关异常
     */
    B1300011("B1300011", "B1300011"),
    /**
     * 查無菜單明細資料
     */
    B1300012("B1300012", "B1300012"),
    /**
     * 获取用户登录信息失败，请重新登录.
     */
    B1300013("B1300013", "B1300013"),
    ;

    private final String code;

    private final String message;

    WebErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getI18nKey() {
        return this.message;
    }
}
