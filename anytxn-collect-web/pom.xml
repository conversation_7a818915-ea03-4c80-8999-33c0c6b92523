<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.anytxn.base</groupId>
        <artifactId>anytxn-parent</artifactId>
        <version>2.2.0-CUB-SNAPSHOT</version>
    </parent>
    <artifactId>anytxn-collect-web</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>anytxn-collect-admin</module>
        <module>anytxn-collect-common</module>
        <module>anytxn-collect-entity</module>
        <module>anytxn-collect-engine</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <re.version>2.2.0-CUB-SNAPSHOT</re.version>
    </properties>

    <dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.anytxn.base</groupId>
            <artifactId>anytxn-started</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.anytxn.base</groupId>
                    <artifactId>anytxn-trace</artifactId>
                </exclusion>
            </exclusions>
            <version>${re.version}</version>
        </dependency>
<!--        <dependency>
            <groupId>com.anytxn.param</groupId>
            <artifactId>anytxn-param-sdk</artifactId>
            <version>${re.version}</version>
        </dependency>-->
        <dependency>
            <groupId>com.anytxn.common</groupId>
            <artifactId>anytxn-rule</artifactId>
            <version>${re.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>6.1.0</version>
        </dependency>
        <!-- MySQL连接器 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>

        <dependency>
            <groupId>com.anytxn.base</groupId>
            <artifactId>anytxn-collect-entity</artifactId>
            <version>${re.version}</version>
        </dependency>
        <dependency>
            <groupId>com.anytxn.base</groupId>
            <artifactId>anytxn-collect-common</artifactId>
            <version>${re.version}</version>
        </dependency>
        <dependency>
            <groupId>com.anytxn.base</groupId>
            <artifactId>anytxn-collect-engine</artifactId>
            <version>${re.version}</version>
        </dependency>
    </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>false</skipTests>
                    <testFailureIgnore>true</testFailureIgnore>
                    <argLine>${jacocoArgLine}</argLine>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <configuration>
                    <includes>
                        <include>com/anytxn/**/*</include>
                    </includes>
                    <excludes>
                        <exclude>com/anytxn/**/*Test.*</exclude>
                        <!-- 排除以PO结尾的类 -->
                        <exclude>**/*PO.*</exclude>
                        <!-- 排除以DO结尾的类 -->
                        <exclude>**/*DO.*</exclude>
                        <exclude>**/*Param.*</exclude>
                        <exclude>**/*Model.*</exclude>
                        <exclude>com/anytxn/auth/app/ApplicationStarted.*</exclude>
                        <exclude>com/anytxn/auth/converter/**/*</exclude>
                        <!--                        <exclude>com/anytxn/auth/proxy/model/**/*</exclude>-->
                        <exclude>com/anytxn/auth/po/**/*</exclude>
                        <exclude>com/anytxn/auth/mapper/**/*</exclude>
                        <exclude>com/anytxn/auth/api/dto/**/*</exclude>
                        <exclude>com/anytxn/auth/api/constants/**/*</exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <configuration>
                            <propertyName>jacocoArgLine</propertyName>
                        </configuration>
                    </execution>
                    <execution>
                        <id>post-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>