package com.collect.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 案件状态信息实体类
 * 对应数据库中的WCS_CAS_STATE表
 */
public class WcsCasState implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增 ID
     */
    private Integer id;
    /**
     * 案件号
     */
    private String caseCode;
    /**
     * 客户号
     */
    private String orgCustNbr;
    /**
     * 客户名称
     */
    private String custName;
    /**
     * 证件类型
     */
    private String icType;
    /**
     * 证件号
     */
    private String custIc;
    /**
     * 核销标
     */
    private String chargoffFlag;
    /**
     * 入催次数
     */
    private Integer countOdue;
    /**
     * 入催时间
     */
    private LocalDate dteIntoCollection;
    /**
     * 出催时间
     */
    private LocalDate dteOutCollection;
    /**
     * 首次入催时间
     */
    private LocalDate firstDate;
    /**
     * 上案件号
     */
    private String lastCaseCode;
    /**
     * 上次案件时间
     */
    private LocalDate lastCaseDate;
    /**
     * 诉讼日期
     */
    private LocalDate lawDate;
    /**
     * 诉讼标识
     */
    private String lawFlag;
    /**
     * 模块
     */
    private String model;
    /**
     * 当前状态
     */
    private String currState;
    /**
     * 案件状态
     */
    private String caseState;
    /**
     * 风险池编号
     */
    private String teamCode;
    /**
     * 最近行动时间
     */
    private LocalDate lastOpeTime;
    /**
     * 入组日期
     */
    private LocalDate inTeamDate;
    /**
     * 风险池原因码
     */
    private String reasonCode;
    /**
     * 失联决策代码
     */
    private String noneContactRuleCode;
    /**
     * 联系情况
     */
    private String contactCode;
    /**
     * 人工联系次数
     */
    private String contactTimes;
    /**
     * 结果完成时间
     */
    private String resultTime;
    /**
     * 呼入呼出
     */
    private String resultDirection;
    /**
     * 尝试联系这个帐户的次数
     */
    private String attemptCount;
    /**
     * 有效催收总数
     */
    private BigDecimal callTotal;
    /**
     * 上一模板
     */
    private String lastModel;
    /**
     * 英文名
     */
    private String ename;
    /**
     * 性别
     */
    private String eusex;
    /**
     * 账单类型
     */
    private String statementTypeAll;
    /**
     * 账单日
     */
    private Integer billingcycle;
    /**
     * 不良标识域
     */
    private String badnessCode;
    /**
     * 总欠款
     */
    private BigDecimal classIBalance;
    /**
     * 风险等级
     */
    private String riskrank;
    /**
     * 延滞天数
     */
    private Integer delayDays;
    /**
     * 活跃分期标识
     */
    private String activeInstallmentFlag;
    /**
     * 大额账户标识
     */
    private String productType;
    /**
     * 24 期状况
     */
    private String status24;
    /**
     * 委外标志
     */
    private String wcsOutcode;
    /**
     * 手别
     */
    private String handType;
    /**
     * 客户标签
     */
    private String custTags;
    /**
     * 分行号
     */
    private String bankNbr;
    /**
     * 支行号
     */
    private String branchNbr;
    /**
     * 已重审不能开通
     */
    private String restateNotOpen;
    /**
     * 永久信用额
     */
    private Integer crlimitPerm;
    /**
     * 临时额度
     */
    private Integer crlimitTemp;
    /**
     * 备忘事项
     */
    private String note;
    /**
     * 催收备注
     */
    private String custWcsMemo;
    /**
     * VIP 标识域
     */
    private String vipcode;
    /**
     * 证件有效期
     */
    private String permIdExp;
    /**
     * 出生日期
     */
    private LocalDate bornDate;
    /**
     * 居住类型
     */
    private String euTypeOfRes;
    /**
     * 居住年限
     */
    private String euPerOfRes;
    /**
     * 职业
     */
    private String job;
    /**
     * 单位名称
     */
    private String employer;
    /**
     * 年薪
     */
    private Integer income;
    /**
     * AB 客户群
     */
    private String abClass;
    /**
     * 封锁码
     */
    private String blockcode;
    /**
     * 封锁码日期
     */
    private String blockDate;
    /**
     * 旧封锁码
     */
    private String prvBlockcode;
    /**
     * 旧封锁码日期
     */
    private String prvBlockDate;
    /**
     * 手机号
     */
    private String telNum;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 毕业学校名称
     */
    private String school;
    /**
     * 学历
     */
    private String prexCustQualification;
    /**
     * 专业名称
     */
    private String major;
    /**
     * 婚姻状态
     */
    private String euMaritalStatus;
    /**
     * 持卡人类型
     */
    private String custType;
    /**
     * 持卡人状态
     */
    private String custStatus;
    /**
     * 持卡人状态改变日期
     */
    private String custStatusDate;
    /**
     * 担保人姓名
     */
    private String custGName;
    /**
     * 担保人电话
     */
    private String custGPhone;
    /**
     * 保证金
     */
    private BigDecimal depositArea;
    /**
     * 创建（开户）日期
     */
    private LocalDate custOpenDate;
    /**
     * 家庭电话
     */
    private String custPhone;
    /**
     * 备用联系电话
     */
    private String custBackPhone;
    /**
     * 单位电话 1
     */
    private String custEmpTel1;
    /**
     * 单位电话 2
     */
    private String custEmpTel2;
    /**
     * 单位电话 3
     */
    private String custEmpTel3;
    /**
     * 紧急联系人 1 关系
     */
    private String custGlRln1;
    /**
     * 紧急联系人 1 姓名
     */
    private String custGlNam1;
    /**
     * 紧急联系人 1 电话
     */
    private String custGlTel1;
    /**
     * 紧急联系人 2 关系
     */
    private String custGlRln2;
    /**
     * 紧急联系人 2 姓名
     */
    private String custGlNam2;
    /**
     * 紧急联系人 2 电话
     */
    private String custGlTel2;
    /**
     * 户籍地址
     */
    private String custHomeCityAddr;
    /**
     * 家庭地址
     */
    private String custAddr;
    /**
     * 单位地址
     */
    private String custGEmpA;
    /**
     * 现住地
     */
    private String custmAddr;
    /**
     * 账单地址
     */
    private String billAddr;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 获取 自增 ID
     *
     * @return id 自增 ID
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * 设置 自增 ID
     *
     * @param id 自增 ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取 案件号
     *
     * @return caseCode 案件号
     */
    public String getCaseCode() {
        return this.caseCode;
    }

    /**
     * 设置 案件号
     *
     * @param caseCode 案件号
     */
    public void setCaseCode(String caseCode) {
        this.caseCode = caseCode;
    }

    /**
     * 获取 客户号
     *
     * @return orgCustNbr 客户号
     */
    public String getOrgCustNbr() {
        return this.orgCustNbr;
    }

    /**
     * 设置 客户号
     *
     * @param orgCustNbr 客户号
     */
    public void setOrgCustNbr(String orgCustNbr) {
        this.orgCustNbr = orgCustNbr;
    }

    /**
     * 获取 客户名称
     *
     * @return custName 客户名称
     */
    public String getCustName() {
        return this.custName;
    }

    /**
     * 设置 客户名称
     *
     * @param custName 客户名称
     */
    public void setCustName(String custName) {
        this.custName = custName;
    }

    /**
     * 获取 证件类型
     *
     * @return icType 证件类型
     */
    public String getIcType() {
        return this.icType;
    }

    /**
     * 设置 证件类型
     *
     * @param icType 证件类型
     */
    public void setIcType(String icType) {
        this.icType = icType;
    }

    /**
     * 获取 证件号
     *
     * @return custIc 证件号
     */
    public String getCustIc() {
        return this.custIc;
    }

    /**
     * 设置 证件号
     *
     * @param custIc 证件号
     */
    public void setCustIc(String custIc) {
        this.custIc = custIc;
    }

    /**
     * 获取 核销标
     *
     * @return chargoffFlag 核销标
     */
    public String getChargoffFlag() {
        return this.chargoffFlag;
    }

    /**
     * 设置 核销标
     *
     * @param chargoffFlag 核销标
     */
    public void setChargoffFlag(String chargoffFlag) {
        this.chargoffFlag = chargoffFlag;
    }

    /**
     * 获取 入催次数
     *
     * @return countOdue 入催次数
     */
    public Integer getCountOdue() {
        return this.countOdue;
    }

    /**
     * 设置 入催次数
     *
     * @param countOdue 入催次数
     */
    public void setCountOdue(Integer countOdue) {
        this.countOdue = countOdue;
    }

    /**
     * 获取 入催时间
     *
     * @return dteIntoCollection 入催时间
     */
    public LocalDate getDteIntoCollection() {
        return this.dteIntoCollection;
    }

    /**
     * 设置 入催时间
     *
     * @param dteIntoCollection 入催时间
     */
    public void setDteIntoCollection(LocalDate dteIntoCollection) {
        this.dteIntoCollection = dteIntoCollection;
    }

    /**
     * 获取 出催时间
     *
     * @return dteOutCollection 出催时间
     */
    public LocalDate getDteOutCollection() {
        return this.dteOutCollection;
    }

    /**
     * 设置 出催时间
     *
     * @param dteOutCollection 出催时间
     */
    public void setDteOutCollection(LocalDate dteOutCollection) {
        this.dteOutCollection = dteOutCollection;
    }

    /**
     * 获取 首次入催时间
     *
     * @return firstDate 首次入催时间
     */
    public LocalDate getFirstDate() {
        return this.firstDate;
    }

    /**
     * 设置 首次入催时间
     *
     * @param firstDate 首次入催时间
     */
    public void setFirstDate(LocalDate firstDate) {
        this.firstDate = firstDate;
    }

    /**
     * 获取 上案件号
     *
     * @return lastCaseCode 上案件号
     */
    public String getLastCaseCode() {
        return this.lastCaseCode;
    }

    /**
     * 设置 上案件号
     *
     * @param lastCaseCode 上案件号
     */
    public void setLastCaseCode(String lastCaseCode) {
        this.lastCaseCode = lastCaseCode;
    }

    /**
     * 获取 上次案件时间
     *
     * @return lastCaseDate 上次案件时间
     */
    public LocalDate getLastCaseDate() {
        return this.lastCaseDate;
    }

    /**
     * 设置 上次案件时间
     *
     * @param lastCaseDate 上次案件时间
     */
    public void setLastCaseDate(LocalDate lastCaseDate) {
        this.lastCaseDate = lastCaseDate;
    }

    /**
     * 获取 诉讼日期
     *
     * @return lawDate 诉讼日期
     */
    public LocalDate getLawDate() {
        return this.lawDate;
    }

    /**
     * 设置 诉讼日期
     *
     * @param lawDate 诉讼日期
     */
    public void setLawDate(LocalDate lawDate) {
        this.lawDate = lawDate;
    }

    /**
     * 获取 诉讼标识
     *
     * @return lawFlag 诉讼标识
     */
    public String getLawFlag() {
        return this.lawFlag;
    }

    /**
     * 设置 诉讼标识
     *
     * @param lawFlag 诉讼标识
     */
    public void setLawFlag(String lawFlag) {
        this.lawFlag = lawFlag;
    }

    /**
     * 获取 模块
     *
     * @return model 模块
     */
    public String getModel() {
        return this.model;
    }

    /**
     * 设置 模块
     *
     * @param model 模块
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * 获取 当前状态
     *
     * @return currState 当前状态
     */
    public String getCurrState() {
        return this.currState;
    }

    /**
     * 设置 当前状态
     *
     * @param currState 当前状态
     */
    public void setCurrState(String currState) {
        this.currState = currState;
    }

    /**
     * 获取 案件状态
     *
     * @return caseState 案件状态
     */
    public String getCaseState() {
        return this.caseState;
    }

    /**
     * 设置 案件状态
     *
     * @param caseState 案件状态
     */
    public void setCaseState(String caseState) {
        this.caseState = caseState;
    }

    /**
     * 获取 风险池编号
     *
     * @return teamCode 风险池编号
     */
    public String getTeamCode() {
        return this.teamCode;
    }

    /**
     * 设置 风险池编号
     *
     * @param teamCode 风险池编号
     */
    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    /**
     * 获取 最近行动时间
     *
     * @return lastOpeTime 最近行动时间
     */
    public LocalDate getLastOpeTime() {
        return this.lastOpeTime;
    }

    /**
     * 设置 最近行动时间
     *
     * @param lastOpeTime 最近行动时间
     */
    public void setLastOpeTime(LocalDate lastOpeTime) {
        this.lastOpeTime = lastOpeTime;
    }

    /**
     * 获取 入组日期
     *
     * @return inTeamDate 入组日期
     */
    public LocalDate getInTeamDate() {
        return this.inTeamDate;
    }

    /**
     * 设置 入组日期
     *
     * @param inTeamDate 入组日期
     */
    public void setInTeamDate(LocalDate inTeamDate) {
        this.inTeamDate = inTeamDate;
    }

    /**
     * 获取 风险池原因码
     *
     * @return reasonCode 风险池原因码
     */
    public String getReasonCode() {
        return this.reasonCode;
    }

    /**
     * 设置 风险池原因码
     *
     * @param reasonCode 风险池原因码
     */
    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    /**
     * 获取 失联决策代码
     *
     * @return noneContactRuleCode 失联决策代码
     */
    public String getNoneContactRuleCode() {
        return this.noneContactRuleCode;
    }

    /**
     * 设置 失联决策代码
     *
     * @param noneContactRuleCode 失联决策代码
     */
    public void setNoneContactRuleCode(String noneContactRuleCode) {
        this.noneContactRuleCode = noneContactRuleCode;
    }

    /**
     * 获取 联系情况
     *
     * @return contactCode 联系情况
     */
    public String getContactCode() {
        return this.contactCode;
    }

    /**
     * 设置 联系情况
     *
     * @param contactCode 联系情况
     */
    public void setContactCode(String contactCode) {
        this.contactCode = contactCode;
    }

    /**
     * 获取 人工联系次数
     *
     * @return contactTimes 人工联系次数
     */
    public String getContactTimes() {
        return this.contactTimes;
    }

    /**
     * 设置 人工联系次数
     *
     * @param contactTimes 人工联系次数
     */
    public void setContactTimes(String contactTimes) {
        this.contactTimes = contactTimes;
    }

    /**
     * 获取 结果完成时间
     *
     * @return resultTime 结果完成时间
     */
    public String getResultTime() {
        return this.resultTime;
    }

    /**
     * 设置 结果完成时间
     *
     * @param resultTime 结果完成时间
     */
    public void setResultTime(String resultTime) {
        this.resultTime = resultTime;
    }

    /**
     * 获取 呼入呼出
     *
     * @return resultDirection 呼入呼出
     */
    public String getResultDirection() {
        return this.resultDirection;
    }

    /**
     * 设置 呼入呼出
     *
     * @param resultDirection 呼入呼出
     */
    public void setResultDirection(String resultDirection) {
        this.resultDirection = resultDirection;
    }

    /**
     * 获取 尝试联系这个帐户的次数
     *
     * @return attemptCount 尝试联系这个帐户的次数
     */
    public String getAttemptCount() {
        return this.attemptCount;
    }

    /**
     * 设置 尝试联系这个帐户的次数
     *
     * @param attemptCount 尝试联系这个帐户的次数
     */
    public void setAttemptCount(String attemptCount) {
        this.attemptCount = attemptCount;
    }

    /**
     * 获取 有效催收总数
     *
     * @return callTotal 有效催收总数
     */
    public BigDecimal getCallTotal() {
        return this.callTotal;
    }

    /**
     * 设置 有效催收总数
     *
     * @param callTotal 有效催收总数
     */
    public void setCallTotal(BigDecimal callTotal) {
        this.callTotal = callTotal;
    }

    /**
     * 获取 上一模板
     *
     * @return lastModel 上一模板
     */
    public String getLastModel() {
        return this.lastModel;
    }

    /**
     * 设置 上一模板
     *
     * @param lastModel 上一模板
     */
    public void setLastModel(String lastModel) {
        this.lastModel = lastModel;
    }

    /**
     * 获取 英文名
     *
     * @return ename 英文名
     */
    public String getEname() {
        return this.ename;
    }

    /**
     * 设置 英文名
     *
     * @param ename 英文名
     */
    public void setEname(String ename) {
        this.ename = ename;
    }

    /**
     * 获取 性别
     *
     * @return eusex 性别
     */
    public String getEusex() {
        return this.eusex;
    }

    /**
     * 设置 性别
     *
     * @param eusex 性别
     */
    public void setEusex(String eusex) {
        this.eusex = eusex;
    }

    /**
     * 获取 账单类型
     *
     * @return statementTypeAll 账单类型
     */
    public String getStatementTypeAll() {
        return this.statementTypeAll;
    }

    /**
     * 设置 账单类型
     *
     * @param statementTypeAll 账单类型
     */
    public void setStatementTypeAll(String statementTypeAll) {
        this.statementTypeAll = statementTypeAll;
    }

    /**
     * 获取 账单日
     *
     * @return billingcycle 账单日
     */
    public Integer getBillingcycle() {
        return this.billingcycle;
    }

    /**
     * 设置 账单日
     *
     * @param billingcycle 账单日
     */
    public void setBillingcycle(Integer billingcycle) {
        this.billingcycle = billingcycle;
    }

    /**
     * 获取 不良标识域
     *
     * @return badnessCode 不良标识域
     */
    public String getBadnessCode() {
        return this.badnessCode;
    }

    /**
     * 设置 不良标识域
     *
     * @param badnessCode 不良标识域
     */
    public void setBadnessCode(String badnessCode) {
        this.badnessCode = badnessCode;
    }

    /**
     * 获取 总欠款
     *
     * @return classIBalance 总欠款
     */
    public BigDecimal getClassIBalance() {
        return this.classIBalance;
    }

    /**
     * 设置 总欠款
     *
     * @param classIBalance 总欠款
     */
    public void setClassIBalance(BigDecimal classIBalance) {
        this.classIBalance = classIBalance;
    }

    /**
     * 获取 风险等级
     *
     * @return riskrank 风险等级
     */
    public String getRiskrank() {
        return this.riskrank;
    }

    /**
     * 设置 风险等级
     *
     * @param riskrank 风险等级
     */
    public void setRiskrank(String riskrank) {
        this.riskrank = riskrank;
    }

    /**
     * 获取 延滞天数
     *
     * @return delayDays 延滞天数
     */
    public Integer getDelayDays() {
        return this.delayDays;
    }

    /**
     * 设置 延滞天数
     *
     * @param delayDays 延滞天数
     */
    public void setDelayDays(Integer delayDays) {
        this.delayDays = delayDays;
    }

    /**
     * 获取 活跃分期标识
     *
     * @return activeInstallmentFlag 活跃分期标识
     */
    public String getActiveInstallmentFlag() {
        return this.activeInstallmentFlag;
    }

    /**
     * 设置 活跃分期标识
     *
     * @param activeInstallmentFlag 活跃分期标识
     */
    public void setActiveInstallmentFlag(String activeInstallmentFlag) {
        this.activeInstallmentFlag = activeInstallmentFlag;
    }

    /**
     * 获取 大额账户标识
     *
     * @return productType 大额账户标识
     */
    public String getProductType() {
        return this.productType;
    }

    /**
     * 设置 大额账户标识
     *
     * @param productType 大额账户标识
     */
    public void setProductType(String productType) {
        this.productType = productType;
    }

    /**
     * 获取 24 期状况
     *
     * @return status24 24 期状况
     */
    public String getStatus24() {
        return this.status24;
    }

    /**
     * 设置 24 期状况
     *
     * @param status24 24 期状况
     */
    public void setStatus24(String status24) {
        this.status24 = status24;
    }

    /**
     * 获取 委外标志
     *
     * @return wcsOutcode 委外标志
     */
    public String getWcsOutcode() {
        return this.wcsOutcode;
    }

    /**
     * 设置 委外标志
     *
     * @param wcsOutcode 委外标志
     */
    public void setWcsOutcode(String wcsOutcode) {
        this.wcsOutcode = wcsOutcode;
    }

    /**
     * 获取 手别
     *
     * @return handType 手别
     */
    public String getHandType() {
        return this.handType;
    }

    /**
     * 设置 手别
     *
     * @param handType 手别
     */
    public void setHandType(String handType) {
        this.handType = handType;
    }

    /**
     * 获取 客户标签
     *
     * @return custTags 客户标签
     */
    public String getCustTags() {
        return this.custTags;
    }

    /**
     * 设置 客户标签
     *
     * @param custTags 客户标签
     */
    public void setCustTags(String custTags) {
        this.custTags = custTags;
    }

    /**
     * 获取 分行号
     *
     * @return bankNbr 分行号
     */
    public String getBankNbr() {
        return this.bankNbr;
    }

    /**
     * 设置 分行号
     *
     * @param bankNbr 分行号
     */
    public void setBankNbr(String bankNbr) {
        this.bankNbr = bankNbr;
    }

    /**
     * 获取 支行号
     *
     * @return branchNbr 支行号
     */
    public String getBranchNbr() {
        return this.branchNbr;
    }

    /**
     * 设置 支行号
     *
     * @param branchNbr 支行号
     */
    public void setBranchNbr(String branchNbr) {
        this.branchNbr = branchNbr;
    }

    /**
     * 获取 已重审不能开通
     *
     * @return restateNotOpen 已重审不能开通
     */
    public String getRestateNotOpen() {
        return this.restateNotOpen;
    }

    /**
     * 设置 已重审不能开通
     *
     * @param restateNotOpen 已重审不能开通
     */
    public void setRestateNotOpen(String restateNotOpen) {
        this.restateNotOpen = restateNotOpen;
    }

    /**
     * 获取 永久信用额
     *
     * @return crlimitPerm 永久信用额
     */
    public Integer getCrlimitPerm() {
        return this.crlimitPerm;
    }

    /**
     * 设置 永久信用额
     *
     * @param crlimitPerm 永久信用额
     */
    public void setCrlimitPerm(Integer crlimitPerm) {
        this.crlimitPerm = crlimitPerm;
    }

    /**
     * 获取 临时额度
     *
     * @return crlimitTemp 临时额度
     */
    public Integer getCrlimitTemp() {
        return this.crlimitTemp;
    }

    /**
     * 设置 临时额度
     *
     * @param crlimitTemp 临时额度
     */
    public void setCrlimitTemp(Integer crlimitTemp) {
        this.crlimitTemp = crlimitTemp;
    }

    /**
     * 获取 备忘事项
     *
     * @return note 备忘事项
     */
    public String getNote() {
        return this.note;
    }

    /**
     * 设置 备忘事项
     *
     * @param note 备忘事项
     */
    public void setNote(String note) {
        this.note = note;
    }

    /**
     * 获取 催收备注
     *
     * @return custWcsMemo 催收备注
     */
    public String getCustWcsMemo() {
        return this.custWcsMemo;
    }

    /**
     * 设置 催收备注
     *
     * @param custWcsMemo 催收备注
     */
    public void setCustWcsMemo(String custWcsMemo) {
        this.custWcsMemo = custWcsMemo;
    }

    /**
     * 获取 VIP 标识域
     *
     * @return vipcode VIP 标识域
     */
    public String getVipcode() {
        return this.vipcode;
    }

    /**
     * 设置 VIP 标识域
     *
     * @param vipcode VIP 标识域
     */
    public void setVipcode(String vipcode) {
        this.vipcode = vipcode;
    }

    /**
     * 获取 证件有效期
     *
     * @return permIdExp 证件有效期
     */
    public String getPermIdExp() {
        return this.permIdExp;
    }

    /**
     * 设置 证件有效期
     *
     * @param permIdExp 证件有效期
     */
    public void setPermIdExp(String permIdExp) {
        this.permIdExp = permIdExp;
    }

    /**
     * 获取 出生日期
     *
     * @return bornDate 出生日期
     */
    public LocalDate getBornDate() {
        return this.bornDate;
    }

    /**
     * 设置 出生日期
     *
     * @param bornDate 出生日期
     */
    public void setBornDate(LocalDate bornDate) {
        this.bornDate = bornDate;
    }

    /**
     * 获取 居住类型
     *
     * @return euTypeOfRes 居住类型
     */
    public String getEuTypeOfRes() {
        return this.euTypeOfRes;
    }

    /**
     * 设置 居住类型
     *
     * @param euTypeOfRes 居住类型
     */
    public void setEuTypeOfRes(String euTypeOfRes) {
        this.euTypeOfRes = euTypeOfRes;
    }

    /**
     * 获取 居住年限
     *
     * @return euPerOfRes 居住年限
     */
    public String getEuPerOfRes() {
        return this.euPerOfRes;
    }

    /**
     * 设置 居住年限
     *
     * @param euPerOfRes 居住年限
     */
    public void setEuPerOfRes(String euPerOfRes) {
        this.euPerOfRes = euPerOfRes;
    }

    /**
     * 获取 职业
     *
     * @return job 职业
     */
    public String getJob() {
        return this.job;
    }

    /**
     * 设置 职业
     *
     * @param job 职业
     */
    public void setJob(String job) {
        this.job = job;
    }

    /**
     * 获取 单位名称
     *
     * @return employer 单位名称
     */
    public String getEmployer() {
        return this.employer;
    }

    /**
     * 设置 单位名称
     *
     * @param employer 单位名称
     */
    public void setEmployer(String employer) {
        this.employer = employer;
    }

    /**
     * 获取 年薪
     *
     * @return income 年薪
     */
    public Integer getIncome() {
        return this.income;
    }

    /**
     * 设置 年薪
     *
     * @param income 年薪
     */
    public void setIncome(Integer income) {
        this.income = income;
    }

    /**
     * 获取 AB 客户群
     *
     * @return abClass AB 客户群
     */
    public String getAbClass() {
        return this.abClass;
    }

    /**
     * 设置 AB 客户群
     *
     * @param abClass AB 客户群
     */
    public void setAbClass(String abClass) {
        this.abClass = abClass;
    }

    /**
     * 获取 封锁码
     *
     * @return blockcode 封锁码
     */
    public String getBlockcode() {
        return this.blockcode;
    }

    /**
     * 设置 封锁码
     *
     * @param blockcode 封锁码
     */
    public void setBlockcode(String blockcode) {
        this.blockcode = blockcode;
    }

    /**
     * 获取 封锁码日期
     *
     * @return blockDate 封锁码日期
     */
    public String getBlockDate() {
        return this.blockDate;
    }

    /**
     * 设置 封锁码日期
     *
     * @param blockDate 封锁码日期
     */
    public void setBlockDate(String blockDate) {
        this.blockDate = blockDate;
    }

    /**
     * 获取 旧封锁码
     *
     * @return prvBlockcode 旧封锁码
     */
    public String getPrvBlockcode() {
        return this.prvBlockcode;
    }

    /**
     * 设置 旧封锁码
     *
     * @param prvBlockcode 旧封锁码
     */
    public void setPrvBlockcode(String prvBlockcode) {
        this.prvBlockcode = prvBlockcode;
    }

    /**
     * 获取 旧封锁码日期
     *
     * @return prvBlockDate 旧封锁码日期
     */
    public String getPrvBlockDate() {
        return this.prvBlockDate;
    }

    /**
     * 设置 旧封锁码日期
     *
     * @param prvBlockDate 旧封锁码日期
     */
    public void setPrvBlockDate(String prvBlockDate) {
        this.prvBlockDate = prvBlockDate;
    }

    /**
     * 获取 手机号
     *
     * @return telNum 手机号
     */
    public String getTelNum() {
        return this.telNum;
    }

    /**
     * 设置 手机号
     *
     * @param telNum 手机号
     */
    public void setTelNum(String telNum) {
        this.telNum = telNum;
    }

    /**
     * 获取 电子邮箱
     *
     * @return email 电子邮箱
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * 设置 电子邮箱
     *
     * @param email 电子邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 获取 毕业学校名称
     *
     * @return school 毕业学校名称
     */
    public String getSchool() {
        return this.school;
    }

    /**
     * 设置 毕业学校名称
     *
     * @param school 毕业学校名称
     */
    public void setSchool(String school) {
        this.school = school;
    }

    /**
     * 获取 学历
     *
     * @return prexCustQualification 学历
     */
    public String getPrexCustQualification() {
        return this.prexCustQualification;
    }

    /**
     * 设置 学历
     *
     * @param prexCustQualification 学历
     */
    public void setPrexCustQualification(String prexCustQualification) {
        this.prexCustQualification = prexCustQualification;
    }

    /**
     * 获取 专业名称
     *
     * @return major 专业名称
     */
    public String getMajor() {
        return this.major;
    }

    /**
     * 设置 专业名称
     *
     * @param major 专业名称
     */
    public void setMajor(String major) {
        this.major = major;
    }

    /**
     * 获取 婚姻状态
     *
     * @return euMaritalStatus 婚姻状态
     */
    public String getEuMaritalStatus() {
        return this.euMaritalStatus;
    }

    /**
     * 设置 婚姻状态
     *
     * @param euMaritalStatus 婚姻状态
     */
    public void setEuMaritalStatus(String euMaritalStatus) {
        this.euMaritalStatus = euMaritalStatus;
    }

    /**
     * 获取 持卡人类型
     *
     * @return custType 持卡人类型
     */
    public String getCustType() {
        return this.custType;
    }

    /**
     * 设置 持卡人类型
     *
     * @param custType 持卡人类型
     */
    public void setCustType(String custType) {
        this.custType = custType;
    }

    /**
     * 获取 持卡人状态
     *
     * @return custStatus 持卡人状态
     */
    public String getCustStatus() {
        return this.custStatus;
    }

    /**
     * 设置 持卡人状态
     *
     * @param custStatus 持卡人状态
     */
    public void setCustStatus(String custStatus) {
        this.custStatus = custStatus;
    }

    /**
     * 获取 持卡人状态改变日期
     *
     * @return custStatusDate 持卡人状态改变日期
     */
    public String getCustStatusDate() {
        return this.custStatusDate;
    }

    /**
     * 设置 持卡人状态改变日期
     *
     * @param custStatusDate 持卡人状态改变日期
     */
    public void setCustStatusDate(String custStatusDate) {
        this.custStatusDate = custStatusDate;
    }

    /**
     * 获取 担保人姓名
     *
     * @return custGName 担保人姓名
     */
    public String getCustGName() {
        return this.custGName;
    }

    /**
     * 设置 担保人姓名
     *
     * @param custGName 担保人姓名
     */
    public void setCustGName(String custGName) {
        this.custGName = custGName;
    }

    /**
     * 获取 担保人电话
     *
     * @return custGPhone 担保人电话
     */
    public String getCustGPhone() {
        return this.custGPhone;
    }

    /**
     * 设置 担保人电话
     *
     * @param custGPhone 担保人电话
     */
    public void setCustGPhone(String custGPhone) {
        this.custGPhone = custGPhone;
    }

    /**
     * 获取 保证金
     *
     * @return depositArea 保证金
     */
    public BigDecimal getDepositArea() {
        return this.depositArea;
    }

    /**
     * 设置 保证金
     *
     * @param depositArea 保证金
     */
    public void setDepositArea(BigDecimal depositArea) {
        this.depositArea = depositArea;
    }

    /**
     * 获取 创建（开户）日期
     *
     * @return custOpenDate 创建（开户）日期
     */
    public LocalDate getCustOpenDate() {
        return this.custOpenDate;
    }

    /**
     * 设置 创建（开户）日期
     *
     * @param custOpenDate 创建（开户）日期
     */
    public void setCustOpenDate(LocalDate custOpenDate) {
        this.custOpenDate = custOpenDate;
    }

    /**
     * 获取 家庭电话
     *
     * @return custPhone 家庭电话
     */
    public String getCustPhone() {
        return this.custPhone;
    }

    /**
     * 设置 家庭电话
     *
     * @param custPhone 家庭电话
     */
    public void setCustPhone(String custPhone) {
        this.custPhone = custPhone;
    }

    /**
     * 获取 备用联系电话
     *
     * @return custBackPhone 备用联系电话
     */
    public String getCustBackPhone() {
        return this.custBackPhone;
    }

    /**
     * 设置 备用联系电话
     *
     * @param custBackPhone 备用联系电话
     */
    public void setCustBackPhone(String custBackPhone) {
        this.custBackPhone = custBackPhone;
    }

    /**
     * 获取 单位电话 1
     *
     * @return custEmpTel1 单位电话 1
     */
    public String getCustEmpTel1() {
        return this.custEmpTel1;
    }

    /**
     * 设置 单位电话 1
     *
     * @param custEmpTel1 单位电话 1
     */
    public void setCustEmpTel1(String custEmpTel1) {
        this.custEmpTel1 = custEmpTel1;
    }

    /**
     * 获取 单位电话 2
     *
     * @return custEmpTel2 单位电话 2
     */
    public String getCustEmpTel2() {
        return this.custEmpTel2;
    }

    /**
     * 设置 单位电话 2
     *
     * @param custEmpTel2 单位电话 2
     */
    public void setCustEmpTel2(String custEmpTel2) {
        this.custEmpTel2 = custEmpTel2;
    }

    /**
     * 获取 单位电话 3
     *
     * @return custEmpTel3 单位电话 3
     */
    public String getCustEmpTel3() {
        return this.custEmpTel3;
    }

    /**
     * 设置 单位电话 3
     *
     * @param custEmpTel3 单位电话 3
     */
    public void setCustEmpTel3(String custEmpTel3) {
        this.custEmpTel3 = custEmpTel3;
    }

    /**
     * 获取 紧急联系人 1 关系
     *
     * @return custGlRln1 紧急联系人 1 关系
     */
    public String getCustGlRln1() {
        return this.custGlRln1;
    }

    /**
     * 设置 紧急联系人 1 关系
     *
     * @param custGlRln1 紧急联系人 1 关系
     */
    public void setCustGlRln1(String custGlRln1) {
        this.custGlRln1 = custGlRln1;
    }

    /**
     * 获取 紧急联系人 1 姓名
     *
     * @return custGlNam1 紧急联系人 1 姓名
     */
    public String getCustGlNam1() {
        return this.custGlNam1;
    }

    /**
     * 设置 紧急联系人 1 姓名
     *
     * @param custGlNam1 紧急联系人 1 姓名
     */
    public void setCustGlNam1(String custGlNam1) {
        this.custGlNam1 = custGlNam1;
    }

    /**
     * 获取 紧急联系人 1 电话
     *
     * @return custGlTel1 紧急联系人 1 电话
     */
    public String getCustGlTel1() {
        return this.custGlTel1;
    }

    /**
     * 设置 紧急联系人 1 电话
     *
     * @param custGlTel1 紧急联系人 1 电话
     */
    public void setCustGlTel1(String custGlTel1) {
        this.custGlTel1 = custGlTel1;
    }

    /**
     * 获取 紧急联系人 2 关系
     *
     * @return custGlRln2 紧急联系人 2 关系
     */
    public String getCustGlRln2() {
        return this.custGlRln2;
    }

    /**
     * 设置 紧急联系人 2 关系
     *
     * @param custGlRln2 紧急联系人 2 关系
     */
    public void setCustGlRln2(String custGlRln2) {
        this.custGlRln2 = custGlRln2;
    }

    /**
     * 获取 紧急联系人 2 姓名
     *
     * @return custGlNam2 紧急联系人 2 姓名
     */
    public String getCustGlNam2() {
        return this.custGlNam2;
    }

    /**
     * 设置 紧急联系人 2 姓名
     *
     * @param custGlNam2 紧急联系人 2 姓名
     */
    public void setCustGlNam2(String custGlNam2) {
        this.custGlNam2 = custGlNam2;
    }

    /**
     * 获取 紧急联系人 2 电话
     *
     * @return custGlTel2 紧急联系人 2 电话
     */
    public String getCustGlTel2() {
        return this.custGlTel2;
    }

    /**
     * 设置 紧急联系人 2 电话
     *
     * @param custGlTel2 紧急联系人 2 电话
     */
    public void setCustGlTel2(String custGlTel2) {
        this.custGlTel2 = custGlTel2;
    }

    /**
     * 获取 户籍地址
     *
     * @return custHomeCityAddr 户籍地址
     */
    public String getCustHomeCityAddr() {
        return this.custHomeCityAddr;
    }

    /**
     * 设置 户籍地址
     *
     * @param custHomeCityAddr 户籍地址
     */
    public void setCustHomeCityAddr(String custHomeCityAddr) {
        this.custHomeCityAddr = custHomeCityAddr;
    }

    /**
     * 获取 家庭地址
     *
     * @return custAddr 家庭地址
     */
    public String getCustAddr() {
        return this.custAddr;
    }

    /**
     * 设置 家庭地址
     *
     * @param custAddr 家庭地址
     */
    public void setCustAddr(String custAddr) {
        this.custAddr = custAddr;
    }

    /**
     * 获取 单位地址
     *
     * @return custGEmpA 单位地址
     */
    public String getCustGEmpA() {
        return this.custGEmpA;
    }

    /**
     * 设置 单位地址
     *
     * @param custGEmpA 单位地址
     */
    public void setCustGEmpA(String custGEmpA) {
        this.custGEmpA = custGEmpA;
    }

    /**
     * 获取 现住地
     *
     * @return custmAddr 现住地
     */
    public String getCustmAddr() {
        return this.custmAddr;
    }

    /**
     * 设置 现住地
     *
     * @param custmAddr 现住地
     */
    public void setCustmAddr(String custmAddr) {
        this.custmAddr = custmAddr;
    }

    /**
     * 获取 账单地址
     *
     * @return billAddr 账单地址
     */
    public String getBillAddr() {
        return this.billAddr;
    }

    /**
     * 设置 账单地址
     *
     * @param billAddr 账单地址
     */
    public void setBillAddr(String billAddr) {
        this.billAddr = billAddr;
    }

    /**
     * 获取 更新人
     *
     * @return updateUser 更新人
     */
    public String getUpdateUser() {
        return this.updateUser;
    }

    /**
     * 设置 更新人
     *
     * @param updateUser 更新人
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取 更新时间
     *
     * @return updateTime 更新时间
     */
    public LocalDateTime getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置 更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}