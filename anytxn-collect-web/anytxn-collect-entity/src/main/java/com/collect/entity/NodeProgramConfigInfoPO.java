package com.collect.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 节点程序配置信息实体类
 * 对应数据库中的node_program_config_info表
 */
public class NodeProgramConfigInfoPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 节点ID
     */
    private String nodeCode;

    /**
     * 节点名称
     */
    private String nodeCallName;

    /**
     * 程序ID
     */
    private String nodeAttriId;

    /**
     * 节点程序优先级
     */
    private Integer nodePriority;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间戳
     */
    private LocalDateTime createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeCallName() {
        return nodeCallName;
    }

    public void setNodeCallName(String nodeCallName) {
        this.nodeCallName = nodeCallName;
    }

    public String getNodeAttriId() {
        return nodeAttriId;
    }

    public void setNodeAttriId(String nodeAttriId) {
        this.nodeAttriId = nodeAttriId;
    }

    public Integer getNodePriority() {
        return nodePriority;
    }

    public void setNodePriority(Integer nodePriority) {
        this.nodePriority = nodePriority;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
} 