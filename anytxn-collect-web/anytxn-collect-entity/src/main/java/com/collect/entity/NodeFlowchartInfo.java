package com.collect.entity;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 节点流程图信息实体类
 * 对应数据库中的node_flowchart_info表
 */
public class NodeFlowchartInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 流程图ID
     */
    private String flowchartId;
    
    /**
     * 流程图名称
     */
    private String flowchartName;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 流程图文本
     */
    private String flowchartText;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 更新用户
     */
    private String updateUser;
    
    /**
     * 创建时间
     */
    private Timestamp createTime;
    
    /**
     * 更新时间
     */
    private Timestamp updateTime;
    
    /**
     * 版本
     */
    private Integer version;

    /**
     * 获取 主键ID
     *
     * @return id 主键ID
     */
    public Long getId() {
        return this.id;
    }

    /**
     * 设置 主键ID
     *
     * @param id 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取 流程图ID
     *
     * @return flowchartId 流程图ID
     */
    public String getFlowchartId() {
        return this.flowchartId;
    }

    /**
     * 设置 流程图ID
     *
     * @param flowchartId 流程图ID
     */
    public void setFlowchartId(String flowchartId) {
        this.flowchartId = flowchartId;
    }

    /**
     * 获取 流程图名称
     *
     * @return flowchartName 流程图名称
     */
    public String getFlowchartName() {
        return this.flowchartName;
    }

    /**
     * 设置 流程图名称
     *
     * @param flowchartName 流程图名称
     */
    public void setFlowchartName(String flowchartName) {
        this.flowchartName = flowchartName;
    }

    /**
     * 获取 描述
     *
     * @return description 描述
     */
    public String getDescription() {
        return this.description;
    }

    /**
     * 设置 描述
     *
     * @param description 描述
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 获取 流程图文本
     *
     * @return flowchartText 流程图文本
     */
    public String getFlowchartText() {
        return this.flowchartText;
    }

    /**
     * 设置 流程图文本
     *
     * @param flowchartText 流程图文本
     */
    public void setFlowchartText(String flowchartText) {
        this.flowchartText = flowchartText;
    }

    /**
     * 获取 创建用户
     *
     * @return createUser 创建用户
     */
    public String getCreateUser() {
        return this.createUser;
    }

    /**
     * 设置 创建用户
     *
     * @param createUser 创建用户
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取 更新用户
     *
     * @return updateUser 更新用户
     */
    public String getUpdateUser() {
        return this.updateUser;
    }

    /**
     * 设置 更新用户
     *
     * @param updateUser 更新用户
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取 创建时间
     *
     * @return createTime 创建时间
     */
    public Timestamp getCreateTime() {
        return this.createTime;
    }

    /**
     * 设置 创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取 更新时间
     *
     * @return updateTime 更新时间
     */
    public Timestamp getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置 更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取 版本
     *
     * @return version 版本
     */
    public Integer getVersion() {
        return this.version;
    }

    /**
     * 设置 版本
     *
     * @param version 版本
     */
    public void setVersion(Integer version) {
        this.version = version;
    }
} 