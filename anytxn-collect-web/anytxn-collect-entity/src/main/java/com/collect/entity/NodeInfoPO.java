package com.collect.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 节点信息实体类
 * 对应数据库中的node_info表
 */
public class NodeInfoPO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 节点代码
     */
    private String nodeCode;

    /**
     * 节点调用名称
     */
    private String nodeCallName;

    /**
     * 节点功能描述
     */
    private String nodeFuncDesc;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 节点状态
     */
    private String nodeStatus;

    /**
     * 节点权限
     */
    private String nodeAuth;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 审批人
     */
    private String aduitUser;

    /**
     * 审批时间
     */
    private LocalDateTime aduitTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private String dispatchManageList;

    // Getters and Setters

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeCallName() {
        return nodeCallName;
    }

    public void setNodeCallName(String nodeCallName) {
        this.nodeCallName = nodeCallName;
    }

    public String getNodeFuncDesc() {
        return nodeFuncDesc;
    }

    public void setNodeFuncDesc(String nodeFuncDesc) {
        this.nodeFuncDesc = nodeFuncDesc;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getNodeStatus() {
        return nodeStatus;
    }

    public void setNodeStatus(String nodeStatus) {
        this.nodeStatus = nodeStatus;
    }

    public String getNodeAuth() {
        return nodeAuth;
    }

    public void setNodeAuth(String nodeAuth) {
        this.nodeAuth = nodeAuth;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getAduitUser() {
        return aduitUser;
    }

    public void setAduitUser(String aduitUser) {
        this.aduitUser = aduitUser;
    }

    public LocalDateTime getAduitTime() {
        return aduitTime;
    }

    public void setAduitTime(LocalDateTime aduitTime) {
        this.aduitTime = aduitTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getDispatchManageList() {
        return dispatchManageList;
    }

    public void setDispatchManageList(String dispatchManageList) {
        this.dispatchManageList = dispatchManageList;
    }
}
