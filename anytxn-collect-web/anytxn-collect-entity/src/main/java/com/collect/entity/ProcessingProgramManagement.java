package com.collect.entity;

import java.util.Date;

/**
 * 处理程序管理实体类
 */
public class ProcessingProgramManagement {

    /**
     * 节点属性ID，主键
     */
    private String nodeAttriId;

    /**
     * 节点属性名称
     */
    private String nodeAttriName;

    /**
     * 节点属性描述
     */
    private String nodeAttriDesc;

    /**
     * 节点属性类型
     */
    private String nodeAttriType;

    /**
     * 节点属性代码
     */
    private String nodeAttriCode;

    /**
     * 节点属性排序
     */
    private Integer nodeAttriUp;

    /**
     * 状态
     */
    private String status;

    /**
     * 更新用户
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    // Getter和Setter方法

    public String getNodeAttriId() {
        return nodeAttriId;
    }

    public void setNodeAttriId(String nodeAttriId) {
        this.nodeAttriId = nodeAttriId;
    }

    public String getNodeAttriName() {
        return nodeAttriName;
    }

    public void setNodeAttriName(String nodeAttriName) {
        this.nodeAttriName = nodeAttriName;
    }

    public String getNodeAttriDesc() {
        return nodeAttriDesc;
    }

    public void setNodeAttriDesc(String nodeAttriDesc) {
        this.nodeAttriDesc = nodeAttriDesc;
    }

    public String getNodeAttriType() {
        return nodeAttriType;
    }

    public void setNodeAttriType(String nodeAttriType) {
        this.nodeAttriType = nodeAttriType;
    }

    public String getNodeAttriCode() {
        return nodeAttriCode;
    }

    public void setNodeAttriCode(String nodeAttriCode) {
        this.nodeAttriCode = nodeAttriCode;
    }

    public Integer getNodeAttriUp() {
        return nodeAttriUp;
    }

    public void setNodeAttriUp(Integer nodeAttriUp) {
        this.nodeAttriUp = nodeAttriUp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "ProcessingProgramManagement{" +
                "nodeAttriId='" + nodeAttriId + '\'' +
                ", nodeAttriName='" + nodeAttriName + '\'' +
                ", nodeAttriDesc='" + nodeAttriDesc + '\'' +
                ", nodeAttriType='" + nodeAttriType + '\'' +
                ", nodeAttriCode='" + nodeAttriCode + '\'' +
                ", nodeAttriUp=" + nodeAttriUp +
                ", status='" + status + '\'' +
                ", updateUser='" + updateUser + '\'' +
                ", updateTime=" + updateTime +
                ", createTime=" + createTime +
                '}';
    }
} 