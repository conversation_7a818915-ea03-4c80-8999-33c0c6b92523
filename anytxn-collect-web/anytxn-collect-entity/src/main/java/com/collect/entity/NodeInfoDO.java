package com.collect.entity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 10:53
 **/
public class NodeInfoDO {

    private Integer id;

    /**
     * 节点代码
     */
    private String nodeCode;

    /**
     * 节点调用名称
     */
    private String nodeCallName;

    /**
     * 节点功能描述
     */
    private String nodeFuncDesc;

    /**
     * 节点类型
     */
    private String nodeType;

    /**
     * 节点状态
     */
    private String nodeStatus;

    /**
     * 节点权限
     */
    private String nodeAuth;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 审批人
     */
    private String aduitUser;

    /**
     * 审批时间
     */
    private LocalDateTime aduitTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private String dispatchManageList;

    private List<NodeProgramConfigInfoPO> nodeManageList;

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public String getNodeCallName() {
        return nodeCallName;
    }

    public void setNodeCallName(String nodeCallName) {
        this.nodeCallName = nodeCallName;
    }

    public String getNodeFuncDesc() {
        return nodeFuncDesc;
    }

    public void setNodeFuncDesc(String nodeFuncDesc) {
        this.nodeFuncDesc = nodeFuncDesc;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getNodeStatus() {
        return nodeStatus;
    }

    public void setNodeStatus(String nodeStatus) {
        this.nodeStatus = nodeStatus;
    }

    public String getNodeAuth() {
        return nodeAuth;
    }

    public void setNodeAuth(String nodeAuth) {
        this.nodeAuth = nodeAuth;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getAduitUser() {
        return aduitUser;
    }

    public void setAduitUser(String aduitUser) {
        this.aduitUser = aduitUser;
    }

    public LocalDateTime getAduitTime() {
        return aduitTime;
    }

    public void setAduitTime(LocalDateTime aduitTime) {
        this.aduitTime = aduitTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public List<NodeProgramConfigInfoPO> getNodeManageList() {
        return nodeManageList;
    }

    public void setNodeManageList(List<NodeProgramConfigInfoPO> nodeManageList) {
        this.nodeManageList = nodeManageList;
    }

    public String getDispatchManageList() {
        return dispatchManageList;
    }

    public void setDispatchManageList(String dispatchManageList) {
        this.dispatchManageList = dispatchManageList;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
