package com.collect.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 还款记录表
 */
public class WcsBasRepaymentPO {
    /**
     * ID
     */
    private Long id;

    /**
     * 案件编号
     */
    private String caseCode;

    /**
     * 卡号
     */
    private String cardNbr;

    /**
     * 交易代码
     */
    private String transCode;

    /**
     * 交易货币
     */
    private Integer txnCurrCode;

    /**
     * 交易金额
     */
    private BigDecimal txnCurrAmt;

    /**
     * 入账货币
     */
    private Integer postCurr;

    /**
     * 入账金额
     */
    private BigDecimal amount;

    /**
     * 交易日期
     */
    private String purchaseDte;

    /**
     * 入账日期
     */
    private String postingDte;

    /**
     * 注释/交易描述
     */
    private String dbaDescription;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCaseCode() {
        return caseCode;
    }

    public void setCaseCode(String caseCode) {
        this.caseCode = caseCode;
    }

    public String getCardNbr() {
        return cardNbr;
    }

    public void setCardNbr(String cardNbr) {
        this.cardNbr = cardNbr;
    }

    public String getTransCode() {
        return transCode;
    }

    public void setTransCode(String transCode) {
        this.transCode = transCode;
    }

    public Integer getTxnCurrCode() {
        return txnCurrCode;
    }

    public void setTxnCurrCode(Integer txnCurrCode) {
        this.txnCurrCode = txnCurrCode;
    }

    public BigDecimal getTxnCurrAmt() {
        return txnCurrAmt;
    }

    public void setTxnCurrAmt(BigDecimal txnCurrAmt) {
        this.txnCurrAmt = txnCurrAmt;
    }

    public Integer getPostCurr() {
        return postCurr;
    }

    public void setPostCurr(Integer postCurr) {
        this.postCurr = postCurr;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPurchaseDte() {
        return purchaseDte;
    }

    public void setPurchaseDte(String purchaseDte) {
        this.purchaseDte = purchaseDte;
    }

    public String getPostingDte() {
        return postingDte;
    }

    public void setPostingDte(String postingDte) {
        this.postingDte = postingDte;
    }

    public String getDbaDescription() {
        return dbaDescription;
    }

    public void setDbaDescription(String dbaDescription) {
        this.dbaDescription = dbaDescription;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
}
