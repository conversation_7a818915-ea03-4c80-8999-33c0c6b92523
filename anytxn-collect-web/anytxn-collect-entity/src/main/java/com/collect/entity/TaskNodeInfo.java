package com.collect.entity;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 任务节点信息实体类
 * 对应数据库中的task_node_info表
 */
public class TaskNodeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 案件编号
     */
    private String caseCode;
    
    /**
     * TASK_ID
     */
    private String taskId;
    
    /**
     * 节点ID
     */
    private String nodeCode;
    
    /**
     * 节点名称
     */
    private String flowchartName;
    
    /**
     * 节点执行状态
     */
    private String nodeExecStatus;
    
    /**
     * 节点执行次数
     */
    private Integer nodeExecCount;
    
    /**
     * 节点执行时长
     */
    private Long lastExecDuration;
    
    /**
     * 节点执行时间
     */
    private Timestamp lastExecTime;
    
    /**
     * 下一节点
     */
    private String nextNode;

    /**
     * 获取 案件编号
     *
     * @return caseCode 案件编号
     */
    public String getCaseCode() {
        return this.caseCode;
    }

    /**
     * 设置 案件编号
     *
     * @param caseCode 案件编号
     */
    public void setCaseCode(String caseCode) {
        this.caseCode = caseCode;
    }

    /**
     * 获取 TASK_ID
     *
     * @return taskId TASK_ID
     */
    public String getTaskId() {
        return this.taskId;
    }

    /**
     * 设置 TASK_ID
     *
     * @param taskId TASK_ID
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 获取 节点ID
     *
     * @return nodeCode 节点ID
     */
    public String getNodeCode() {
        return this.nodeCode;
    }

    /**
     * 设置 节点ID
     *
     * @param nodeCode 节点ID
     */
    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    /**
     * 获取 节点名称
     *
     * @return flowchartName 节点名称
     */
    public String getFlowchartName() {
        return this.flowchartName;
    }

    /**
     * 设置 节点名称
     *
     * @param flowchartName 节点名称
     */
    public void setFlowchartName(String flowchartName) {
        this.flowchartName = flowchartName;
    }

    /**
     * 获取 节点执行状态
     *
     * @return nodeExecStatus 节点执行状态
     */
    public String getNodeExecStatus() {
        return this.nodeExecStatus;
    }

    /**
     * 设置 节点执行状态
     *
     * @param nodeExecStatus 节点执行状态
     */
    public void setNodeExecStatus(String nodeExecStatus) {
        this.nodeExecStatus = nodeExecStatus;
    }

    /**
     * 获取 节点执行次数
     *
     * @return nodeExecCount 节点执行次数
     */
    public Integer getNodeExecCount() {
        return this.nodeExecCount;
    }

    /**
     * 设置 节点执行次数
     *
     * @param nodeExecCount 节点执行次数
     */
    public void setNodeExecCount(Integer nodeExecCount) {
        this.nodeExecCount = nodeExecCount;
    }

    /**
     * 获取 节点执行时长
     *
     * @return lastExecDuration 节点执行时长
     */
    public Long getLastExecDuration() {
        return this.lastExecDuration;
    }

    /**
     * 设置 节点执行时长
     *
     * @param lastExecDuration 节点执行时长
     */
    public void setLastExecDuration(Long lastExecDuration) {
        this.lastExecDuration = lastExecDuration;
    }

    /**
     * 获取 节点执行时间
     *
     * @return lastExecTime 节点执行时间
     */
    public Timestamp getLastExecTime() {
        return this.lastExecTime;
    }

    /**
     * 设置 节点执行时间
     *
     * @param lastExecTime 节点执行时间
     */
    public void setLastExecTime(Timestamp lastExecTime) {
        this.lastExecTime = lastExecTime;
    }

    /**
     * 获取 下一节点
     *
     * @return nextNode 下一节点
     */
    public String getNextNode() {
        return this.nextNode;
    }

    /**
     * 设置 下一节点
     *
     * @param nextNode 下一节点
     */
    public void setNextNode(String nextNode) {
        this.nextNode = nextNode;
    }
} 