package com.collect.entity;
import com.collect.constant.BasePO;

/**
 * 规则因子管理.
 *
 * <AUTHOR>
 * @date 2024 /12/17 10:33
 */
public class WebRuleVariablePO extends BasePO {

    /**
     * 变量名称
     */
    private String name;

    /**
     * 变量标识
     */
    private String key;

    /**
     * 规则类型
     */
    private String ruleType;

    /**
     * 规则执行类型
     */
    private Integer ruleExecuteType;

    /**
     * 值类型
     */
    private Integer valueType;

    /**
     * 小数精度位.
     */
    private Integer digits;

    /**
     * 因子值最小长度.
     */
    private Integer minLength;

    /**
     * 因子值最大长度.
     */
    private Integer maxLength;

    /**
     * Gets digits.
     *
     * @return the digits
     */
    public Integer getDigits() {
        return digits;
    }

    /**
     * Sets digits.
     *
     * @param digits the digits
     */
    public void setDigits(final Integer digits) {
        this.digits = digits;
    }

    /**
     * Gets min length.
     *
     * @return the min length
     */
    public Integer getMinLength() {
        return minLength;
    }

    /**
     * Sets min length.
     *
     * @param minLength the min length
     */
    public void setMinLength(final Integer minLength) {
        this.minLength = minLength;
    }

    /**
     * Gets max length.
     *
     * @return the max length
     */
    public Integer getMaxLength() {
        return maxLength;
    }

    /**
     * Sets max length.
     *
     * @param maxLength the max length
     */
    public void setMaxLength(final Integer maxLength) {
        this.maxLength = maxLength;
    }

    /**
     * Gets name.
     *
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * Sets name.
     *
     * @param name the name
     */
    public void setName(final String name) {
        this.name = name;
    }

    /**
     * Gets key.
     *
     * @return the key
     */
    public String getKey() {
        return key;
    }

    /**
     * Sets key.
     *
     * @param key the key
     */
    public void setKey(final String key) {
        this.key = key;
    }

    /**
     * Gets rule type.
     *
     * @return the rule type
     */
    public String getRuleType() {
        return ruleType;
    }

    /**
     * Sets rule type.
     *
     * @param ruleType the rule type
     */
    public void setRuleType(final String ruleType) {
        this.ruleType = ruleType;
    }

    /**
     * Gets rule execute type.
     *
     * @return the rule execute type
     */
    public Integer getRuleExecuteType() {
        return ruleExecuteType;
    }

    /**
     * Sets rule execute type.
     *
     * @param ruleExecuteType the rule execute type
     */
    public void setRuleExecuteType(final Integer ruleExecuteType) {
        this.ruleExecuteType = ruleExecuteType;
    }

    /**
     * Gets value type.
     *
     * @return the value type
     */
    public Integer getValueType() {
        return valueType;
    }

    /**
     * Sets value type.
     *
     * @param valueType the value type
     */
    public void setValueType(final Integer valueType) {
        this.valueType = valueType;
    }
}
