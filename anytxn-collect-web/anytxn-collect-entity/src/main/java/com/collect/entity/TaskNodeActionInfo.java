package com.collect.entity;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 任务节点动作信息实体类
 * 对应数据库中的task_node_action_info表
 */
public class TaskNodeActionInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 案件编号
     */
    private String caseCode;
    
    /**
     * TASK_ID
     */
    private String taskId;
    
    /**
     * 节点ID
     */
    private String nodeCode;
    
    /**
     * 程序ID
     */
    private String nodeAttriId;
    
    /**
     * 程序名称
     */
    private String nodeAttriName;
    
    /**
     * Action执行时间
     */
    private Timestamp actionTime;
    
    /**
     * Action执行结果
     */
    private String actionResult;

    /**
     * 获取 案件编号
     *
     * @return caseCode 案件编号
     */
    public String getCaseCode() {
        return this.caseCode;
    }

    /**
     * 设置 案件编号
     *
     * @param caseCode 案件编号
     */
    public void setCaseCode(String caseCode) {
        this.caseCode = caseCode;
    }

    /**
     * 获取 TASK_ID
     *
     * @return taskId TASK_ID
     */
    public String getTaskId() {
        return this.taskId;
    }

    /**
     * 设置 TASK_ID
     *
     * @param taskId TASK_ID
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /**
     * 获取 节点ID
     *
     * @return nodeCode 节点ID
     */
    public String getNodeCode() {
        return this.nodeCode;
    }

    /**
     * 设置 节点ID
     *
     * @param nodeCode 节点ID
     */
    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    /**
     * 获取 程序ID
     *
     * @return nodeAttriId 程序ID
     */
    public String getNodeAttriId() {
        return this.nodeAttriId;
    }

    /**
     * 设置 程序ID
     *
     * @param nodeAttriId 程序ID
     */
    public void setNodeAttriId(String nodeAttriId) {
        this.nodeAttriId = nodeAttriId;
    }

    /**
     * 获取 程序名称
     *
     * @return nodeAttriName 程序名称
     */
    public String getNodeAttriName() {
        return this.nodeAttriName;
    }

    /**
     * 设置 程序名称
     *
     * @param nodeAttriName 程序名称
     */
    public void setNodeAttriName(String nodeAttriName) {
        this.nodeAttriName = nodeAttriName;
    }

    /**
     * 获取 Action执行时间
     *
     * @return actionTime Action执行时间
     */
    public Timestamp getActionTime() {
        return this.actionTime;
    }

    /**
     * 设置 Action执行时间
     *
     * @param actionTime Action执行时间
     */
    public void setActionTime(Timestamp actionTime) {
        this.actionTime = actionTime;
    }

    /**
     * 获取 Action执行结果
     *
     * @return actionResult Action执行结果
     */
    public String getActionResult() {
        return this.actionResult;
    }

    /**
     * 设置 Action执行结果
     *
     * @param actionResult Action执行结果
     */
    public void setActionResult(String actionResult) {
        this.actionResult = actionResult;
    }
} 