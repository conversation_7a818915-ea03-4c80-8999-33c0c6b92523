package com.collect.entity;

import java.time.LocalDateTime;

public class BaseRuleInfoParamPO {
    private Long incId;
    private String ruleId;
    private String ruleName;
    private String ruleDesc;
    private String ruleCondField;
    private String ruleFacField;
    private String ruleResultField;
    private String ruleTypeSub;
    private String ruleType;
    private Integer rulePri;
    private String execType;
    private String ruleSts;
    private String ruleVerNo;
    private String crcdOrgNo;
    private String opExpsField;
    private String createUser;
    private String updateUser;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Integer version;
    private String paramSts;

    // Getters and Setters

    public Long getIncId() {
        return incId;
    }

    public void setIncId(Long incId) {
        this.incId = incId;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleDesc() {
        return ruleDesc;
    }

    public void setRuleDesc(String ruleDesc) {
        this.ruleDesc = ruleDesc;
    }

    public String getRuleCondField() {
        return ruleCondField;
    }

    public void setRuleCondField(String ruleCondField) {
        this.ruleCondField = ruleCondField;
    }

    public String getRuleFacField() {
        return ruleFacField;
    }

    public void setRuleFacField(String ruleFacField) {
        this.ruleFacField = ruleFacField;
    }

    public String getRuleResultField() {
        return ruleResultField;
    }

    public void setRuleResultField(String ruleResultField) {
        this.ruleResultField = ruleResultField;
    }

    public String getRuleTypeSub() {
        return ruleTypeSub;
    }

    public void setRuleTypeSub(String ruleTypeSub) {
        this.ruleTypeSub = ruleTypeSub;
    }

    public String getRuleType() {
        return ruleType;
    }

    public void setRuleType(String ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getRulePri() {
        return rulePri;
    }

    public void setRulePri(Integer rulePri) {
        this.rulePri = rulePri;
    }

    public String getExecType() {
        return execType;
    }

    public void setExecType(String execType) {
        this.execType = execType;
    }

    public String getRuleSts() {
        return ruleSts;
    }

    public void setRuleSts(String ruleSts) {
        this.ruleSts = ruleSts;
    }

    public String getRuleVerNo() {
        return ruleVerNo;
    }

    public void setRuleVerNo(String ruleVerNo) {
        this.ruleVerNo = ruleVerNo;
    }

    public String getCrcdOrgNo() {
        return crcdOrgNo;
    }

    public void setCrcdOrgNo(String crcdOrgNo) {
        this.crcdOrgNo = crcdOrgNo;
    }

    public String getOpExpsField() {
        return opExpsField;
    }

    public void setOpExpsField(String opExpsField) {
        this.opExpsField = opExpsField;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getParamSts() {
        return paramSts;
    }

    public void setParamSts(String paramSts) {
        this.paramSts = paramSts;
    }
}
