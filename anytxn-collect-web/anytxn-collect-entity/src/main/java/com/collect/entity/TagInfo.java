package com.collect.entity;


import java.sql.Timestamp;

/**
 * 标签信息实体类
 * 对应数据库中的tag_info表
 */

public class TagInfo {
    /**
     * 标签ID
     */
    private String tagId;
    
    /**
     * 标签名称
     */
    private String tagName;
    
    /**
     * 标签类型
     */
    private String tagType;
    
    /**
     * 标签属性
     */
    private String tagAttribute;
    
    /**
     * 标签描述
     */
    private String tagDescription;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 更新者
     */
    private String updateUser;
    
    /**
     * 更新时间
     */
    private Timestamp updateTime;
    
    /**
     * 创建时间
     */
    private Timestamp createTimestamp;

    /**
     * 标签来源
     */
    private String tagSource;

    /**
     * 节点程序
     */
    private String nodeAttriId;


    /**
     * 获取 标签ID
     *
     * @return tagId 标签ID
     */
    public String getTagId() {
        return this.tagId;
    }

    /**
     * 设置 标签ID
     *
     * @param tagId 标签ID
     */
    public void setTagId(String tagId) {
        this.tagId = tagId;
    }

    /**
     * 获取 标签名称
     *
     * @return tagName 标签名称
     */
    public String getTagName() {
        return this.tagName;
    }

    /**
     * 设置 标签名称
     *
     * @param tagName 标签名称
     */
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    /**
     * 获取 标签类型
     *
     * @return tagType 标签类型
     */
    public String getTagType() {
        return this.tagType;
    }

    /**
     * 设置 标签类型
     *
     * @param tagType 标签类型
     */
    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    /**
     * 获取 标签属性
     *
     * @return tagAttribute 标签属性
     */
    public String getTagAttribute() {
        return this.tagAttribute;
    }

    /**
     * 设置 标签属性
     *
     * @param tagAttribute 标签属性
     */
    public void setTagAttribute(String tagAttribute) {
        this.tagAttribute = tagAttribute;
    }

    /**
     * 获取 标签描述
     *
     * @return tagDescription 标签描述
     */
    public String getTagDescription() {
        return this.tagDescription;
    }

    /**
     * 设置 标签描述
     *
     * @param tagDescription 标签描述
     */
    public void setTagDescription(String tagDescription) {
        this.tagDescription = tagDescription;
    }

    /**
     * 获取 状态
     *
     * @return status 状态
     */
    public String getStatus() {
        return this.status;
    }

    /**
     * 设置 状态
     *
     * @param status 状态
     */
    public void setStatus(String status) {
        this.status = status;
    }



    /**
     * 获取 更新时间
     *
     * @return updateTime 更新时间
     */
    public Timestamp getUpdateTime() {
        return this.updateTime;
    }

    /**
     * 设置 更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取 创建时间
     *
     * @return createTimestamp 创建时间
     */
    public Timestamp getCreateTimestamp() {
        return this.createTimestamp;
    }

    /**
     * 设置 创建时间
     *
     * @param createTimestamp 创建时间
     */
    public void setCreateTimestamp(Timestamp createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    /**
     * 获取 标签来源
     *
     * @return tagSource 标签来源
     */
    public String getTagSource() {
        return this.tagSource;
    }

    /**
     * 设置 标签来源
     *
     * @param tagSource 标签来源
     */
    public void setTagSource(String tagSource) {
        this.tagSource = tagSource;
    }

    /**
     * 获取 更新者
     *
     * @return updateUser 更新者
     */
    public String getUpdateUser() {
        return this.updateUser;
    }

    /**
     * 设置 更新者
     *
     * @param updateUser 更新者
     */
    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * 获取 节点程序
     *
     * @return nodeAttriId 节点程序
     */
    public String getNodeAttriId() {
        return this.nodeAttriId;
    }

    /**
     * 设置 节点程序
     *
     * @param nodeAttriId 节点程序
     */
    public void setNodeAttriId(String nodeAttriId) {
        this.nodeAttriId = nodeAttriId;
    }
}