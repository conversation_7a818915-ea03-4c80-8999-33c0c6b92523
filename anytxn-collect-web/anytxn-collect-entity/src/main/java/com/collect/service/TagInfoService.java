package com.collect.service;


import com.collect.entity.TagInfo;

import java.util.List;

/**
 * 标签信息服务接口
 */
public interface TagInfoService {
    
    /**
     * 根据标签ID查询标签信息
     * @param tagId 标签ID
     * @return 标签信息
     */
    TagInfo getById(String tagId);
    
    /**
     * 查询所有标签信息
     * @return 标签信息列表
     */
    List<TagInfo> getAll();
    
    /**
     * 根据标签类型查询标签信息
     * @param tagType 标签类型
     * @return 标签信息列表
     */
    List<TagInfo> getByType(String tagType);
    
    /**
     * 添加标签信息
     * @param tagInfo 标签信息
     * @return 是否成功
     */
    boolean add(TagInfo tagInfo);
    
    /**
     * 更新标签信息
     * @param tagInfo 标签信息
     * @return 是否成功
     */
    boolean update(TagInfo tagInfo);
    
    /**
     * 根据标签ID删除标签信息
     * @param tagId 标签ID
     * @return 是否成功
     */
    boolean deleteById(String tagId);
    
    /**
     * 根据条件查询标签信息
     * @param condition 查询条件，如果为null或不传任何属性则查询全部
     * @return 查询结果列表
     */
    List<TagInfo> getByCondition(TagInfo condition);
} 