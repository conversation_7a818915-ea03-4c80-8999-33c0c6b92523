package com.collect.service.impl;


import com.collect.entity.WcsBasRepaymentPO;
import com.collect.mapper.WcsBasRepaymentMapper;
import com.collect.service.WcsBasRepaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class WcsBasRepaymentServiceImpl implements WcsBasRepaymentService {

    @Autowired
    private WcsBasRepaymentMapper wcsBasRepaymentMapper;

    @Override
    public List<WcsBasRepaymentPO> selectAll() {
        return wcsBasRepaymentMapper.selectAll();
    }

    @Override
    public List<WcsBasRepaymentPO> selectByCaseCode(String caseCode) {
        return wcsBasRepaymentMapper.selectByCaseCode(caseCode);
    }

    @Override
    public int insertSelective(WcsBasRepaymentPO record) {
        return wcsBasRepaymentMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(WcsBasRepaymentPO record) {
        return wcsBasRepaymentMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return wcsBasRepaymentMapper.deleteByPrimaryKey(id);
    }
}
