package com.collect.service.impl;

import com.collect.entity.TaskNodeActionInfo;
import com.collect.mapper.TaskNodeActionInfoMapper;
import com.collect.service.TaskNodeActionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 任务节点动作信息服务实现类
 */
@Service
public class TaskNodeActionInfoServiceImpl implements TaskNodeActionInfoService {

    @Autowired
    private TaskNodeActionInfoMapper taskNodeActionInfoMapper;

    @Override
    public List<TaskNodeActionInfo> getByCondition(TaskNodeActionInfo condition) {
        if (condition == null) {
            condition = new TaskNodeActionInfo();
        }
        return taskNodeActionInfoMapper.selectByCondition(condition);
    }
} 