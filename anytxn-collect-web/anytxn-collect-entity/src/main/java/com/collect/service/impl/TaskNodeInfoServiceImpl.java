package com.collect.service.impl;

import com.collect.entity.TaskNodeInfo;
import com.collect.mapper.TaskNodeInfoMapper;
import com.collect.service.TaskNodeInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 任务节点信息服务实现类
 */
@Service
public class TaskNodeInfoServiceImpl implements TaskNodeInfoService {

    @Autowired
    private TaskNodeInfoMapper taskNodeInfoMapper;

    @Override
    public List<TaskNodeInfo> getByCondition(TaskNodeInfo condition) {
        if (condition == null) {
            condition = new TaskNodeInfo();
        }
        return taskNodeInfoMapper.selectByCondition(condition);
    }

    @Override
    public boolean add(TaskNodeInfo condition) {
        return taskNodeInfoMapper.insert(condition) > 0;
    }
} 