package com.collect.service;

import com.collect.entity.NodeFlowchartInfo;

import java.util.List;

/**
 * 节点流程图信息服务接口
 */
public interface NodeFlowchartInfoService {

    /**
     * 添加节点流程图信息
     * @param nodeFlowchartInfo 节点流程图信息
     * @return 是否成功
     */
    boolean add(NodeFlowchartInfo nodeFlowchartInfo);
    
    /**
     * 更新节点流程图信息
     * @param nodeFlowchartInfo 节点流程图信息
     * @return 是否成功
     */
    boolean update(NodeFlowchartInfo nodeFlowchartInfo);
    
    /**
     * 根据ID删除节点流程图信息
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteById(Long id);
    
    /**
     * 根据流程图ID删除节点流程图信息
     * @param flowchartId 流程图ID
     * @return 是否成功
     */
    boolean deleteByFlowchartId(String flowchartId);
    
    /**
     * 根据条件查询流程图信息
     * @param condition 查询条件，如果为null或不传任何属性则查询全部
     * @return 查询结果列表
     */
    List<NodeFlowchartInfo> getByCondition(NodeFlowchartInfo condition);
} 