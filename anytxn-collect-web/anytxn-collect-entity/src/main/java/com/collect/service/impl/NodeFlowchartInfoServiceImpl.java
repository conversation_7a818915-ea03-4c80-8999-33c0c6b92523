package com.collect.service.impl;

import com.collect.entity.NodeFlowchartInfo;
import com.collect.mapper.NodeFlowchartInfoMapper;
import com.collect.service.NodeFlowchartInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 节点流程图信息服务实现类
 */
@Service
public class NodeFlowchartInfoServiceImpl implements NodeFlowchartInfoService {

    @Autowired
    private NodeFlowchartInfoMapper nodeFlowchartInfoMapper;


    @Override
    public boolean add(NodeFlowchartInfo nodeFlowchartInfo) {
        // 如果未设置流程图ID，则自动生成
        if (nodeFlowchartInfo.getFlowchartId() == null || nodeFlowchartInfo.getFlowchartId().isEmpty()) {
            nodeFlowchartInfo.setFlowchartId(UUID.randomUUID().toString().replace("-", "").substring(0, 20));
        }
        
        // 设置初始版本号
        if (nodeFlowchartInfo.getVersion() == null) {
            nodeFlowchartInfo.setVersion(1);
        }
        
        return nodeFlowchartInfoMapper.insert(nodeFlowchartInfo) > 0;
    }

    @Override
    public boolean update(NodeFlowchartInfo nodeFlowchartInfo) {
        return nodeFlowchartInfoMapper.update(nodeFlowchartInfo) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return nodeFlowchartInfoMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteByFlowchartId(String flowchartId) {
        return nodeFlowchartInfoMapper.deleteByFlowchartId(flowchartId) > 0;
    }

    @Override
    public List<NodeFlowchartInfo> getByCondition(NodeFlowchartInfo condition) {
        if (condition == null) {
            condition = new NodeFlowchartInfo();
        }
        return nodeFlowchartInfoMapper.selectByCondition(condition);
    }
} 