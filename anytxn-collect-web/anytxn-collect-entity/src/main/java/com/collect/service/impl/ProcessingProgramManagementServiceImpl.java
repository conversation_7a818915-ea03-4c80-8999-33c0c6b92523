package com.collect.service.impl;

import com.collect.entity.ProcessingProgramManagement;
import com.collect.mapper.ProcessingProgramManagementMapper;
import com.collect.service.ProcessingProgramManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 处理程序管理服务实现类
 */
@Service
public class ProcessingProgramManagementServiceImpl implements ProcessingProgramManagementService {

    @Autowired
    private ProcessingProgramManagementMapper processingProgramManagementMapper;


    @Override
    public boolean add(ProcessingProgramManagement processingProgram) {
        // 如果未设置节点属性ID，则自动生成
        if (processingProgram.getNodeAttriId() == null || processingProgram.getNodeAttriId().isEmpty()) {
            processingProgram.setNodeAttriId(UUID.randomUUID().toString().replace("-", ""));
        }
        return processingProgramManagementMapper.insert(processingProgram) > 0;
    }

    @Override
    public boolean update(ProcessingProgramManagement processingProgram) {
        return processingProgramManagementMapper.update(processingProgram) > 0;
    }

    @Override
    public boolean deleteById(String nodeAttriId) {
        return processingProgramManagementMapper.deleteById(nodeAttriId) > 0;
    }

    @Override
    public List<ProcessingProgramManagement> getByCondition(ProcessingProgramManagement condition) {
        if (condition == null) {
            condition = new ProcessingProgramManagement();
        }
        return processingProgramManagementMapper.selectByCondition(condition);
    }
} 