package com.collect.service;

import com.collect.entity.WcsCasState;

import java.util.List;

/**
 * 案件状态信息服务接口
 */
public interface WcsCasStateService {

    /**
     * 添加案件状态信息
     * @param wcsCasState 案件状态信息
     * @return 是否成功
     */
    boolean add(WcsCasState wcsCasState);
    
    /**
     * 更新案件状态信息
     * @param wcsCasState 案件状态信息
     * @return 是否成功
     */
    boolean update(WcsCasState wcsCasState);
    
    /**
     * 根据ID删除案件状态信息
     * @param id 主键ID
     * @return 是否成功
     */
    boolean deleteById(Long id);
    
    /**
     * 根据案件号删除案件状态信息
     * @param caseCode 案件号
     * @return 是否成功
     */
    boolean deleteByCaseCode(String caseCode);
    
    /**
     * 根据条件查询案件状态信息
     * @param condition 查询条件，如果为null或不传任何属性则查询全部
     * @return 查询结果列表
     */
    List<WcsCasState> getByCondition(WcsCasState condition);
} 