package com.collect.service.impl;

import com.collect.entity.WcsCasState;
import com.collect.mapper.WcsCasStateMapper;
import com.collect.service.WcsCasStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 案件状态信息服务实现类
 */
@Service
public class WcsCasStateServiceImpl implements WcsCasStateService {

    @Autowired
    private WcsCasStateMapper wcsCasStateMapper;

    @Override
    public boolean add(WcsCasState wcsCasState) {
        // 如果未设置更新时间，则自动设置为当前时间
        if (wcsCasState.getUpdateTime() == null) {
            wcsCasState.setUpdateTime(LocalDateTime.now());
        }
        
        return wcsCasStateMapper.insert(wcsCasState) > 0;
    }

    @Override
    public boolean update(WcsCasState wcsCasState) {
        // 自动设置更新时间为当前时间
        wcsCasState.setUpdateTime(LocalDateTime.now());
        
        return wcsCasStateMapper.update(wcsCasState) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return wcsCasStateMapper.deleteById(id) > 0;
    }

    @Override
    public boolean deleteByCaseCode(String caseCode) {
        return wcsCasStateMapper.deleteByCaseCode(caseCode) > 0;
    }

    @Override
    public List<WcsCasState> getByCondition(WcsCasState condition) {
        if (condition == null) {
            condition = new WcsCasState();
        }
        return wcsCasStateMapper.selectByCondition(condition);
    }
} 