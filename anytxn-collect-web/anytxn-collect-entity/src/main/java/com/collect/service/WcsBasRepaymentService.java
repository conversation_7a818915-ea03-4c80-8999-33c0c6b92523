package com.collect.service;


import com.collect.entity.WcsBasRepaymentPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WcsBasRepaymentService {
    List<WcsBasRepaymentPO> selectAll();

    List<WcsBasRepaymentPO> selectByCaseCode(String caseCode);

    int insertSelective(WcsBasRepaymentPO record);

    int updateByPrimaryKeySelective(WcsBasRepaymentPO record);

    int deleteByPrimaryKey(Long id);
}
