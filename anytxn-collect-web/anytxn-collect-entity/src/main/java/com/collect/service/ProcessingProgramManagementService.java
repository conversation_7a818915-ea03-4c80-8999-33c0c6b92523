package com.collect.service;

import com.collect.entity.ProcessingProgramManagement;

import java.util.List;

/**
 * 处理程序管理服务接口
 */
public interface ProcessingProgramManagementService {
    

    /**
     * 添加处理程序信息
     * @param processingProgram 处理程序信息
     * @return 是否成功
     */
    boolean add(ProcessingProgramManagement processingProgram);
    
    /**
     * 更新处理程序信息
     * @param processingProgram 处理程序信息
     * @return 是否成功
     */
    boolean update(ProcessingProgramManagement processingProgram);
    
    /**
     * 根据节点属性ID删除处理程序信息
     * @param nodeAttriId 节点属性ID
     * @return 是否成功
     */
    boolean deleteById(String nodeAttriId);
    
    /**
     * 根据条件查询处理程序信息
     * @param condition 查询条件，如果为null或不传任何属性则查询全部
     * @return 查询结果列表
     */
    List<ProcessingProgramManagement> getByCondition(ProcessingProgramManagement condition);
} 