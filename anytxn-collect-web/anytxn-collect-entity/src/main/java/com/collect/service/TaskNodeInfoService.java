package com.collect.service;

import com.collect.entity.TagInfo;
import com.collect.entity.TaskNodeInfo;

import java.util.List;

/**
 * 任务节点信息服务接口
 */
public interface TaskNodeInfoService {

    /**
     * 根据条件查询任务节点信息
     * @param condition 查询条件，如果为null或不传任何属性则查询全部
     * @return 查询结果列表
     */
    List<TaskNodeInfo> getByCondition(TaskNodeInfo condition);
    /**
     * 添加任务节点信息
     * @param condition 任务节点信息
     * @return 是否成功
     */
    boolean add(TaskNodeInfo condition);

} 