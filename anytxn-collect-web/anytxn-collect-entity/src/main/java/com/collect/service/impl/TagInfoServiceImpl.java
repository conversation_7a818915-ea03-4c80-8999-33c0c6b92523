package com.collect.service.impl;


import com.collect.entity.TagInfo;
import com.collect.mapper.TagInfoMapper;
import com.collect.service.TagInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 标签信息服务实现类
 */
@Service
public class TagInfoServiceImpl implements TagInfoService {

    @Autowired
    private TagInfoMapper tagInfoMapper;

    @Override
    public TagInfo getById(String tagId) {
        return tagInfoMapper.selectById(tagId);
    }

    @Override
    public List<TagInfo> getAll() {
        return tagInfoMapper.selectAll();
    }

    @Override
    public List<TagInfo> getByType(String tagType) {
        return tagInfoMapper.selectByType(tagType);
    }

    @Override
    public boolean add(TagInfo tagInfo) {
        // 如果未设置标签ID，则自动生成
        if (tagInfo.getTagId() == null || tagInfo.getTagId().isEmpty()) {
            tagInfo.setTagId(UUID.randomUUID().toString().replace("-", ""));
        }
        return tagInfoMapper.insert(tagInfo) > 0;
    }

    @Override
    public boolean update(TagInfo tagInfo) {
        return tagInfoMapper.update(tagInfo) > 0;
    }

    @Override
    public boolean deleteById(String tagId) {
        return tagInfoMapper.deleteById(tagId) > 0;
    }

    @Override
    public List<TagInfo> getByCondition(TagInfo condition) {
        if (condition == null) {
            condition = new TagInfo();
        }
        return tagInfoMapper.selectByCondition(condition);
    }
} 