package com.collect.constant;

import com.anytxn.base.constant.ReqHeader;

import java.time.LocalDate;
import java.util.Set;

/**
 * 请求体的header.
 *
 * <AUTHOR>
 * @date 2024 /9/17 10:06
 */
public final class RequestHeader extends ReqHeader {

    /**
     * 网关生成-客户鉴别标识
     * 路由相关的标识处理.
     *
     * @see CustRecogFlag
     */
    private CustRecogFlag custRecogFlag;

    /**
     * 网关生成-客户号
     */
    private String custRecogNo;

    /**
     * 网关生成-持卡人编号
     */
    private String crcdCardholderNo;

    /**
     * 機房標識.
     * //一般由網關傳入。
     */
    private String idc;

    /**
     * 网关生成-信用卡唯一機構號.
     */
    private String crcdOrgNo;

    /**
     * 网关生成-数据位置.
     */
    private String dataPosition;

    /**
     * 网关生成-故障单元.
     */
    private Set<String> faultUnits;

    /**
     * 网关生成-业务日期
     */
    private LocalDate bizDate;

    /**
     * partNo.
     */
    private Long partNo;

    /**
     * Gets part no.
     *
     * @return the part no
     */
    public Long getPartNo() {
        return partNo;
    }

    /**
     * Sets part no.
     *
     * @param partNo the part no
     */
    public void setPartNo(final Long partNo) {
        this.partNo = partNo;
    }

    /**
     * Gets biz date.
     *
     * @return the biz date
     */
    public LocalDate getBizDate() {
        return bizDate;
    }

    /**
     * Sets biz date.
     *
     * @param bizDate the biz date
     */
    public void setBizDate(final LocalDate bizDate) {
        this.bizDate = bizDate;
    }

    /**
     * Gets idc.
     *
     * @return the idc
     */
    public String getIdc() {
        return idc;
    }

    /**
     * Sets idc.
     *
     * @param idc the idc
     */
    public void setIdc(String idc) {
        this.idc = idc;
    }

    /**
     * Gets crcd org no.
     *
     * @return the crcd org no
     */
    public String getCrcdOrgNo() {
        return crcdOrgNo;
    }

    /**
     * Sets crcd org no.
     *
     * @param crcdOrgNo the crcd org no
     */
    public void setCrcdOrgNo(final String crcdOrgNo) {
        this.crcdOrgNo = crcdOrgNo;
    }

    /**
     * Gets data position.
     *
     * @return the data position
     */
    public String getDataPosition() {
        return dataPosition;
    }

    /**
     * Sets data position.
     *
     * @param dataPosition the data position
     */
    public void setDataPosition(String dataPosition) {
        this.dataPosition = dataPosition;
    }

    /**
     * Gets fault units.
     *
     * @return the fault units
     */
    public Set<String> getFaultUnits() {
        return faultUnits;
    }

    /**
     * Sets fault units.
     *
     * @param faultUnits the fault units
     */
    public void setFaultUnits(Set<String> faultUnits) {
        this.faultUnits = faultUnits;
    }

    /**
     * Gets cust recog flag.
     *
     * @return the cust recog flag
     */
    public CustRecogFlag getCustRecogFlag() {
        return custRecogFlag;
    }

    /**
     * Sets cust recog flag.
     *
     * @param custRecogFlag the cust recog flag
     */
    public void setCustRecogFlag(CustRecogFlag custRecogFlag) {
        this.custRecogFlag = custRecogFlag;
    }

    /**
     * Gets cust recog no.
     *
     * @return the cust recog no
     */
    public String getCustRecogNo() {
        return custRecogNo;
    }

    /**
     * Sets cust recog no.
     *
     * @param custRecogNo the cust recog no
     */
    public void setCustRecogNo(String custRecogNo) {
        this.custRecogNo = custRecogNo;
    }

    /**
     * Gets crcd cardholder no.
     *
     * @return the crcd cardholder no
     */
    public String getCrcdCardholderNo() {
        return crcdCardholderNo;
    }

    /**
     * Sets crcd cardholder no.
     *
     * @param crcdCardholderNo the crcd cardholder no
     */
    public void setCrcdCardholderNo(String crcdCardholderNo) {
        this.crcdCardholderNo = crcdCardholderNo;
    }
}
