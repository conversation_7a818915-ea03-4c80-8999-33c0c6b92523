package com.collect.constant;

/**
 * The type Page.
 *
 * <AUTHOR>
 * @date 2024 /12/17 14:44
 */
public class PageInfo {

    /**
     * 当前页
     */
    private Integer pageNo;

    /**
     * 页条数.
     */
    private Integer pageSize;

    /**
     * 返回值：总条数.
     */
    private Long totalCount;

    /**
     * Instantiates a new Page info.
     */
    public PageInfo() {

    }

    /**
     * Instantiates a new Page info.
     *
     * @param pageNo   the page no
     * @param pageSize the page size
     */
    public PageInfo(final Integer pageNo, final Integer pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    /**
     * Instantiates a new Page info.
     *
     * @param pageNo     the page no
     * @param pageSize   the page size
     * @param totalCount the total count
     */
    public PageInfo(final Integer pageNo, final Integer pageSize, final Long totalCount) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
    }

    /**
     * Page page info.
     *
     * @param pageNo   the page no
     * @param pageSize the page size
     * @return the page info
     */
    public static PageInfo page(final Integer pageNo, final Integer pageSize) {
        return new PageInfo(pageNo, pageSize);
    }

    /**
     * Page page info.
     *
     * @param pageNo     the page no
     * @param pageSize   the page size
     * @param totalCount the total count
     * @return the page info
     */
    public static PageInfo page(final Integer pageNo, final Integer pageSize, final Long totalCount) {
        return new PageInfo(pageNo, pageSize, totalCount);
    }

    /**
     * Gets page no.
     *
     * @return the page no
     */
    public Integer getPageNo() {
        return pageNo;
    }

    /**
     * Sets page no.
     *
     * @param pageNo the page no
     */
    public void setPageNo(final Integer pageNo) {
        this.pageNo = pageNo;
    }

    /**
     * Gets page size.
     *
     * @return the page size
     */
    public Integer getPageSize() {
        return pageSize;
    }

    /**
     * Sets page size.
     *
     * @param pageSize the page size
     */
    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }

    /**
     * Gets total count.
     *
     * @return the total count
     */
    public Long getTotalCount() {
        return totalCount;
    }

    /**
     * Sets total count.
     *
     * @param totalCount the total count
     */
    public void setTotalCount(final Long totalCount) {
        this.totalCount = totalCount;
    }
}
