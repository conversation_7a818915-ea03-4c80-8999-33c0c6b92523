package com.collect.constant;

import com.anytxn.base.lang.ThreadCache;

/**
 * 请求体的上下文保存.
 *
 * <AUTHOR>
 * @date 2024 /8/18 00:45
 */
public final class Context {

    private Context() {
    }

    /**
     * Get string.
     *
     * @param key the key
     * @return the string
     */
    public static String get(String key) {
        return ThreadCache.get(key);
    }


    /**
     * Add.
     *
     * @param key   the key
     * @param value the value
     */
    public static void add(String key, String value) {
        ThreadCache.set(key, value);
    }

    /**
     * Add g info.
     *
     * @param value the value
     */
    public static void addGInfo(GInfo value) {
        ThreadCache.set(CrcdConstant.GINFO_KEY.getKey1(), value);
    }

    /**
     * Gets g info.
     *
     * @return the g info
     */
    public static GInfo getGInfo() {
        GInfo o = ThreadCache.get(CrcdConstant.GINFO_KEY.getKey1());
        if (o == null) {
            o = new GInfo();
            Context.addGInfo(o);
        }
        return o;
    }

    /**
     * New g info.
     *
     * @return the g info
     */
    public static GInfo newGInfo() {
        return Context.getGInfo();
    }

    /**
     * Remove.
     *
     * @param key the key
     */
    public static void remove(String key) {
        ThreadCache.remove(key);
    }

    /**
     * Clear.
     */
    public static void removeGInfo() {
        remove(CrcdConstant.GINFO_KEY.getKey1());
    }
}
