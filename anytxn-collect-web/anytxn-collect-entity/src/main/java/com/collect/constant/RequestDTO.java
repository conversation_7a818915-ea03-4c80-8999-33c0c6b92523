package com.collect.constant;

import com.anytxn.base.constant.Request;
import com.anytxn.base.utils.ObjectUtils;

/**
 * 通用请求体的定义.
 *
 * <AUTHOR>
 * @date 2024 /8/18 20:41
 */
public class RequestDTO implements Request<RequestHeader> {

    /**
     * 请求头.
     */
    private RequestHeader header = new RequestHeader();

    /**
     * 记录创建人.
     */
    private String createUser;

    /**
     * 记录修改人.
     */
    private String updateUser;

    /**
     * Gets create user.
     *
     * @return the create user
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * Sets create user.
     *
     * @param createUser the create user
     */
    public void setCreateUser(final String createUser) {
        this.createUser = createUser;
    }

    /**
     * Gets update user.
     *
     * @return the update user
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * Sets update user.
     *
     * @param updateUser the update user
     */
    public void setUpdateUser(final String updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public RequestHeader getHeader() {
        return header;
    }

    @Override
    public void setHeader(final RequestHeader header) {
        this.header = header;
    }

    /**
     * To assign g info.
     *
     * @param gInfo the g info
     */
    public void toAssignGInfo(final GInfo gInfo) {
        if (gInfo == null) {
            return;
        }
        RequestHeader header = getHeader();
        gInfo.setGid(ObjectUtils.getAnother(gInfo.getGid(), header.getGid()));
        gInfo.setMsgId(ObjectUtils.getAnother(gInfo.getMsgId(), header.getMsgId()));
        gInfo.setBizDate(ObjectUtils.getAnother(gInfo.getBizDate(), header.getBizDate()));
        gInfo.setCreateUser(ObjectUtils.getAnother(gInfo.getCreateUser(), getCreateUser()));
        gInfo.setUpdateUser(ObjectUtils.getAnother(gInfo.getUpdateUser(), getUpdateUser()));
        gInfo.setPartNo(ObjectUtils.getAnother(gInfo.getPartNo(), header.getPartNo()));
        gInfo.setPath(ObjectUtils.getAnother(gInfo.getPath(), header.getPath()));
        gInfo.setUnitNo(ObjectUtils.getAnother(gInfo.getUnitNo(), header.getUnitNo()));
        gInfo.setDataPosition(ObjectUtils.getAnother(gInfo.getDataPosition(), header.getDataPosition()));
        gInfo.setCustRecogFlag(ObjectUtils.getAnother(gInfo.getCustRecogFlag(), header.getCustRecogFlag()));
        gInfo.setCustRecogNo(ObjectUtils.getAnother(gInfo.getCustRecogNo(), header.getCustRecogNo()));
        gInfo.setCrcdCardholderNo(ObjectUtils.getAnother(gInfo.getCrcdCardholderNo(), header.getCrcdCardholderNo()));
        gInfo.setFaultUnits(ObjectUtils.getAnother(gInfo.getFaultUnits(), header.getFaultUnits()));
        gInfo.setCrcdOrgNo(ObjectUtils.getAnother(gInfo.getCrcdOrgNo(), header.getCrcdOrgNo()));
        gInfo.setSrcSystem(ObjectUtils.getAnother(gInfo.getSrcSystem(), header.getSrcSystem()));
    }
}
