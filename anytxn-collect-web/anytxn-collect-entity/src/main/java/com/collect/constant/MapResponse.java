package com.collect.constant;

import java.util.Map;

/**
 * The type Map response.
 *
 * <AUTHOR>
 * @date 2024 /8/19 21:57
 */
public non-sealed class MapResponse extends ResponseDTO<Map<String, Object>> {

    /**
     * Of map response.
     *
     * @param header the header
     * @return the map response
     */
    public static MapResponse of(ResponseHeader header) {
        MapResponse mapResponse = new MapResponse();
        mapResponse.setHeader(header);
        mapResponse.setData(Map.of());
        return mapResponse;
    }
}
