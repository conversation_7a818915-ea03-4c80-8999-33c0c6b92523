package com.collect.constant;

import com.anytxn.base.constant.RspHeader;
import com.anytxn.base.error.CommonError;
import com.anytxn.base.error.ErrorCode;
import com.anytxn.base.lang.i18n.I18nMessage;

import java.util.Objects;

/**
 * The type Response header.
 *
 * <AUTHOR>
 * @date 2024 /8/19 22:43
 */
public final class ResponseHeader extends RspHeader {

    /**
     * 错误码.
     */
    private String errorCode;

    /**
     * 错误信息.
     */
    private String errorMsg;

    /**
     * Instantiates a new Response header.
     */
    public ResponseHeader() {
    }

    /**
     * Instantiates a new Rsp header.
     *
     * @param errorCode the error code
     * @param errorMsg  the error msg
     */
    public ResponseHeader(final String errorCode, final String errorMsg) {
        super(errorCode, errorMsg);
    }

    /**
     * Of response header.
     *
     * @param errorCode the error code
     * @param args      the args
     * @return the response header
     */
    public static ResponseHeader of(ErrorCode errorCode, Object... args) {
        ResponseHeader responseHeader = new ResponseHeader();
        responseHeader.setErrorCode(errorCode.getCode());
        responseHeader.setErrorMsg(I18nMessage.getMessage(errorCode.getI18nKey(), args));
        return responseHeader;
    }

    /**
     * Gets error code.
     *
     * @return the error code
     */
    public String getErrorCode() {
        return errorCode;
    }


    /**
     * Sets error code.
     *
     * @param errorCode the error code
     */
    @Override
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * Gets error msg.
     *
     * @return the error msg
     */
    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * Sets error msg.
     *
     * @param errorMsg the error msg
     */
    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * Is success boolean.
     *
     * @return the boolean
     */
    public boolean isSuccess() {
        return Objects.equals(getErrorCode(), CommonError.SUCCESS.getCode());
    }
}
