package com.collect.constant;

import com.anytxn.base.constant.Response;
import com.anytxn.base.error.CommonError;
import com.anytxn.base.error.ErrorCode;

/**
 * 父类的响应体.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @date 2024 /8/18 20:43
 */
public sealed class ResponseDTO<T> implements Response<T, ResponseHeader> permits MapResponse, PageResponseDTO {

    /**
     * 响应头.
     */
    private ResponseHeader header;

    /**
     * 数据.
     */
    private T data;

    /**
     * Success response dto.
     *
     * @param <T>  the type parameter
     * @param data the data
     * @return the response dto
     */
    public static <T> ResponseDTO<T> success(T data) {
        ResponseDTO<T> dto = new ResponseDTO<>();
        ResponseHeader header = new ResponseHeader();
        header.setErrorCode(CommonError.SUCCESS.getCode());
        header.setErrorMsg(CommonError.SUCCESS.getMessage());
        dto.header = header;
        dto.data = data;
        return dto;
    }

    /**
     * Success response dto.
     *
     * @param <T> the type parameter
     * @return the response dto
     */
    public static <T> ResponseDTO<T> success() {
        ResponseDTO<T> dto = new ResponseDTO<>();
        ResponseHeader header = new ResponseHeader();
        header.setErrorCode(CommonError.SUCCESS.getCode());
        header.setErrorMsg(CommonError.SUCCESS.getMessage());
        dto.header = header;
        dto.data = null;
        return dto;
    }


    /**
     * Fail response dto.
     *
     * @param <T>       the type parameter
     * @param errorCode the error code
     * @return the response dto
     */
    public static <T> ResponseDTO<T> fail(ErrorCode errorCode) {
        ResponseDTO<T> dto = new ResponseDTO<>();
        ResponseHeader header = new ResponseHeader();
        header.setErrorCode(errorCode.getCode());
        header.setErrorMsg(errorCode.getMessage());
        dto.header = header;
        return dto;
    }

    public static <T> ResponseDTO<T> failWithData(ErrorCode errorCode, T data) {
        ResponseDTO<T> dto = new ResponseDTO<>();
        ResponseHeader header = new ResponseHeader();
        header.setErrorCode(errorCode.getCode());
        header.setErrorMsg(errorCode.getMessage());
        dto.header = header;
        dto.data = data;
        return dto;
    }

    /**
     * Fail response dto.
     *
     * @param <T>       the type parameter
     * @param errorCode the error code
     * @param args      the args
     * @return the response dto
     */
    public static <T> ResponseDTO<T> fail(ErrorCode errorCode, Object... args) {
        ResponseDTO<T> dto = new ResponseDTO<>();
        ResponseHeader header = new ResponseHeader();
        header.setErrorCode(errorCode.getCode());
        header.setErrorMsg(errorCode.getMessage(args));
        dto.header = header;
        return dto;
    }

    /**
     * Gets header.
     *
     * @return the header
     */
    @Override
    public ResponseHeader getHeader() {
        return header;
    }

    @Override
    public T getData() {
        return data;
    }

    /**
     * Sets header.
     *
     * @param header the header
     */
    public void setHeader(ResponseHeader header) {
        this.header = header;
    }

    /**
     * Sets data.
     *
     * @param data the data
     */
    public void setData(T data) {
        this.data = data;
    }
}
