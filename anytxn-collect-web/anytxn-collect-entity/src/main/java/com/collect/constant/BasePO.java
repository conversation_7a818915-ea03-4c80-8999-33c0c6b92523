package com.collect.constant;

import com.anytxn.base.constant.DBValue;
import com.anytxn.base.utils.IdUtils;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 通一请求的PO对象.
 *
 * <AUTHOR>
 * @date 2024 /8/26 23:20
 */
public class BasePO extends Base implements DBValue {

    /**
     * 自增主键ID.
     */
    private Long incId;

    /**
     * 全局流水号.
     */
    private String globalTrxJrnNo;

    /**
     * 版本号.
     */
    private Long version;

    /**
     * 同步时间.
     */
    private LocalDateTime syncTime;

    /**
     * Gets inc id.
     *
     * @return the inc id
     */
    public Long getIncId() {
        return incId;
    }

    /**
     * Sets inc id.
     *
     * @param incId the inc id
     */
    public void setIncId(final Long incId) {
        this.incId = incId;
    }

    /**
     * Gets global trx jrn no.
     *
     * @return the global trx jrn no
     */
    public String getGlobalTrxJrnNo() {
        return globalTrxJrnNo;
    }

    /**
     * Sets global trx jrn no.
     *
     * @param globalTrxJrnNo the global trx jrn no
     */
    public void setGlobalTrxJrnNo(final String globalTrxJrnNo) {
        this.globalTrxJrnNo = globalTrxJrnNo;
    }

    /**
     * Gets version.
     *
     * @return the version
     */
    public Long getVersion() {
        return version;
    }

    /**
     * Sets version.
     *
     * @param version the version
     */
    public void setVersion(final Long version) {
        this.version = version;
    }

    /**
     * Gets sync time.
     *
     * @return the sync time
     */
    public LocalDateTime getSyncTime() {
        return syncTime;
    }

    /**
     * Sets sync time.
     *
     * @param syncTime the sync time
     */
    public void setSyncTime(final LocalDateTime syncTime) {
        this.syncTime = syncTime;
    }

    @Override
    public void resolve() {
        if (this.getUpdateTime() == null) {
            this.setUpdateTime(LocalDateTime.now());
        }
        if (this.getCreateTime() == null) {
            this.setCreateTime(LocalDateTime.now());
        }
        GInfo info = GInfo.getContextGinfo();
        if (this.getCrcdOrgNo() == null) {
            this.setCrcdOrgNo(info.getCrcdOrgNo());
        }
        if (this.getPartNo() == null) {
            this.setPartNo(info.getPartNo());
        }
        if (this.getBizDate() == null) {
            this.setBizDate(info.getBizDate());
        }
        if (this.getCreateUser() == null) {
            this.setCreateUser(Optional.ofNullable(info.getCreateUser()).orElse("sys"));
        }
        if (this.getUpdateUser() == null) {
            this.setUpdateUser(Optional.ofNullable(info.getUpdateUser()).orElse("sys"));
        }
        if (this.getDataPosition() == null) {
            this.setDataPosition(info.getDataPosition());
        }
        if (this.getGid() == null) {
            this.setGid(info.getGid());
        }
        if (this.getGlobalTrxJrnNo() == null) {
            this.setGlobalTrxJrnNo(info.getGid());
        }
        if (this.getCrcdOrgNo() == null) {
            this.setCrcdOrgNo(info.getCrcdOrgNo());
        }
    }

    @Override
    public void buildId() {
        if (this.getSerialId() != null && this.getIncId() == null) {
            this.setIncId(this.getSerialId());
        } else if (this.getIncId() == null && this.getSerialId() == null) {
            Long id = IdUtils.getId();
            this.setIncId(id);
            this.setSerialId(id);
        }
    }
}
