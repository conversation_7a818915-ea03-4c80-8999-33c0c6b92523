package com.collect.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 用于路由处理的标识位.
 *
 * <AUTHOR>
 * @date 2024 /11/6 15:24
 */
public enum CustRecogFlag {

    /**
     * 持卡人
     */
    RECOG_CRCD_CARDHOLDER_NO("1", "crcdCardholderNo"),
    /**
     * 全行客户号
     */
    RECOG_CUST_NO("2", "custNo", "ecifNo"),
    /**
     * 手机号
     */
    RECOG_MOBILE_NO("3", "mobileNo"),
    /**
     * 证件号
     */
    RECOG_ID_NO("4", "idNo"),

    /**
     * 卡号
     */
    RECOS_CRCD_NO("5", "crcdNo"),
    /**
     * 账户
     */
    RECOG_ACCOUNT_NO("6", "crcdAcctNo"),

    /**
     * 分期订单号
     */
    INSTALT_ORDER_NO("7", "instaltOrderNo"),

    /**
     * 条形码
     */
    RECOG_CRCD_BARCODE("8", "barcode", "crcdBarcode"),

    /**
     * Token
     */
    RECOG_TOKEN("9", "tokenNo", "token"),

    /**
     * 旧客户号
     */
    OLD_CUST_NO("10", "oldCustNo"),

    /**
     * PID卡号
     */
    PURSE_ID("11", "purseId");


    private final List<String> keys;
    private final String value;


    CustRecogFlag(String value, String... keys) {
        this.value = value;
        this.keys = Arrays.asList(keys);
    }

    /**
     * Gets by key.
     *
     * @param key the code
     * @return the by key
     */
    public static CustRecogFlag getByKey(String key) {
        for (CustRecogFlag routeFindStrategyEnum : values()) {
            if (routeFindStrategyEnum.keys.contains(key)) {
                return routeFindStrategyEnum;
            }
        }
        return null;
    }

    /**
     * 按价值获取
     *
     * @param key 钥匙
     * @return {@link CustRecogFlag }
     */
    public static CustRecogFlag getByValue(String key) {
        for (CustRecogFlag routeFindStrategyEnum : values()) {
            if (routeFindStrategyEnum.value.equals(key)) {
                return routeFindStrategyEnum;
            }
        }
        return null;
    }

    public String getValue() {
        return value;
    }
}
