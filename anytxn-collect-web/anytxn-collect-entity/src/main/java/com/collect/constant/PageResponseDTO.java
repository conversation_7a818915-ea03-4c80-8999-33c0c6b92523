package com.collect.constant;

import com.anytxn.base.error.CommonError;

/**
 * The type Page response dto.
 *
 * @param <T> the type parameter
 * <AUTHOR>
 * @date 2024 /12/17 14:42
 */
public final class PageResponseDTO<T> extends ResponseDTO<T> {

    private final PageInfo page;

    /**
     * Instantiates a new Page response dto.
     *
     * @param page the page
     */
    PageResponseDTO(PageInfo page) {
        this.page = page;
    }

    /**
     * Success response dto.
     *
     * @param <T>  the type parameter
     * @param page the page
     * @param data the data
     * @return the response dto
     */
    public static <T> PageResponseDTO<T> success(PageInfo page, T data) {
        PageResponseDTO<T> dto = new PageResponseDTO<>(page);
        ResponseHeader header = new ResponseHeader();
        header.setErrorCode(CommonError.SUCCESS.getCode());
        header.setErrorMsg(CommonError.SUCCESS.getMessage());
        dto.setData(data);
        dto.setHeader(header);
        return dto;
    }

    /**
     * Gets page.
     *
     * @return the page
     */
    public PageInfo getPage() {
        return page;
    }
}
