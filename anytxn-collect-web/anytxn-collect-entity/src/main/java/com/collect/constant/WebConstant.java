package com.collect.constant;

/**
 * 定义一些常量.
 *
 * <AUTHOR>
 * @date 2024 /11/19 15:21
 */
public final class WebConstant {

    /**
     * 用于对外授权的地址.
     */
    public static final String AUTH_PATH = "/web/sso/**";

    /**
     * 获取用户权限信息.
     */
    @SuppressWarnings("all")
    public static final String USER_PERMISSION_PATH = "/user/getUserPermission";
    /**
     * The constant gid.
     */
    public static final String GID = "gid";

    /**
     * The constant PARAM_SEQ_NO.
     */
//参数的字段.
    public static final String PARAM_SEQ_NO = "seqNo";

    /**
     * The constant UPDATE_USER.
     */
    public static final String UPDATE_USER = "updateUser";

    /**
     * The constant CREATE_USER.
     */
    public static final String CREATE_USER = "createUser";

    /**
     * The constant GID_KEY.
     */
    public static final String SRC_SYSTEM = "srcSystem";

    /**
     * The constant SRC_SYSTEM_VALUE.
     */
//表示后管系统
    public static final String SRC_SYSTEM_VALUE = "CCBS";

    /**
     * The constant GID_KEY.
     */
    public static final String MSG_ID = "msgId";

    /**
     * The constant header.
     */
    public static final String HEADER = "header";

    /**
     * The constant body.
     */
    public static final String BODY = "body";

    public static final String PATH = "path";
    private WebConstant() {
    }
}
