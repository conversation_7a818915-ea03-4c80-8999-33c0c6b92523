package com.collect.constant;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 定义统一下的Base 实体的处理.
 *
 * <AUTHOR>
 * @date 2024 /11/8 15:35
 */
public class Base {

    /**
     * 全局流水号.
     */
    private String gid;

    /**
     * 技术主键ID.
     */
    private Long serialId;

    /**
     * 数据位置.
     */
    private String dataPosition;

    /**
     * 业务日期
     */
    private LocalDate bizDate;

    /**
     * 修改人.
     */
    private String updateUser;

    /**
     * 创建人.
     */
    private String createUser;

    /**
     * 修改时间.
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间.
     */
    private LocalDateTime createTime;

    /**
     * 分片号.
     */
    private Long partNo;

    /**
     * 机构号.
     */
    private String crcdOrgNo;

    /**
     * Gets crcd org no.
     *
     * @return the crcd org no
     */
    public String getCrcdOrgNo() {
        return crcdOrgNo;
    }

    /**
     * Sets crcd org no.
     *
     * @param crcdOrgNo the crcd org no
     */
    public void setCrcdOrgNo(final String crcdOrgNo) {
        this.crcdOrgNo = crcdOrgNo;
    }

    /**
     * Gets gid.
     *
     * @return the gid
     */
    public String getGid() {
        return gid;
    }

    /**
     * Sets gid.
     *
     * @param gid the gid
     */
    public void setGid(final String gid) {
        this.gid = gid;
    }

    /**
     * Gets serial id.
     *
     * @return the serial id
     */
    public Long getSerialId() {
        return serialId;
    }

    /**
     * Sets serial id.
     *
     * @param serialId the serial id
     */
    public void setSerialId(final Long serialId) {
        this.serialId = serialId;
    }

    /**
     * Gets data position.
     *
     * @return the data position
     */
    public String getDataPosition() {
        return dataPosition;
    }

    /**
     * Sets data position.
     *
     * @param dataPosition the data position
     */
    public void setDataPosition(final String dataPosition) {
        this.dataPosition = dataPosition;
    }

    /**
     * Gets biz date.
     *
     * @return the biz date
     */
    public LocalDate getBizDate() {
        return bizDate;
    }

    /**
     * Sets biz date.
     *
     * @param bizDate the biz date
     */
    public void setBizDate(final LocalDate bizDate) {
        this.bizDate = bizDate;
    }

    /**
     * Gets update user.
     *
     * @return the update user
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * Sets update user.
     *
     * @param updateUser the update user
     */
    public void setUpdateUser(final String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * Gets create user.
     *
     * @return the create user
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * Sets create user.
     *
     * @param createUser the create user
     */
    public void setCreateUser(final String createUser) {
        this.createUser = createUser;
    }

    /**
     * Gets part no.
     *
     * @return the part no
     */
    public Long getPartNo() {
        return partNo;
    }

    /**
     * Sets part no.
     *
     * @param partNo the part no
     */
    public void setPartNo(final Long partNo) {
        this.partNo = partNo;
    }

    /**
     * Gets update time.
     *
     * @return the update time
     */
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    /**
     * Sets update time.
     *
     * @param updateTime the update time
     */
    public void setUpdateTime(final LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * Gets create time.
     *
     * @return the create time
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * Sets create time.
     *
     * @param createTime the create time
     */
    public void setCreateTime(final LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
