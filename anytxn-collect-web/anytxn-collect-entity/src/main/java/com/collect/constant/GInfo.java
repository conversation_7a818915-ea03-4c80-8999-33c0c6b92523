package com.collect.constant;

import com.anytxn.base.constant.Request;

import java.time.LocalDate;
import java.util.Set;

import static com.anytxn.base.utils.ObjectUtils.getAnother;

/**
 * 交易处理的统一的数据用于保存在ThreadLocal 上.
 *
 * <AUTHOR>
 * @date 2024 /8/22 20:06
 */
public class GInfo {

    private String gid;

    /**
     * 路径.
     */
    private String path;

    //網關對外的服務碼.
    private String msgId;

    /**
     * 业务日.
     */
    private LocalDate bizDate;

    private String unitNo;

    private Long partNo;

    private String dataPosition;

    private String createUser;

    private String updateUser;

    private Set<String> faultUnits;

    private String crcdOrgNo;

    private String srcSystem;

    /**
     * 客户鉴别标识
     */
    private CustRecogFlag custRecogFlag;

    /**
     * 客户号
     */
    private String custRecogNo;

    /**
     * 持卡人编号
     */
    private String crcdCardholderNo;

    /**
     * Gets src system.
     *
     * @return the src system
     */
    public String getSrcSystem() {
        return srcSystem;
    }

    /**
     * Sets src system.
     *
     * @param srcSystem the src system
     */
    public void setSrcSystem(final String srcSystem) {
        this.srcSystem = srcSystem;
    }

    /**
     * Gets crcd org no.
     *
     * @return the crcd org no
     */
    public String getCrcdOrgNo() {
        return crcdOrgNo;
    }

    /**
     * Sets crcd org no.
     *
     * @param crcdOrgNo the crcd org no
     */
    public void setCrcdOrgNo(final String crcdOrgNo) {
        this.crcdOrgNo = crcdOrgNo;
    }

    /**
     * Gets fault units.
     *
     * @return the fault units
     */
    public Set<String> getFaultUnits() {
        return faultUnits;
    }

    /**
     * Sets fault units.
     *
     * @param faultUnits the fault units
     */
    public void setFaultUnits(final Set<String> faultUnits) {
        this.faultUnits = faultUnits;
    }

    /**
     * Gets create user.
     *
     * @return the create user
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * Sets create user.
     *
     * @param createUser the create user
     */
    public void setCreateUser(final String createUser) {
        this.createUser = createUser;
    }

    /**
     * Gets update user.
     *
     * @return the update user
     */
    public String getUpdateUser() {
        return updateUser;
    }

    /**
     * Sets update user.
     *
     * @param updateUser the update user
     */
    public void setUpdateUser(final String updateUser) {
        this.updateUser = updateUser;
    }

    /**
     * Gets unit no.
     *
     * @return the unit no
     */
    public String getUnitNo() {
        return unitNo;
    }

    /**
     * Sets unit no.
     *
     * @param unitNo the unit no
     */
    public void setUnitNo(final String unitNo) {
        this.unitNo = unitNo;
    }

    /**
     * Gets part no.
     *
     * @return the part no
     */
    public Long getPartNo() {
        return partNo;
    }

    /**
     * Sets part no.
     *
     * @param partNo the part no
     */
    public void setPartNo(final Long partNo) {
        this.partNo = partNo;
    }

    /**
     * Gets data position.
     *
     * @return the data position
     */
    public String getDataPosition() {
        return dataPosition;
    }

    /**
     * Sets data position.
     *
     * @param dataPosition the data position
     */
    public void setDataPosition(final String dataPosition) {
        this.dataPosition = dataPosition;
    }

    /**
     * Gets biz date.
     *
     * @return the biz date
     */
    public LocalDate getBizDate() {
        return bizDate;
    }

    /**
     * Sets biz date.
     *
     * @param bizDate the biz date
     */
    public void setBizDate(final LocalDate bizDate) {
        this.bizDate = bizDate;
    }

    /**
     * Gets gid.
     *
     * @return the gid
     */
    public String getGid() {
        return gid;
    }

    /**
     * Sets gid.
     *
     * @param gid the gid
     */
    public void setGid(String gid) {
        this.gid = gid;
    }


    /**
     * Gets path.
     *
     * @return the path
     */
    public String getPath() {
        return path;
    }

    /**
     * Sets path.
     *
     * @param path the path
     */
    public void setPath(String path) {
        this.path = path;
    }

    /**
     * Gets cust recog flag.
     *
     * @return the cust recog flag
     */
    public CustRecogFlag getCustRecogFlag() {
        return custRecogFlag;
    }

    /**
     * Sets cust recog flag.
     *
     * @param custRecogFlag the cust recog flag
     */
    public void setCustRecogFlag(CustRecogFlag custRecogFlag) {
        this.custRecogFlag = custRecogFlag;
    }

    /**
     * Gets cust recog no.
     *
     * @return the cust recog no
     */
    public String getCustRecogNo() {
        return custRecogNo;
    }

    /**
     * Sets cust recog no.
     *
     * @param custRecogNo the cust recog no
     */
    public void setCustRecogNo(String custRecogNo) {
        this.custRecogNo = custRecogNo;
    }

    /**
     * Gets crcd cardholder no.
     *
     * @return the crcd cardholder no
     */
    public String getCrcdCardholderNo() {
        return crcdCardholderNo;
    }

    /**
     * Sets crcd cardholder no.
     *
     * @param crcdCardholderNo the crcd cardholder no
     */
    public void setCrcdCardholderNo(String crcdCardholderNo) {
        this.crcdCardholderNo = crcdCardholderNo;
    }

    /**
     * Gets context ginfo.
     *
     * @return the context ginfo
     */
    public static GInfo getContextGinfo() {
        return Context.getGInfo();
    }

    /**
     * Gets msg id.
     *
     * @return the msg id
     */
    public String getMsgId() {
        return msgId;
    }

    /**
     * Sets msg id.
     *
     * @param msgId the msg id
     */
    public void setMsgId(final String msgId) {
        this.msgId = msgId;
    }

    /**
     * 通过Header 赋值.
     *
     * @param request the request
     */
    public void toHandlerAssign(final Request<RequestHeader> request) {
        if (request == null) {
            return;
        }
        RequestHeader header = request.getHeader();
        header.setGid(getAnother(header.getGid(), gid));
        header.setCustRecogNo(getAnother(header.getCustRecogNo(), custRecogNo));
        header.setCustRecogFlag(getAnother(header.getCustRecogFlag(), custRecogFlag));
        header.setCrcdCardholderNo(getAnother(header.getCrcdCardholderNo(), crcdCardholderNo));
        header.setPath(getAnother(header.getPath(), path));
        header.setBizDate(getAnother(header.getBizDate(), bizDate));
        header.setUnitNo(getAnother(header.getUnitNo(), unitNo));
        header.setMsgId(getAnother(header.getMsgId(), msgId));
        if (request instanceof RequestDTO requestDTO) {
            requestDTO.setCreateUser(getAnother(requestDTO.getCreateUser(), createUser));
            requestDTO.setUpdateUser(getAnother(requestDTO.getUpdateUser(), updateUser));
        }
        header.setCrcdOrgNo(getAnother(header.getCrcdOrgNo(), crcdOrgNo));
        header.setSrcSystem(getAnother(header.getSrcSystem(), srcSystem));
        header.setPartNo(getAnother(header.getPartNo(), partNo));
        header.setDataPosition(getAnother(header.getDataPosition(), dataPosition));
        header.setFaultUnits(getAnother(header.getFaultUnits(), faultUnits));
    }
}
