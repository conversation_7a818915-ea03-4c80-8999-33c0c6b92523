package com.collect.enums;

import com.anytxn.base.lang.BaseEnum;

import java.util.Arrays;

/**
 * 表达式执行类型.
 *
 * <AUTHOR>
 * @date 2024 /12/19 11:32
 */
public enum RuleExecuteType implements BaseEnum {
    /**
     * 判断型.
     */
    JUDGE(0, "判断型"),

    /**
     * 计算型.
     */
    OPERATOR(1, "计算型"),

    /**
     * 结果型.
     */
    RESULT(2, "结果型");

    private final Integer code;

    private final String desc;

    RuleExecuteType(final Integer code, final String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * To rule execute type [ ].
     *
     * @param value the value
     * @return the rule execute type [ ]
     */
    public static RuleExecuteType[] to(Integer value) {
        return Arrays.stream(RuleExecuteType.values())
                .filter(r -> (r.getCode() & value) == r.getCode())
                .toArray(RuleExecuteType[]::new);
    }

    /**
     * From integer.
     *
     * @param types the types
     * @return the integer
     */
    public static Integer from(RuleExecuteType[] types) {
        int localCode = types[0].getCode();
        for (int i = 1; i < types.length; i++) {
            localCode |= types[i].getCode();
        }
        return localCode;
    }

    /**
     * Gets rule execute type.
     *
     * @param code the code
     * @return the rule execute type
     */
    public static RuleExecuteType getRuleExecuteType(Integer code) {
        for (RuleExecuteType ruleExecuteType : RuleExecuteType.values()) {
            if (ruleExecuteType.getCode().equals(code)) {
                return ruleExecuteType;
            }
        }
        return null;
    }

    /**
     * Gets code.
     *
     * @return the code
     */
    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * Gets desc.
     *
     * @return the desc
     */
    @Override
    public String getDesc() {
        return desc;
    }
}
