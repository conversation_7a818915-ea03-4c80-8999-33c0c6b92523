package com.collect.enums;

import com.anytxn.base.lang.BaseEnum;

/**
 * 规则因子值类型.
 *
 * <AUTHOR>
 * @date 2024 /12/19 11:32
 */
public enum RuleValueType implements BaseEnum {
    /**
     * 字典型.
     */
    DIC(0, "字典型"),
    /**
     * 整數型.
     */
    INT_X(1, "整數型"),
    /**
     * 浮點型.
     */
    DOUBLE_X(2, "浮點型"),
    /**
     * 金額型.
     */
    AMOUNT(3, "金額型"),
    /**
     * 字符型.
     */
    CHARACTER_X(4, "字符型"),
    /**
     * 參數型.
     */
    PARAMETER(5, "參數型"),
    /**
     * 布爾型.
     */
    BOOLEAN_X(6, "布尔型"),

    /**
     * 数组.
     */
    ARRAY(7, "数组"),

    /**
     * 长整型.
     */
    LONG_X(8, "长整型"),

    ;

    private final Integer code;

    private final String desc;

    RuleValueType(final Integer code, final String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * Gets rule value type.
     *
     * @param code the code
     * @return the rule value type
     */
    public static RuleValueType getRuleValueType(Integer code) {
        for (RuleValueType ruleValueType : RuleValueType.values()) {
            if (ruleValueType.getCode().equals(code)) {
                return ruleValueType;
            }
        }
        return null;
    }

    /**
     * Gets code.
     *
     * @return the code
     */
    @Override
    public Integer getCode() {
        return code;
    }

    /**
     * Gets desc.
     *
     * @return the desc
     */
    @Override
    public String getDesc() {
        return desc;
    }
}
