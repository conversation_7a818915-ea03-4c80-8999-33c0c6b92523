package com.collect.mapper;

import com.collect.entity.NodeProgramConfigInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 节点程序配置信息Mapper接口
 */
@Mapper
public interface NodeProgramConfigInfoMapper {
    
    /**
     * 根据节点代码查询节点程序配置信息
     * @param nodeCode 节点代码
     * @return 节点程序配置信息
     */
    NodeProgramConfigInfoPO selectByNodeCode(@Param("nodeCode") String nodeCode);
    
    /**
     * 查询所有节点程序配置信息
     * @return 节点程序配置信息列表
     */
    List<NodeProgramConfigInfoPO> selectAll();
    
    /**
     * 根据节点调用名称查询节点程序配置信息
     * @param nodeCallName 节点调用名称
     * @return 节点程序配置信息列表
     */
    List<NodeProgramConfigInfoPO> selectByNodeCallName(@Param("nodeCallName") String nodeCallName);
    
    /**
     * 根据节点代码名称查询节点程序配置信息
     * @param nodeAttriId 节点代码名称
     * @return 节点程序配置信息列表
     */
    List<NodeProgramConfigInfoPO> selectByNodeAttriId(@Param("nodeAttriId") String nodeAttriId);
    
    /**
     * 插入节点程序配置信息
     * @param nodeProgramConfigInfoPO 节点程序配置信息
     * @return 影响行数
     */
    int insert(NodeProgramConfigInfoPO nodeProgramConfigInfoPO);
    
    /**
     * 更新节点程序配置信息
     * @param nodeProgramConfigInfoPO 节点程序配置信息
     * @return 影响行数
     */
    int update(NodeProgramConfigInfoPO nodeProgramConfigInfoPO);
    
    /**
     * 根据节点代码删除节点程序配置信息
     * @param nodeCode 节点代码
     * @return 影响行数
     */
    int deleteByNodeCode(@Param("nodeCode") String nodeCode);

    
    /**
     * 根据条件查询节点程序配置信息
     * @param condition 查询条件
     * @return 查询结果列表
     */
    List<NodeProgramConfigInfoPO> selectByCondition(NodeProgramConfigInfoPO condition);
} 