package com.collect.mapper;
import com.collect.entity.WebRuleVariablePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * The interface Web rule variable mapper.
 *
 * <AUTHOR>
 * @date 2024 /12/17 10:35
 */
@Mapper
public interface WebRuleVariableMapper {

    /**
     * Query all list.
     *
     * @param ruleVariable the rule variable
     * @return the list
     */
    List<WebRuleVariablePO> queryAll(@Param("item") WebRuleVariablePO ruleVariable);

    /**
     * Insert int.
     *
     * @param ruleVariable the rule variable
     * @return the int
     */
    int insert(WebRuleVariablePO ruleVariable);

    /**
     * Update int.
     *
     * @param ruleVariable the rule variable
     * @return the int
     */
    int update(WebRuleVariablePO ruleVariable);
}
