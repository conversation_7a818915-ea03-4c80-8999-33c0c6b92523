package com.collect.mapper;

import com.collect.entity.NodeInfoDO;
import com.collect.entity.NodeInfoPO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface NodeInfoMapper {

    int insert(NodeInfoPO nodeInfoPO);


    int update(NodeInfoPO nodeInfoPO);


    int deleteById(Integer id);

    int deleteByNodeCode(String nodeCode);

    NodeInfoPO selectById(Integer id);

    NodeInfoPO selectByNodeCode(String nodeCode);

    List<NodeInfoPO> selectAll();

    List<NodeInfoDO> selectAllWithConfigs(NodeInfoPO nodeInfoPO);

    NodeInfoDO selectAllWithConfigsByNodeCode(@Param("nodeCode")String nodeCode);
}
