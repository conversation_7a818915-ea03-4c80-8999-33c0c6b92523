package com.collect.mapper;

import com.collect.entity.ProcessingProgramManagement;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 处理程序管理Mapper接口
 */
@Mapper
public interface ProcessingProgramManagementMapper {
    
    /**
     * 根据节点属性ID查询处理程序
     * @param nodeAttriId 节点属性ID
     * @return 处理程序信息
     */
    ProcessingProgramManagement selectById(@Param("nodeAttriId") String nodeAttriId);
    
    /**
     * 查询所有处理程序
     * @return 处理程序信息列表
     */
    List<ProcessingProgramManagement> selectAll();
    
    /**
     * 根据节点属性类型查询处理程序
     * @param nodeAttriType 节点属性类型
     * @return 处理程序信息列表
     */
    List<ProcessingProgramManagement> selectByType(@Param("nodeAttriType") String nodeAttriType);
    
    /**
     * 插入处理程序信息
     * @param processingProgram 处理程序信息
     * @return 影响行数
     */
    int insert(ProcessingProgramManagement processingProgram);
    
    /**
     * 更新处理程序信息
     * @param processingProgram 处理程序信息
     * @return 影响行数
     */
    int update(ProcessingProgramManagement processingProgram);
    
    /**
     * 根据节点属性ID删除处理程序信息
     * @param nodeAttriId 节点属性ID
     * @return 影响行数
     */
    int deleteById(@Param("nodeAttriId") String nodeAttriId);
    
    /**
     * 根据条件查询处理程序信息
     * @param condition 查询条件
     * @return 查询结果列表
     */
    List<ProcessingProgramManagement> selectByCondition(ProcessingProgramManagement condition);
} 