package com.collect.mapper;

import com.collect.entity.TagInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 标签信息Mapper接口
 */
@Mapper
public interface TagInfoMapper {
    
    /**
     * 根据标签ID查询标签信息
     * @param tagId 标签ID
     * @return 标签信息
     */
    TagInfo selectById(@Param("tagId") String tagId);
    
    /**
     * 查询所有标签信息
     * @return 标签信息列表
     */
    List<TagInfo> selectAll();
    
    /**
     * 根据标签类型查询标签信息
     * @param tagType 标签类型
     * @return 标签信息列表
     */
    List<TagInfo> selectByType(@Param("tagType") String tagType);
    
    /**
     * 插入标签信息
     * @param tagInfo 标签信息
     * @return 影响行数
     */
    int insert(TagInfo tagInfo);
    
    /**
     * 更新标签信息
     * @param tagInfo 标签信息
     * @return 影响行数
     */
    int update(TagInfo tagInfo);
    
    /**
     * 根据标签ID删除标签信息
     * @param tagId 标签ID
     * @return 影响行数
     */
    int deleteById(@Param("tagId") String tagId);
    
    /**
     * 根据条件查询标签信息
     * @param condition 查询条件
     * @return 查询结果列表
     */
    List<TagInfo> selectByCondition(TagInfo condition);
} 