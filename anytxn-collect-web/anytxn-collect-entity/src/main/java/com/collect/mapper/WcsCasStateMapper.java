package com.collect.mapper;

import com.collect.entity.WcsCasState;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 案件状态信息Mapper接口
 */
@Mapper
public interface WcsCasStateMapper {
    
    /**
     * 插入案件状态信息
     * @param wcsCasState 案件状态信息
     * @return 影响行数
     */
    int insert(WcsCasState wcsCasState);
    
    /**
     * 更新案件状态信息
     * @param wcsCasState 案件状态信息
     * @return 影响行数
     */
    int update(WcsCasState wcsCasState);
    
    /**
     * 根据ID删除案件状态信息
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据案件号删除案件状态信息
     * @param caseCode 案件号
     * @return 影响行数
     */
    int deleteByCaseCode(@Param("caseCode") String caseCode);
    
    /**
     * 根据条件查询案件状态信息
     * @param condition 查询条件
     * @return 查询结果列表
     */
    List<WcsCasState> selectByCondition(WcsCasState condition);
} 