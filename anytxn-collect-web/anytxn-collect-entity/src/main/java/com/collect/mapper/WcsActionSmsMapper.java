package com.collect.mapper;

import com.collect.entity.WcsActionSmsPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 20:27
 **/

@Mapper
public interface WcsActionSmsMapper {


    int insertWcsActionSms(WcsActionSmsPO wcsActionSmsPO);

    int deleteWcsActionSmsById(Long id);

    WcsActionSmsPO selectWcsActionSmsById(Long id);

    List<WcsActionSmsPO> selectWcsActionSmsByCaseCode(@Param("caseCode")String caseCode);

    int updateWcsActionSms(WcsActionSmsPO wcsActionSmsPO);
}
