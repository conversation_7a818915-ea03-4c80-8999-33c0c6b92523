package com.collect.mapper;

import com.collect.entity.TagInfo;
import com.collect.entity.TaskNodeInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 任务节点信息Mapper接口
 */
@Mapper
public interface TaskNodeInfoMapper {
    
    /**
     * 根据条件查询任务节点信息
     * @param condition 查询条件
     * @return 查询结果列表
     */
    List<TaskNodeInfo> selectByCondition(TaskNodeInfo condition);
    /**
     * 插入标签信息
     * @param condition 标签信息
     * @return 影响行数
     */
    int insert(TaskNodeInfo condition);

} 