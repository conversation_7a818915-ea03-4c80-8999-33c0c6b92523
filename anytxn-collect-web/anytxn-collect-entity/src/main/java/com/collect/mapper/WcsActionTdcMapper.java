package com.collect.mapper;

import com.collect.entity.WcsActionTdcPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 17:14
 **/
@Mapper
public interface WcsActionTdcMapper {

    List<WcsActionTdcPO> selectAll();

    WcsActionTdcPO selectById(int id);

    List<WcsActionTdcPO> selectByCaseCode(@Param("caseCode")String caseCode);

    int insert(WcsActionTdcPO wcsActionTdc);

    int update(WcsActionTdcPO wcsActionTdc);

    int deleteById(int id);
}
