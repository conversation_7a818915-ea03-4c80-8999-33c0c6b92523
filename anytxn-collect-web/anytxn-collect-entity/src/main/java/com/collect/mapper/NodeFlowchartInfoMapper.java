package com.collect.mapper;

import com.collect.entity.NodeFlowchartInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 节点流程图信息Mapper接口
 */
@Mapper
public interface NodeFlowchartInfoMapper {
    

    /**
     * 插入节点流程图信息
     * @param nodeFlowchartInfo 节点流程图信息
     * @return 影响行数
     */
    int insert(NodeFlowchartInfo nodeFlowchartInfo);
    
    /**
     * 更新节点流程图信息
     * @param nodeFlowchartInfo 节点流程图信息
     * @return 影响行数
     */
    int update(NodeFlowchartInfo nodeFlowchartInfo);
    
    /**
     * 根据ID删除节点流程图信息
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据流程图ID删除节点流程图信息
     * @param flowchartId 流程图ID
     * @return 影响行数
     */
    int deleteByFlowchartId(@Param("flowchartId") String flowchartId);
    
    /**
     * 根据条件查询流程图信息
     * @param condition 查询条件
     * @return 查询结果列表
     */
    List<NodeFlowchartInfo> selectByCondition(NodeFlowchartInfo condition);
} 