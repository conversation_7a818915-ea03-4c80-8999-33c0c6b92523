package com.collect.mapper;


import com.collect.entity.BaseRuleInfoParamPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface BaseRuleInfoParamMapper {
    List<BaseRuleInfoParamPO> selectAll(BaseRuleInfoParamPO baseRuleInfoParamPO);

    void insert(BaseRuleInfoParamPO baseRuleInfoParamPO);

    void update(BaseRuleInfoParamPO baseRuleInfoParamPO);

    void deleteById(Long incId);
}
