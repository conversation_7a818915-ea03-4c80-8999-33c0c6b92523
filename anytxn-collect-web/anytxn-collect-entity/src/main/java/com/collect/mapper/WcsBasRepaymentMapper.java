package com.collect.mapper;


import com.collect.entity.WcsBasRepaymentPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WcsBasRepaymentMapper {
    List<WcsBasRepaymentPO> selectAll();
    List<WcsBasRepaymentPO> selectByCaseCode(@Param("caseCode")String caseCode);

    int insertSelective(WcsBasRepaymentPO record);

    int updateByPrimaryKeySelective(WcsBasRepaymentPO record);

    int deleteByPrimaryKey(Long id);
}
