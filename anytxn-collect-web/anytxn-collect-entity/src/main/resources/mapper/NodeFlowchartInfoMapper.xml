<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.NodeFlowchartInfoMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.NodeFlowchartInfo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="flowchart_id" property="flowchartId" jdbcType="VARCHAR"/>
        <result column="flowchart_name" property="flowchartName" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="flowchart_text" property="flowchartText" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        id, flowchart_id, flowchart_name, description, flowchart_text, create_user, update_user, create_time, update_time, version
    </sql>


    <!-- 通用条件查询流程图信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.NodeFlowchartInfo" resultType="com.collect.entity.NodeFlowchartInfo">
        SELECT 
            id,
            flowchart_id AS flowchartId,
            flowchart_name AS flowchartName,
            description,
            create_time AS createTime,
            create_user AS createUser,
            update_time AS updateTime,
            update_user AS updateUser,
            version
        FROM node_flowchart_info
        WHERE 1=1
        <if test="id != null">
            AND id = #{id}
        </if>
        <if test="flowchartId != null and flowchartId != ''">
            AND flowchart_id = #{flowchartId}
        </if>
        <if test="flowchartName != null and flowchartName != ''">
            AND flowchart_name LIKE CONCAT('%', #{flowchartName}, '%')
        </if>
        <if test="description != null and description != ''">
            AND description = #{description}
        </if>
        <if test="createUser != null and createUser != ''">
            AND create_user = #{createUser}
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND update_user = #{updateUser}
        </if>
        <if test="version != null">
            AND version = #{version}
        </if>
    </select>

    <!-- 插入节点流程图信息 -->
    <insert id="insert" parameterType="com.collect.entity.NodeFlowchartInfo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO node_flowchart_info (
            flowchart_id, flowchart_name, description, flowchart_text, create_user, update_user, version
        ) VALUES (
            #{flowchartId}, #{flowchartName}, #{description}, #{flowchartText}, #{createUser}, #{updateUser}, #{version}
        )
    </insert>

    <!-- 更新节点流程图信息 -->
    <update id="update" parameterType="com.collect.entity.NodeFlowchartInfo">
        UPDATE node_flowchart_info
        <set>
            <if test="flowchartName != null">flowchart_name = #{flowchartName},</if>
            <if test="description != null">description = #{description},</if>
            <if test="flowchartText != null">flowchart_text = #{flowchartText},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="version != null">version = #{version} + 1,</if>
        </set>
        WHERE id = #{id} AND version = #{version}
    </update>

    <!-- 根据ID删除节点流程图信息 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM node_flowchart_info WHERE id = #{id}
    </delete>

    <!-- 根据流程图ID删除节点流程图信息 -->
    <delete id="deleteByFlowchartId" parameterType="java.lang.String">
        DELETE FROM node_flowchart_info WHERE flowchart_id = #{flowchartId}
    </delete>
</mapper> 