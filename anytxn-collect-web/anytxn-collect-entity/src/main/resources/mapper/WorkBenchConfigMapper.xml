<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.WorkBenchConfigMapper">
    <resultMap id="BaseResultMap" type="com.collect.entity.WorkBenchConfigPO">
        <id column="id" property="id"/>
        <result column="workbench_name" property="workBenchName"/>
        <result column="auxiliary_functions" property="auxiliaryFunctions"/>
        <result column="workbench_model" property="workBenchModel"/>
        <result column="call_fields" property="callFields"/>
        <result column="custom_fields" property="customFields"/>
        <result column="contact_field" property="contactField"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, workbench_name, auxiliary_functions, workbench_model, call_fields, custom_fields, 
        contact_field, update_user, update_time, create_user, create_time
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM work_bench_config
        WHERE 1 = 1
        <if test="workBenchName != null">
            AND workbench_name = #{workBenchName}
        </if>
    </select>

    <insert id="insertSelective" parameterType="com.collect.entity.WorkBenchConfigPO">
        INSERT INTO work_bench_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="workBenchName != null">workbench_name,</if>
            <if test="auxiliaryFunctions != null">auxiliary_functions,</if>
            <if test="workBenchModel != null">workbench_model,</if>
            <if test="callFields != null">call_fields,</if>
            <if test="customFields != null">custom_fields,</if>
            <if test="contactField != null">contact_field,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="createUser != null">create_user,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="workBenchName != null">#{workBenchName},</if>
            <if test="auxiliaryFunctions != null">#{auxiliaryFunctions},</if>
            <if test="workBenchModel != null">#{workBenchModel},</if>
            <if test="callFields != null">#{callFields},</if>
            <if test="customFields != null">#{customFields},</if>
            <if test="contactField != null">#{contactField},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="createUser != null">#{createUser},</if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.collect.entity.WorkBenchConfigPO">
        UPDATE work_bench_config
        <set>
            <if test="auxiliaryFunctions != null">auxiliary_functions = #{auxiliaryFunctions},</if>
            <if test="workBenchModel != null">workbench_model = #{workBenchModel},</if>
            <if test="callFields != null">call_fields = #{callFields},</if>
            <if test="customFields != null">custom_fields = #{customFields},</if>
            <if test="contactField != null">contact_field = #{contactField},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
        </set>
        WHERE workbench_name = #{workBenchName}
    </update>
</mapper>