<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.NodeProgramConfigInfoMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.NodeProgramConfigInfoPO">
        <id column="node_code" property="nodeCode" jdbcType="VARCHAR"/>
        <result column="node_call_name" property="nodeCallName" jdbcType="VARCHAR"/>
        <result column="node_attri_id" property="nodeAttriId" jdbcType="VARCHAR"/>
        <result column="node_priority" property="nodePriority" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        node_code, node_call_name, node_attri_id, node_priority
    </sql>

    <!-- 查询所有节点程序配置信息 -->
    <select id="selectAll" resultType="com.collect.entity.NodeProgramConfigInfoPO">
        SELECT 
            node_code AS nodeCode,
            node_call_name AS nodeCallName,
            node_attri_id AS nodeAttriId,
            node_priority AS nodePriority
        FROM node_program_config_info
    </select>

    <!-- 根据节点代码查询节点程序配置信息 -->
    <select id="selectByNodeCode" parameterType="java.lang.String" resultType="com.collect.entity.NodeProgramConfigInfoPO">
        SELECT 
            node_code AS nodeCode,
            node_call_name AS nodeCallName,
            node_attri_id AS nodeAttriId,
            node_priority AS nodePriority
        FROM node_program_config_info
        WHERE node_code = #{nodeCode}
    </select>

    <!-- 根据节点调用名称查询节点程序配置信息 -->
    <select id="selectByNodeCallName" parameterType="java.lang.String" resultType="com.collect.entity.NodeProgramConfigInfoPO">
        SELECT 
            node_code AS nodeCode,
            node_call_name AS nodeCallName,
            node_attri_id AS nodeAttriId,
            node_priority AS nodePriority
        FROM node_program_config_info
        WHERE node_call_name = #{nodeCallName}
    </select>

    <!-- 根据节点代码名称查询节点程序配置信息 -->
    <select id="selectByNodeAttriId" parameterType="java.lang.String" resultType="com.collect.entity.NodeProgramConfigInfoPO">
        SELECT 
            node_code AS nodeCode,
            node_call_name AS nodeCallName,
            node_attri_id AS nodeAttriId,
            node_priority AS nodePriority
        FROM node_program_config_info
        WHERE node_attri_id = #{nodeAttriId}
    </select>

    <!-- 插入节点程序配置信息 -->
    <insert id="insert" parameterType="com.collect.entity.NodeProgramConfigInfoPO">
        INSERT INTO node_program_config_info (
            node_code, node_call_name, node_attri_id, node_priority
        ) VALUES (
            #{nodeCode}, #{nodeCallName}, #{nodeAttriId}, #{nodePriority}
        )
    </insert>

    <!-- 更新节点程序配置信息 -->
    <update id="update" parameterType="com.collect.entity.NodeProgramConfigInfoPO">
        UPDATE node_program_config_info
        <set>
            <if test="nodeCallName != null">node_call_name = #{nodeCallName},</if>
            <if test="nodePriority != null">node_priority = #{nodePriority},</if>
        </set>
        WHERE node_attri_id = #{nodeAttriId}
    </update>

    <!-- 根据节点代码删除节点程序配置信息 -->
    <delete id="deleteByNodeCode" parameterType="java.lang.String">
        DELETE FROM node_program_config_info WHERE node_code = #{nodeCode}
    </delete>

    <!-- 通用条件查询节点程序配置信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.NodeProgramConfigInfoPO" resultType="com.collect.entity.NodeProgramConfigInfoPO">
        SELECT 
            node_code AS nodeCode,
            node_call_name AS nodeCallName,
            node_attri_id AS nodeAttriId,
            node_priority AS nodePriority
        FROM node_program_config_info
        WHERE 1=1
        <if test="nodeCode != null and nodeCode != ''">
            AND node_code = #{nodeCode}
        </if>
        <if test="nodeCallName != null and nodeCallName != ''">
            AND node_call_name = #{nodeCallName}
        </if>
        <if test="nodeAttriId != null and nodeAttriId != ''">
            AND node_attri_id = #{nodeAttriId}
        </if>
        <if test="nodePriority != null">
            AND node_priority = #{nodePriority}
        </if>
    </select>
</mapper> 