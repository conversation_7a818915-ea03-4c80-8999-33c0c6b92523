<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.collect.mapper.NodeInfoMapper">

    <resultMap id="BaseResultMap" type="com.collect.entity.NodeInfoPO">
        <id column="id" property="id" />
        <result column="node_code" property="nodeCode" />
        <result column="node_call_name" property="nodeCallName" />
        <result column="node_func_desc" property="nodeFuncDesc" />
        <result column="node_type" property="nodeType" />
        <result column="node_status" property="nodeStatus" />
        <result column="node_auth" property="nodeAuth" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" javaType="java.time.LocalDateTime" />
        <result column="aduit_user" property="aduitUser" />
        <result column="aduit_time" property="aduitTime" javaType="java.time.LocalDateTime" />
        <result column="create_time" property="createTime" javaType="java.time.LocalDateTime" />
        <result column="dispatch_manage_list" property="dispatchManageList" />
    </resultMap>

    <resultMap id="NodeInfoWithConfigsResultMap" type="com.collect.entity.NodeInfoDO">
        <id column="node_code" property="nodeCode" jdbcType="VARCHAR"/>
        <result column="id" property="id" />
        <result column="node_call_name" property="nodeCallName" />
        <result column="node_func_desc" property="nodeFuncDesc" />
        <result column="node_type" property="nodeType" />
        <result column="node_status" property="nodeStatus" />
        <result column="node_auth" property="nodeAuth" />
        <result column="update_user" property="updateUser" />
        <result column="update_time" property="updateTime" javaType="java.time.LocalDateTime" />
        <result column="aduit_user" property="aduitUser" />
        <result column="aduit_time" property="aduitTime" javaType="java.time.LocalDateTime" />
        <result column="create_time" property="createTime" javaType="java.time.LocalDateTime" />
        <result column="dispatch_manage_list" property="dispatchManageList" />
        <collection property="nodeManageList" ofType="com.collect.entity.NodeProgramConfigInfoPO"  javaType="java.util.ArrayList">
            <result column="npci_id" property="id" />
            <result column="node_code" property="nodeCode" jdbcType="VARCHAR"/>
            <result column="node_call_name" property="nodeCallName" jdbcType="VARCHAR"/>
            <result column="node_attri_id" property="nodeAttriId" jdbcType="VARCHAR"/>
            <result column="node_priority" property="nodePriority" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <select id="selectAllWithConfigs" resultMap="NodeInfoWithConfigsResultMap">
        SELECT ni.node_code AS node_code,
               ni.id AS id,
               ni.node_call_name AS node_call_name,
               ni.node_func_desc AS node_func_desc,
               ni.node_type AS node_type,
               ni.node_status AS node_status,
               ni.node_auth AS node_auth,
               ni.update_user AS update_user,
               ni.update_time AS update_time,
               ni.aduit_user AS aduit_user,
               ni.aduit_time AS aduit_time,
               ni.create_time AS create_time,
               ni.dispatch_manage_list AS dispatch_manage_list,
               npci.id AS npci_id,
               npci.node_code AS node_code,
               npci.node_call_name AS node_call_name,
               npci.node_attri_id AS node_attri_id,
               npci.node_priority AS node_priority
        FROM node_info ni LEFT JOIN node_program_config_info npci ON ni.node_code = npci.node_code
        <where>
            <if test="nodeCode != null and nodeCode != ''">
                AND ni.node_code = #{nodeCode}
            </if>
            <if test="nodeCallName != null and nodeCallName != ''">
                AND ni.node_call_name LIKE CONCAT('%', #{nodeCallName}, '%')
            </if>
            <if test="nodeType != null and nodeType != ''">
                AND ni.node_type = #{nodeType}
            </if>
            <if test="nodeStatus != null and nodeStatus != ''">
                AND ni.node_status = #{nodeStatus}
            </if>
        </where>
    </select>

    <select id="selectAllWithConfigsByNodeCode" resultMap="NodeInfoWithConfigsResultMap">
        SELECT ni.node_code AS node_code,
               ni.node_call_name AS node_call_name,
               ni.node_func_desc AS node_func_desc,
               ni.node_type AS node_type,
               ni.node_status AS node_status,
               ni.node_auth AS node_auth,
               ni.update_user AS update_user,
               ni.update_time AS update_time,
               ni.aduit_user AS aduit_user,
               ni.aduit_time AS aduit_time,
               ni.create_time AS create_time,
               ni.dispatch_manage_list AS dispatch_manage_list,
               npci.node_code AS node_code,
               npci.node_call_name AS node_call_name,
               npci.node_attri_id AS node_attri_id,
               npci.node_priority AS node_priority
        FROM node_info ni LEFT JOIN node_program_config_info npci
            ON ni.node_code = npci.node_code
        WHERE ni.node_code = #{nodeCode}
    </select>


    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO node_info (node_code, node_call_name, node_func_desc, node_type,
                               node_status, node_auth, update_user, update_time,
                               aduit_user, aduit_time, create_time, dispatch_manage_list)
        VALUES (#{nodeCode}, #{nodeCallName}, #{nodeFuncDesc},
                #{nodeType}, #{nodeStatus}, #{nodeAuth}, #{updateUser},
                #{updateTime}, #{aduitUser}, #{aduitTime}, #{createTime}, #{dispatchManageList})
    </insert>

    <update id="update">
        UPDATE node_info
        <set>
            <if test="nodeCode != null">node_code = #{nodeCode},</if>
            <if test="nodeCallName != null">node_call_name = #{nodeCallName},</if>
            <if test="nodeFuncDesc != null">node_func_desc = #{nodeFuncDesc},</if>
            <if test="nodeType != null">node_type = #{nodeType},</if>
            <if test="nodeStatus != null">node_status = #{nodeStatus},</if>
            <if test="nodeAuth != null">node_auth = #{nodeAuth},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="aduitUser != null">aduit_user = #{aduitUser},</if>
            <if test="aduitTime != null">aduit_time = #{aduitTime},</if>
            <if test="createTime != null">create_time = #{createTime}</if>
            <if test="dispatchManageList != null">dispatch_manage_list = #{dispatchManageList}</if>
        </set>
        WHERE node_code = #{nodeCode}
    </update>

    <delete id="deleteById">
        DELETE FROM node_info WHERE id = #{id}
    </delete>

    <delete id="deleteByNodeCode">
        DELETE FROM node_info WHERE node_code = #{nodeCode}
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT * FROM node_info WHERE id = #{id}
    </select>

    <select id="selectByNodeCode" resultMap="BaseResultMap">
        SELECT * FROM node_info WHERE node_code = #{nodeCode}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT * FROM node_info
    </select>

</mapper>