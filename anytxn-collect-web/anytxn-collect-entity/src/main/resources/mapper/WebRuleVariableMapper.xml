<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.WebRuleVariableMapper">
    <resultMap id="BaseResultMap" type="com.collect.entity.WebRuleVariablePO">
        <id column="INC_ID" property="incId"/>
        <result column="NAME" property="name"/>
        <result column="KEY" property="key"/>
        <result column="RULE_TYPE" property="ruleType"/>
        <result column="RULE_EXECUTE_TYPE" property="ruleExecuteType"/>
        <result column="VALUE_TYPE" property="valueType"/>
        <result column="DIGITS" property="digits"/>
        <result column="MIN_LENGTH" property="minLength"/>
        <result column="MAX_LENGTH" property="maxLength"/>
        <result column="CREATE_USER" property="createUser"/>
        <result column="UPDATE_USER" property="updateUser"/>
        <result column="CREATE_TIME" property="createTime"/>
        <result column="UPDATE_TIME" property="updateTime"/>
    </resultMap>


    <sql id="Base_Column_List">
        INC_ID, NAME, `KEY`, RULE_TYPE, RULE_EXECUTE_TYPE, VALUE_TYPE,DIGITS,MIN_LENGTH,MAX_LENGTH, CREATE_USER, UPDATE_USER,UPDATE_TIME, CREATE_TIME
    </sql>

    <insert id="insert" parameterType="com.collect.entity.WebRuleVariablePO">
        insert into WEB_RULE_VARIABLE( NAME, `KEY`, RULE_TYPE, RULE_EXECUTE_TYPE, VALUE_TYPE, DIGITS, MIN_LENGTH,
                                      MAX_LENGTH, CREATE_USER,
                                      UPDATE_USER, CREATE_TIME, UPDATE_TIME)
        values (#{name},
                #{key},
                #{ruleType},
                #{ruleExecuteType},
                #{valueType},
                #{digits},
                #{minLength},
                #{maxLength},
                #{createUser},
                #{updateUser},
                #{createTime},
                #{updateTime})
    </insert>

    <update id="update">
        update WEB_RULE_VARIABLE
        set NAME=#{name},
            RULE_EXECUTE_TYPE=#{ruleExecuteType},
            VALUE_TYPE=#{valueType},
            DIGITS=#{digits},
            MIN_LENGTH=#{minLength},
            MAX_LENGTH=#{maxLength},
            UPDATE_TIME=#{updateTime},
            UPDATE_USER=#{updateUser}
        where `KEY` = #{key}
    </update>

    <select id="queryAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from WEB_RULE_VARIABLE
        <where>
            <if test="item.name != null and item.name!=''">
                AND NAME like CONCAT(#{item.name},'%')
            </if>
            <if test="item.key != null and item.key!=''">
                AND `KEY` = #{item.key}
            </if>
            <if test="item.ruleType != null and item.ruleType!=''">
                AND RULE_TYPE = #{item.ruleType}
            </if>
        </where>
    </select>

</mapper>