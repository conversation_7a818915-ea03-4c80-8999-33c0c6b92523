<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.TagInfoMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.TagInfo">
        <id column="tag_id" property="tagId" jdbcType="VARCHAR"/>
        <result column="tag_name" property="tagName" jdbcType="VARCHAR"/>
        <result column="tag_type" property="tagType" jdbcType="VARCHAR"/>
        <result column="tag_attribute" property="tagAttribute" jdbcType="VARCHAR"/>
        <result column="tag_description" property="tagDescription" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_timestamp" property="createTimestamp" jdbcType="TIMESTAMP"/>
        <result column="tag_source" property="tagSource" jdbcType="VARCHAR"/>
        <result column="node_attriId" property="nodeAttriId" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        tag_id, tag_name, tag_type, tag_attribute, tag_description, status, updateUser, update_time, create_timestamp,tag_source,node_attriId
    </sql>

    <!-- 查询所有标签信息 -->
    <select id="selectAll" resultType="com.collect.entity.TagInfo">
        SELECT 
            tag_id AS tagId,
            tag_name AS tagName,
            tag_type AS tagType,
            tag_attribute AS tagAttribute,
            tag_description AS tagDescription,
            status,
            create_timestamp AS createTimestamp,
            update_time AS updateTime,
            update_User,tag_source as tagSource,node_attriId as nodeAttriId
        FROM tag_info
    </select>

    <!-- 根据标签ID查询标签信息 -->
    <select id="selectById" parameterType="java.lang.String" resultType="com.collect.entity.TagInfo">
        SELECT 
            tag_id AS tagId,
            tag_name AS tagName,
            tag_type AS tagType,
            tag_attribute AS tagAttribute,
            tag_description AS tagDescription,
            status,
            create_timestamp AS createTimestamp,
            update_time AS updateTime,
            update_User,tag_source as tagSource,node_attriId as nodeAttriId
        FROM tag_info
        WHERE tag_id = #{tagId}
    </select>

    <!-- 根据标签类型查询标签信息 -->
    <select id="selectByType" parameterType="java.lang.String" resultType="com.collect.entity.TagInfo">
        SELECT 
            tag_id AS tagId,
            tag_name AS tagName,
            tag_type AS tagType,
            tag_attribute AS tagAttribute,
            tag_description AS tagDescription,
            status,
            create_timestamp AS createTimestamp,
            update_time AS updateTime,
            update_user,tag_source as tagSource,node_attriId as nodeAttriId
        FROM tag_info
        WHERE tag_type = #{tagType}
    </select>

    <!-- 插入标签信息 -->
    <insert id="insert" parameterType="com.collect.entity.TagInfo">
        INSERT INTO tag_info (
        <!-- 动态拼接列名 -->
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="tagId != null">
                tag_id,
            </if>
            <if test="tagName != null">
                tag_name,
            </if>
            <if test="tagType != null">
                tag_type,
            </if>
            <if test="tagAttribute != null">
                tag_attribute,
            </if>
            <if test="tagDescription != null">
                tag_description,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="true">
                create_timestamp,
            </if>
            <if test="true">
                update_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="tagSource != null">
                tag_Source
            </if>
            <if test="nodeAttriId != null">
                node_attriId
            </if>

        </trim>
        ) VALUES (
        <!-- 动态拼接值 -->
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="tagId != null">
                #{tagId},
            </if>
            <if test="tagName != null">
                #{tagName},
            </if>
            <if test="tagType != null">
                #{tagType},
            </if>
            <if test="tagAttribute != null">
                #{tagAttribute},
            </if>
            <if test="tagDescription != null">
                #{tagDescription},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="true">
                NOW(),
            </if>
            <if test="true">
                NOW(),
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="tagSource != null">
                #{tagSource}
            </if>
            <if test="nodeAttriId != null">
                #{nodeAttriId}
            </if>


        </trim>
        )
    </insert>

    <!-- 更新标签信息 -->
    <update id="update" parameterType="com.collect.entity.TagInfo">
        UPDATE tag_info
        SET 
            tag_name = #{tagName},
            tag_type = #{tagType},
            tag_attribute = #{tagAttribute},
            tag_description = #{tagDescription},
            status = #{status},
            update_time = NOW(),
            update_user = #{updateUser},
            tag_source = #{tagSource},
            node_attriId = #{nodeAttriId}
        WHERE tag_id = #{tagId}
    </update>


    <!-- 根据标签ID删除标签信息 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM tag_info
        WHERE tag_id = #{tagId}
    </delete>

    <!-- 通用条件查询标签信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.TagInfo" resultType="com.collect.entity.TagInfo">
        SELECT
        tag_id AS tagId,
        tag_name AS tagName,
        tag_type AS tagType,
        tag_attribute AS tagAttribute,
        tag_description AS tagDescription,
        status,
        create_timestamp AS createTimestamp,
        update_time AS updateTime,
        update_User,tag_source as tagSource,node_attriId as nodeAttriId
        FROM tag_info
        WHERE 1=1

        <if test="tagId != null and tagId != ''">
            AND tag_id = #{tagId}
        </if>
        <if test="tagName != null and tagName != ''">
            AND tag_name LIKE CONCAT('%', #{tagName}, '%')
        </if>
        <if test="tagType != null and tagType != ''">
            AND tag_type = #{tagType}
        </if>
        <if test="tagSource != null and tagSource != ''">
            AND tag_source = #{tagSource}
        </if>
        <if test="tagDescription != null and tagDescription != ''">
            AND tag_description = #{tagDescription}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>
</mapper> 