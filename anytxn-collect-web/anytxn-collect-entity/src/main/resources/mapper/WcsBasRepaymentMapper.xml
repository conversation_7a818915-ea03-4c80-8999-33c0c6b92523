<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.collect.mapper.WcsBasRepaymentMapper">

    <resultMap id="BaseResultMap" type="com.collect.entity.WcsBasRepaymentPO">
        <id column="id" property="id"/>
        <result column="case_code" property="caseCode"/>
        <result column="card_nbr" property="cardNbr"/>
        <result column="trans_code" property="transCode"/>
        <result column="txn_curr_code" property="txnCurrCode"/>
        <result column="txn_curr_amt" property="txnCurrAmt"/>
        <result column="post_curr" property="postCurr"/>
        <result column="amount" property="amount"/>
        <result column="purchase_dte" property="purchaseDte"/>
        <result column="posting_dte" property="postingDte"/>
        <result column="dba_description" property="dbaDescription"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, case_code, card_nbr, trans_code, txn_curr_code, txn_curr_amt, post_curr, amount, purchase_dte, posting_dte, dba_description, create_time, update_time
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM wcs_bas_repayment
    </select>

    <select id="selectByCaseCode" resultMap="BaseResultMap">
        SELECT * FROM wcs_bas_repayment WHERE case_code = #{caseCode}
    </select>

    <insert id="insertSelective" parameterType="com.collect.entity.WcsBasRepaymentPO">
        INSERT INTO wcs_bas_repayment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseCode != null">
                case_code,
            </if>
            <if test="cardNbr != null">
                card_nbr,
            </if>
            <if test="transCode != null">
                trans_code,
            </if>
            <if test="txnCurrCode != null">
                txn_curr_code,
            </if>
            <if test="txnCurrAmt != null">
                txn_curr_amt,
            </if>
            <if test="postCurr != null">
                post_curr,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="purchaseDte != null">
                purchase_dte,
            </if>
            <if test="postingDte != null">
                posting_dte,
            </if>
            <if test="dbaDescription != null">
                dba_description,
            </if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="caseCode != null">
                #{caseCode,jdbcType=VARCHAR},
            </if>
            <if test="cardNbr != null">
                #{cardNbr,jdbcType=VARCHAR},
            </if>
            <if test="transCode != null">
                #{transCode,jdbcType=VARCHAR},
            </if>
            <if test="txnCurrCode != null">
                #{txnCurrCode,jdbcType=INTEGER},
            </if>
            <if test="txnCurrAmt != null">
                #{txnCurrAmt,jdbcType=DECIMAL},
            </if>
            <if test="postCurr != null">
                #{postCurr,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="purchaseDte != null">
                #{purchaseDte,jdbcType=VARCHAR},
            </if>
            <if test="postingDte != null">
                #{postingDte,jdbcType=VARCHAR},
            </if>
            <if test="dbaDescription != null">
                #{dbaDescription,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.collect.entity.WcsBasRepaymentPO">
        UPDATE wcs_bas_repayment
        <set>
            <if test="caseCode != null">
                case_code = #{caseCode,jdbcType=VARCHAR},
            </if>
            <if test="cardNbr != null">
                card_nbr = #{cardNbr,jdbcType=VARCHAR},
            </if>
            <if test="transCode != null">
                trans_code = #{transCode,jdbcType=VARCHAR},
            </if>
            <if test="txnCurrCode != null">
                txn_curr_code = #{txnCurrCode,jdbcType=INTEGER},
            </if>
            <if test="txnCurrAmt != null">
                txn_curr_amt = #{txnCurrAmt,jdbcType=DECIMAL},
            </if>
            <if test="postCurr != null">
                post_curr = #{postCurr,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="purchaseDte != null">
                purchase_dte = #{purchaseDte,jdbcType=VARCHAR},
            </if>
            <if test="postingDte != null">
                posting_dte = #{postingDte,jdbcType=VARCHAR},
            </if>
            <if test="dbaDescription != null">
                dba_description = #{dbaDescription,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        DELETE FROM wcs_bas_repayment
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

</mapper>
