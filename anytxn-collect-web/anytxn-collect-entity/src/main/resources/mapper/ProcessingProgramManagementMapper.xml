<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.ProcessingProgramManagementMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.ProcessingProgramManagement">
        <id column="node_attri_id" property="nodeAttriId" jdbcType="VARCHAR"/>
        <result column="node_attri_name" property="nodeAttriName" jdbcType="VARCHAR"/>
        <result column="node_attri_desc" property="nodeAttriDesc" jdbcType="VARCHAR"/>
        <result column="node_attri_type" property="nodeAttriType" jdbcType="VARCHAR"/>
        <result column="node_attri_code" property="nodeAttriCode" jdbcType="VARCHAR"/>
        <result column="node_attri_up" property="nodeAttriUp" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="CHAR"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        node_attri_id, node_attri_name, node_attri_desc, node_attri_type, node_attri_code, 
        node_attri_up, status, update_user, update_time, create_time
    </sql>

    <!-- 查询所有处理程序信息 -->
    <select id="selectAll" resultType="com.collect.entity.ProcessingProgramManagement">
        SELECT 
            node_attri_id AS nodeAttriId,
            node_attri_name AS nodeAttriName,
            node_attri_desc AS nodeAttriDesc,
            node_attri_type AS nodeAttriType,
            node_attri_code AS nodeAttriCode,
            node_attri_up AS nodeAttriUp,
            status,
            update_user AS updateUser,
            update_time AS updateTime,
            create_time AS createTime
        FROM processing_program_management
    </select>

    <!-- 根据节点属性ID查询处理程序信息 -->
    <select id="selectById" parameterType="java.lang.String" resultType="com.collect.entity.ProcessingProgramManagement">
        SELECT 
            node_attri_id AS nodeAttriId,
            node_attri_name AS nodeAttriName,
            node_attri_desc AS nodeAttriDesc,
            node_attri_type AS nodeAttriType,
            node_attri_code AS nodeAttriCode,
            node_attri_up AS nodeAttriUp,
            status,
            update_user AS updateUser,
            update_time AS updateTime,
            create_time AS createTime
        FROM processing_program_management
        WHERE node_attri_id = #{nodeAttriId}
    </select>

    <!-- 根据节点属性类型查询处理程序信息 -->
    <select id="selectByType" parameterType="java.lang.String" resultType="com.collect.entity.ProcessingProgramManagement">
        SELECT 
            node_attri_id AS nodeAttriId,
            node_attri_name AS nodeAttriName,
            node_attri_desc AS nodeAttriDesc,
            node_attri_type AS nodeAttriType,
            node_attri_code AS nodeAttriCode,
            node_attri_up AS nodeAttriUp,
            status,
            update_user AS updateUser,
            update_time AS updateTime,
            create_time AS createTime
        FROM processing_program_management
        WHERE node_attri_type = #{nodeAttriType}
    </select>

    <!-- 通用条件查询处理程序信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.ProcessingProgramManagement" resultType="com.collect.entity.ProcessingProgramManagement">
        SELECT 
            node_attri_id AS nodeAttriId,
            node_attri_name AS nodeAttriName,
            node_attri_desc AS nodeAttriDesc,
            node_attri_type AS nodeAttriType,
            node_attri_code AS nodeAttriCode,
            node_attri_up AS nodeAttriUp,
            status,
            update_user AS updateUser,
            update_time AS updateTime,
            create_time AS createTime
        FROM processing_program_management
        WHERE 1=1
        <if test="nodeAttriId != null and nodeAttriId != ''">
            AND node_attri_id = #{nodeAttriId}
        </if>
        <if test="nodeAttriName != null and nodeAttriName != ''">
            AND node_attri_name LIKE CONCAT('%', #{nodeAttriName}, '%')
        </if>
        <if test="nodeAttriType != null and nodeAttriType != ''">
            AND node_attri_type = #{nodeAttriType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="updateUser != null and updateUser != ''">
            AND update_user = #{updateUser}
        </if>
    </select>

    <insert id="insert" parameterType="com.collect.entity.ProcessingProgramManagement">
        INSERT INTO processing_program_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nodeAttriId != null and nodeAttriId != ''">
                node_attri_id,
            </if>
            <if test="nodeAttriName != null and nodeAttriName != ''">
                node_attri_name,
            </if>
            <if test="nodeAttriDesc != null and nodeAttriDesc != ''">
                node_attri_desc,
            </if>
            <if test="nodeAttriType != null and nodeAttriType != ''">
                node_attri_type,
            </if>
            <if test="nodeAttriCode != null and nodeAttriCode != ''">
                node_attri_code,
            </if>
            <if test="nodeAttriUp != null">
                node_attri_up,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nodeAttriId != null and nodeAttriId != ''">
                #{nodeAttriId},
            </if>
            <if test="nodeAttriName != null and nodeAttriName != ''">
                #{nodeAttriName},
            </if>
            <if test="nodeAttriDesc != null and nodeAttriDesc != ''">
                #{nodeAttriDesc},
            </if>
            <if test="nodeAttriType != null and nodeAttriType != ''">
                #{nodeAttriType},
            </if>
            <if test="nodeAttriCode != null and nodeAttriCode != ''">
                #{nodeAttriCode},
            </if>
            <if test="nodeAttriUp != null">
                #{nodeAttriUp},
            </if>
            <if test="status != null and status != ''">
                #{status},
            </if>
            <if test="updateUser != null and updateUser != ''">
                #{updateUser},
            </if>
        </trim>
    </insert>

    <!-- 更新处理程序信息 -->
    <update id="update" parameterType="com.collect.entity.ProcessingProgramManagement">
        UPDATE processing_program_management
        <set>
            <if test="nodeAttriName != null">node_attri_name = #{nodeAttriName},</if>
            <if test="nodeAttriDesc != null">node_attri_desc = #{nodeAttriDesc},</if>
            <if test="nodeAttriType != null">node_attri_type = #{nodeAttriType},</if>
            <if test="nodeAttriCode != null">node_attri_code = #{nodeAttriCode},</if>
            <if test="nodeAttriUp != null">node_attri_up = #{nodeAttriUp},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
        </set>
        WHERE node_attri_id = #{nodeAttriId}
    </update>

    <!-- 根据节点属性ID删除处理程序信息 -->
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM processing_program_management
        WHERE node_attri_id = #{nodeAttriId}
    </delete>
</mapper> 