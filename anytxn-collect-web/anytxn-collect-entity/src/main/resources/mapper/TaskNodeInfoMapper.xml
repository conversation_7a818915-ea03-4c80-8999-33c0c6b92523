<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.TaskNodeInfoMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.TaskNodeInfo">
        <result column="CASE_CODE" property="caseCode" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" jdbcType="VARCHAR"/>
        <result column="NODE_CODE" property="nodeCode" jdbcType="VARCHAR"/>
        <result column="FLOWCHART_NAME" property="flowchartName" jdbcType="VARCHAR"/>
        <result column="NODE_EXEC_STATUS" property="nodeExecStatus" jdbcType="VARCHAR"/>
        <result column="NODE_EXEC_COUNT" property="nodeExecCount" jdbcType="INTEGER"/>
        <result column="LAST_EXEC_DURATION" property="lastExecDuration" jdbcType="BIGINT"/>
        <result column="LAST_EXEC_TIME" property="lastExecTime" jdbcType="TIMESTAMP"/>
        <result column="NEXT_NODE" property="nextNode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        CASE_CODE, TASK_ID, NODE_CODE, FLOWCHART_NAME, NODE_EXEC_STATUS, 
        NODE_EXEC_COUNT, LAST_EXEC_DURATION, LAST_EXEC_TIME, NEXT_NODE
    </sql>

    <!-- 通用条件查询任务节点信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.TaskNodeInfo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM task_node_info
        WHERE 1=1
        <if test="caseCode != null and caseCode != ''">
            AND CASE_CODE = #{caseCode}
        </if>
        <if test="taskId != null and taskId != ''">
            AND TASK_ID = #{taskId}
        </if>
        <if test="nodeCode != null and nodeCode != ''">
            AND NODE_CODE = #{nodeCode}
        </if>
        <if test="flowchartName != null and flowchartName != ''">
            AND FLOWCHART_NAME LIKE CONCAT('%', #{flowchartName}, '%')
        </if>
        <if test="nodeExecStatus != null and nodeExecStatus != ''">
            AND NODE_EXEC_STATUS = #{nodeExecStatus}
        </if>
        <if test="nodeExecCount != null">
            AND NODE_EXEC_COUNT = #{nodeExecCount}
        </if>
        <if test="lastExecDuration != null">
            AND LAST_EXEC_DURATION = #{lastExecDuration}
        </if>
        <if test="nextNode != null and nextNode != ''">
            AND NEXT_NODE = #{nextNode}
        </if>
    </select>


    <!-- 插入任务节点信息 -->
    <insert id="insert" parameterType="com.collect.entity.TaskNodeInfo">
        INSERT INTO task_node_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseCode != null and caseCode != ''">
                CASE_CODE,
            </if>
            <if test="taskId != null and taskId != ''">
                TASK_ID,
            </if>
            <if test="nodeCode != null and nodeCode != ''">
                NODE_CODE,
            </if>
            <if test="flowchartName != null and flowchartName != ''">
                FLOWCHART_NAME,
            </if>
            <if test="nodeExecStatus != null and nodeExecStatus != ''">
                NODE_EXEC_STATUS,
            </if>
            <if test="nodeExecCount != null">
                NODE_EXEC_COUNT,
            </if>
            <if test="lastExecDuration != null">
                LAST_EXEC_DURATION,
            </if>
            <if test="nextNode != null and nextNode != ''">
                NEXT_NODE,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="caseCode != null and caseCode != ''">
                #{caseCode},
            </if>
            <if test="taskId != null and taskId != ''">
                #{taskId},
            </if>
            <if test="nodeCode != null and nodeCode != ''">
                #{nodeCode},
            </if>
            <if test="flowchartName != null and flowchartName != ''">
                #{flowchartName},
            </if>
            <if test="nodeExecStatus != null and nodeExecStatus != ''">
                #{nodeExecStatus},
            </if>
            <if test="nodeExecCount != null">
                #{nodeExecCount},
            </if>
            <if test="lastExecDuration != null">
                #{lastExecDuration},
            </if>
            <if test="nextNode != null and nextNode != ''">
                #{nextNode},
            </if>
        </trim>
    </insert>


</mapper> 