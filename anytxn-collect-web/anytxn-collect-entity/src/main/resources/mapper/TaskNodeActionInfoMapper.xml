<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.TaskNodeActionInfoMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.TaskNodeActionInfo">
        <result column="CASE_CODE" property="caseCode" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" jdbcType="VARCHAR"/>
        <result column="NODE_CODE" property="nodeCode" jdbcType="VARCHAR"/>
        <result column="NODE_ATTRI_ID" property="nodeAttriId" jdbcType="VARCHAR"/>
        <result column="NODE_ATTRI_NAME" property="nodeAttriName" jdbcType="VARCHAR"/>
        <result column="ACTION_TIME" property="actionTime" jdbcType="TIMESTAMP"/>
        <result column="ACTION_RESULT" property="actionResult" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        CASE_CODE, TASK_ID, NODE_CODE, NODE_ATTRI_ID, NODE_ATTRI_NAME, 
        ACTION_TIME, ACTION_RESULT
    </sql>

    <!-- 通用条件查询任务节点动作信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.TaskNodeActionInfo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM task_node_action_info
        WHERE 1=1
        <if test="caseCode != null and caseCode != ''">
            AND CASE_CODE = #{caseCode}
        </if>
        <if test="taskId != null">
            AND TASK_ID = #{taskId}
        </if>
        <if test="nodeCode != null and nodeCode != ''">
            AND NODE_CODE = #{nodeCode}
        </if>
        <if test="nodeAttriId != null and nodeAttriId != ''">
            AND NODE_ATTRI_ID = #{nodeAttriId}
        </if>
        <if test="nodeAttriName != null and nodeAttriName != ''">
            AND NODE_ATTRI_NAME LIKE CONCAT('%', #{nodeAttriName}, '%')
        </if>
        <if test="actionResult != null and actionResult != ''">
            AND ACTION_RESULT = #{actionResult}
        </if>
    </select>
</mapper> 