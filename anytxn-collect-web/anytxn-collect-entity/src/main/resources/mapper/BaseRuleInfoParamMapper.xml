<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.collect.mapper.BaseRuleInfoParamMapper">

    <select id="selectAll" resultType="com.collect.entity.BaseRuleInfoParamPO">
        SELECT * FROM base_rule_info_param
        <where>
            <if test="ruleId != null and ruleId != ''">
                AND RULE_ID = #{ruleId}
            </if>
            <if test="ruleName != null and ruleName != ''">
                AND RULE_NAME LIKE CONCAT('%', #{ruleName}, '%')
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.collect.entity.BaseRuleInfoParamPO">
        INSERT INTO base_rule_info_param (
            RULE_ID, RULE_NAME, RULE_DESC, RULE_COND_FIELD, RULE_FAC_FIELD, RULE_RESULT_FIELD,
            RULE_TYPE_SUB, RULE_TYPE, RULE_PRI, EXEC_TYPE, RULE_STS, RULE_VER_NO, CRCD_ORG_NO,
            OP_EXPS_FIELD, CREATE_USER, UPDATE_USER, VERSION, PARAM_STS
        )
        VALUES (
            #{ruleId}, #{ruleName}, #{ruleDesc}, #{ruleCondField}, #{ruleFacField}, #{ruleResultField},
            #{ruleTypeSub}, #{ruleType}, #{rulePri}, #{execType}, #{ruleSts}, #{ruleVerNo}, #{crcdOrgNo},
            #{opExpsField}, #{createUser}, #{updateUser}, #{version}, #{paramSts}
        )
    </insert>

    <update id="update" parameterType="com.collect.entity.BaseRuleInfoParamPO">
        UPDATE base_rule_info_param
        SET
            RULE_ID = #{ruleId},
            RULE_NAME = #{ruleName},
            RULE_DESC = #{ruleDesc},
            RULE_COND_FIELD = #{ruleCondField},
            RULE_FAC_FIELD = #{ruleFacField},
            RULE_RESULT_FIELD = #{ruleResultField},
            RULE_TYPE_SUB = #{ruleTypeSub},
            RULE_TYPE = #{ruleType},
            RULE_PRI = #{rulePri},
            EXEC_TYPE = #{execType},
            RULE_STS = #{ruleSts},
            RULE_VER_NO = #{ruleVerNo},
            CRCD_ORG_NO = #{crcdOrgNo},
            OP_EXPS_FIELD = #{opExpsField},
            VERSION = #{version},
            PARAM_STS = #{paramSts}
        WHERE RULE_ID = #{ruleId}
    </update>

    <delete id="deleteById" parameterType="long">
        DELETE FROM base_rule_info_param WHERE INC_ID = #{incId}
    </delete>

</mapper>
