<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.collect.mapper.WcsActionTdcMapper">


    <resultMap id="WcsActionTdcResultMap" type="com.collect.entity.WcsActionTdcPO">
        <id property="id" column="id" />
        <result property="createUser" column="create_user" />
        <result property="updateUser" column="update_user" />
        <result property="orgCustNbr" column="org_cust_nbr" />
        <result property="caseCode" column="case_code" />
        <result property="caseResultFlag" column="case_result_flag" />
        <result property="contactName" column="contact_name" />
        <result property="phoneNmbr" column="phone_nmbr" />
        <result property="relation" column="relation" />
        <result property="actionCode" column="action_code" />
        <result property="contactRemarks" column="contact_remarks" />
        <result property="reviewDate" column="review_date" />
        <result property="commitmentAmount" column="commitment_amount" />
        <result property="commitmentDate" column="commitment_date" />
        <result property="dailFlag" column="dail_flag" />
        <result property="callDuration" column="call_duration" />
        <result property="startTime" column="start_time" />
        <result property="endTime" column="end_time" />
        <result property="audioCode" column="audio_code" />
        <result property="actionDate" column="action_date" />
        <result property="obType" column="ob_type" />
        <result property="telType" column="tel_type" />
        <result property="callType" column="call_type" />
    </resultMap>

    <insert id="insert" parameterType="com.collect.entity.WcsActionTdcPO">
        INSERT INTO wcs_action_tdc (
            create_user,  update_user,
            org_cust_nbr, case_code, case_result_flag, contact_name,
            phone_nmbr, relation, action_code, contact_remarks,
            review_date, commitment_amount, commitment_date,
            dail_flag, call_duration, start_time, end_time,
            audio_code, action_date, ob_type, tel_type, call_type
        ) VALUES (
                     #{createUser},  #{updateUser},
                     #{orgCustNbr}, #{caseCode}, #{caseResultFlag}, #{contactName},
                     #{phoneNmbr}, #{relation}, #{actionCode}, #{contactRemarks},
                     #{reviewDate}, #{commitmentAmount}, #{commitmentDate},
                     #{dailFlag}, #{callDuration}, #{startTime}, #{endTime},
                     #{audioCode}, #{actionDate}, #{obType}, #{telType}, #{callType}
                 )
    </insert>

    <update id="update" parameterType="com.collect.entity.WcsActionTdcPO">
        UPDATE wcs_action_tdc
        <set>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="orgCustNbr != null">org_cust_nbr = #{orgCustNbr},</if>
            <if test="caseCode != null">case_code = #{caseCode},</if>
            <if test="caseResultFlag != null">case_result_flag = #{caseResultFlag},</if>
            <if test="contactName != null">contact_name = #{contactName},</if>
            <if test="phoneNmbr != null">phone_nmbr = #{phoneNmbr},</if>
            <if test="relation != null">relation = #{relation},</if>
            <if test="actionCode != null">action_code = #{actionCode},</if>
            <if test="contactRemarks != null">contact_remarks = #{contactRemarks},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="commitmentAmount != null">commitment_amount = #{commitmentAmount},</if>
            <if test="commitmentDate != null">commitment_date = #{commitmentDate},</if>
            <if test="dailFlag != null">dail_flag = #{dailFlag},</if>
            <if test="callDuration != null">call_duration = #{callDuration},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="audioCode != null">audio_code = #{audioCode},</if>
            <if test="actionDate != null">action_date = #{actionDate},</if>
            <if test="obType != null">ob_type = #{obType},</if>
            <if test="telType != null">tel_type = #{telType},</if>
            <if test="callType != null">call_type = #{callType},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="int">
        DELETE FROM wcs_action_tdc WHERE id = #{id}
    </delete>

    <select id="selectById" parameterType="int" resultMap="WcsActionTdcResultMap">
        SELECT * FROM wcs_action_tdc WHERE id = #{id}
    </select>

    <select id="selectByCaseCode" resultMap="WcsActionTdcResultMap">
        SELECT * FROM wcs_action_tdc WHERE case_code = #{caseCode}
    </select>

    <select id="selectAll" resultMap="WcsActionTdcResultMap">
        SELECT * FROM wcs_action_tdc
    </select>
</mapper>