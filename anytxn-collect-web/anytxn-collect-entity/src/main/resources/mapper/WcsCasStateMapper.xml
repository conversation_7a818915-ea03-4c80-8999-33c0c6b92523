<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.WcsCasStateMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.collect.entity.WcsCasState">
        <id property="id" column="ID" jdbcType="VARCHAR"/>
        <result property="caseCode" column="CASE_CODE" jdbcType="VARCHAR"/>
        <result property="orgCustNbr" column="ORG_CUST_NBR" jdbcType="VARCHAR"/>
        <result property="custName" column="CUST_NAME" jdbcType="VARCHAR"/>
        <result property="icType" column="IC_TYPE" jdbcType="VARCHAR"/>
        <result property="custIc" column="CUST_IC" jdbcType="VARCHAR"/>
        <result property="chargoffFlag" column="CHARGOFF_FLAG" jdbcType="VARCHAR"/>
        <result property="countOdue" column="COUNT_ODUE" jdbcType="INTEGER"/>
        <result property="dteIntoCollection" column="DTE_INTO_COLLECTION" jdbcType="TIMESTAMP"/>
        <result property="dteOutCollection" column="DTE_OUT_COLLECTION" jdbcType="TIMESTAMP"/>
        <result property="firstDate" column="FIRST_DATE" jdbcType="TIMESTAMP"/>
        <result property="lastCaseCode" column="LAST_CASE_CODE" jdbcType="VARCHAR"/>
        <result property="lastCaseDate" column="LAST_CASE_DATE" jdbcType="TIMESTAMP"/>
        <result property="lawDate" column="LAW_DATE" jdbcType="TIMESTAMP"/>
        <result property="lawFlag" column="LAW_FLAG" jdbcType="VARCHAR"/>
        <result property="model" column="MODEL" jdbcType="VARCHAR"/>
        <result property="currState" column="CURR_STATE" jdbcType="VARCHAR"/>
        <result property="caseState" column="CASE_STATE" jdbcType="VARCHAR"/>
        <result property="teamCode" column="TEAM_CODE" jdbcType="VARCHAR"/>
        <result property="lastOpeTime" column="LAST_OPE_TIME" jdbcType="TIMESTAMP"/>
        <result property="inTeamDate" column="IN_TEAM_DATE" jdbcType="TIMESTAMP"/>
        <result property="reasonCode" column="REASON_CODE" jdbcType="VARCHAR"/>
        <result property="noneContactRuleCode" column="NONE_CONTACT_RULE_CODE" jdbcType="VARCHAR"/>
        <result property="contactCode" column="CONTACT_CODE" jdbcType="VARCHAR"/>
        <result property="contactTimes" column="CONTACT_TIMES" jdbcType="VARCHAR"/>
        <result property="resultTime" column="RESULT_TIME" jdbcType="VARCHAR"/>
        <result property="resultDirection" column="RESULT_DIRECTION" jdbcType="VARCHAR"/>
        <result property="attemptCount" column="ATTEMPT_COUNT" jdbcType="VARCHAR"/>
        <result property="callTotal" column="CALL_TOTAL" jdbcType="NUMERIC"/>
        <result property="lastModel" column="LAST_MODEL" jdbcType="VARCHAR"/>
        <result property="ename" column="ENAME" jdbcType="VARCHAR"/>
        <result property="eusex" column="EUSEX" jdbcType="VARCHAR"/>
        <result property="statementTypeAll" column="STATEMENT_TYPE_ALL" jdbcType="VARCHAR"/>
        <result property="billingcycle" column="BILLINGCYCLE" jdbcType="INTEGER"/>
        <result property="badnessCode" column="BADNESS_CODE" jdbcType="VARCHAR"/>
        <result property="classIBalance" column="CLASS_I_BALANCE" jdbcType="DECIMAL"/>
        <result property="riskrank" column="RISKRANK" jdbcType="VARCHAR"/>
        <result property="delayDays" column="DELAY_DAYS" jdbcType="INTEGER"/>
        <result property="activeInstallmentFlag" column="ACTIVE_INSTALLMENT_FLAG" jdbcType="VARCHAR"/>
        <result property="productType" column="PRODUCT_TYPE" jdbcType="VARCHAR"/>
        <result property="status24" column="STATUS_24" jdbcType="VARCHAR"/>
        <result property="wcsOutcode" column="WCS_OUTCODE" jdbcType="VARCHAR"/>
        <result property="handType" column="HAND_TYPE" jdbcType="VARCHAR"/>
        <result property="custTags" column="CUST_TAGS" jdbcType="VARCHAR"/>
        <result property="bankNbr" column="BANK_NBR" jdbcType="VARCHAR"/>
        <result property="branchNbr" column="BRANCH_NBR" jdbcType="VARCHAR"/>
        <result property="restateNotOpen" column="RESTATE_NOT_OPEN" jdbcType="VARCHAR"/>
        <result property="crlimitPerm" column="CRLIMIT_PERM" jdbcType="INTEGER"/>
        <result property="crlimitTemp" column="CRLIMIT_TEMP" jdbcType="INTEGER"/>
        <result property="note" column="NOTE" jdbcType="VARCHAR"/>
        <result property="custWcsMemo" column="CUST_WCS_MEMO" jdbcType="VARCHAR"/>
        <result property="vipcode" column="VIPCODE" jdbcType="VARCHAR"/>
        <result property="permIdExp" column="PERM_ID_EXP" jdbcType="VARCHAR"/>
        <result property="bornDate" column="BORN_DATE" jdbcType="TIMESTAMP"/>
        <result property="euTypeOfRes" column="EU_TYPE_OF_RES" jdbcType="VARCHAR"/>
        <result property="euPerOfRes" column="EU_PER_OF_RES" jdbcType="VARCHAR"/>
        <result property="job" column="job" jdbcType="VARCHAR"/>
        <result property="employer" column="EMPLOYER" jdbcType="VARCHAR"/>
        <result property="income" column="INCOME" jdbcType="INTEGER"/>
        <result property="abClass" column="AB_CLASS" jdbcType="VARCHAR"/>
        <result property="blockcode" column="BLOCKCODE" jdbcType="VARCHAR"/>
        <result property="blockDate" column="BLOCK_DATE" jdbcType="VARCHAR"/>
        <result property="prvBlockcode" column="PRV_BLOCKCODE" jdbcType="VARCHAR"/>
        <result property="prvBlockDate" column="PRV_BLOCK_DATE" jdbcType="VARCHAR"/>
        <result property="telNum" column="TEL_NUM" jdbcType="VARCHAR"/>
        <result property="email" column="EMAIL" jdbcType="VARCHAR"/>
        <result property="school" column="SCHOOL" jdbcType="VARCHAR"/>
        <result property="prexCustQualification" column="PREX_CUST_QUALIFICATION" jdbcType="VARCHAR"/>
        <result property="major" column="MAJOR" jdbcType="VARCHAR"/>
        <result property="euMaritalStatus" column="EU_MARITAL_STATUS" jdbcType="VARCHAR"/>
        <result property="custType" column="CUST_TYPE" jdbcType="VARCHAR"/>
        <result property="custStatus" column="CUST_STATUS" jdbcType="VARCHAR"/>
        <result property="custStatusDate" column="CUST_STATUS_DATE" jdbcType="VARCHAR"/>
        <result property="custGName" column="CUST_G_NAME" jdbcType="VARCHAR"/>
        <result property="custGPhone" column="CUST_G_PHONE" jdbcType="VARCHAR"/>
        <result property="depositArea" column="DEPOSIT_AREA" jdbcType="DECIMAL"/>
        <result property="custOpenDate" column="CUST_OPEN_DATE" jdbcType="DATE"/>
        <result property="custPhone" column="CUST_PHONE" jdbcType="VARCHAR"/>
        <result property="custBackPhone" column="CUST_BACK_PHONE" jdbcType="VARCHAR"/>
        <result property="custEmpTel1" column="CUST_EMP_TEL1" jdbcType="VARCHAR"/>
        <result property="custEmpTel2" column="CUST_EMP_TEL2" jdbcType="VARCHAR"/>
        <result property="custEmpTel3" column="CUST_EMP_TEL3" jdbcType="VARCHAR"/>
        <result property="custGlRln1" column="CUST_GL_RLN1" jdbcType="VARCHAR"/>
        <result property="custGlNam1" column="CUST_GL_NAM1" jdbcType="VARCHAR"/>
        <result property="custGlTel1" column="CUST_GL_TEL1" jdbcType="VARCHAR"/>
        <result property="custGlRln2" column="CUST_GL_RLN2" jdbcType="VARCHAR"/>
        <result property="custGlNam2" column="CUST_GL_NAM2" jdbcType="VARCHAR"/>
        <result property="custGlTel2" column="CUST_GL_TEL2" jdbcType="VARCHAR"/>
        <result property="custHomeCityAddr" column="CUST_HOME_CITY_ADDR" jdbcType="VARCHAR"/>
        <result property="custAddr" column="CUST_ADDR" jdbcType="VARCHAR"/>
        <result property="custGEmpA" column="CUST_G_EMP_A" jdbcType="VARCHAR"/>
        <result property="custmAddr" column="CUSTM_ADDR" jdbcType="VARCHAR"/>
        <result property="billAddr" column="BILL_ADDR" jdbcType="VARCHAR"/>
        <result property="updateUser" column="UPDATE_USER" jdbcType="VARCHAR"/>
        <result property="updateTime" column="UPDATE_TIME" jdbcType="TIMESTAMP"/>
    </resultMap>


    <!-- 公共列 -->
    <sql id="Base_Column_List">
        ID, CASE_CODE, ORG_CUST_NBR, CUST_NAME, IC_TYPE, CUST_IC, CHARGOFF_FLAG, COUNT_ODUE,
        DTE_INTO_COLLECTION, DTE_OUT_COLLECTION, FIRST_DATE, LAST_CASE_CODE, LAST_CASE_DATE,
        LAW_DATE, LAW_FLAG, MODEL, CURR_STATE, CASE_STATE, TEAM_CODE, LAST_OPE_TIME,
        IN_TEAM_DATE, REASON_CODE, NONE_CONTACT_RULE_CODE, CONTACT_CODE, CONTACT_TIMES,
        RESULT_TIME, RESULT_DIRECTION, ATTEMPT_COUNT, CALL_TOTAL, update_user, update_time
    </sql>


    <!-- 通用条件查询案件状态信息 -->
    <select id="selectByCondition" parameterType="com.collect.entity.WcsCasState" resultMap="BaseResultMap">
        SELECT
        id,
        case_code,
        org_cust_nbr,
        cust_name,
        ic_type,
        cust_ic,
        chargoff_flag,
        count_odue,
        dte_into_collection,
        dte_out_collection,
        first_date,
        last_case_code,
        last_case_date,
        law_date,
        law_flag,
        model,
        curr_state,
        case_state,
        team_code,
        last_ope_time,
        in_team_date,
        reason_code,
        none_contact_rule_code,
        contact_code,
        contact_times,
        result_time,
        result_direction,
        attempt_count,
        call_total,
        last_model,
        ename,
        eusex,
        statement_type_all,
        billingcycle,
        badness_code,
        class_i_balance,
        riskrank,
        delay_days,
        active_installment_flag,
        product_type,
        status_24,
        wcs_outcode,
        hand_type,
        cust_tags,
        bank_nbr,
        branch_nbr,
        restate_not_open,
        crlimit_perm,
        crlimit_temp,
        note,
        cust_wcs_memo,
        vipcode,
        perm_id_exp,
        born_date,
        eu_type_of_res,
        eu_per_of_res,
        job,
        employer,
        income,
        ab_class,
        blockcode,
        block_date,
        prv_blockcode,
        prv_block_date,
        tel_num,
        email,
        school,
        prex_cust_qualification,
        major,
        eu_marital_status,
        cust_type,
        cust_status,
        cust_status_date,
        cust_g_name,
        cust_g_phone,
        deposit_area,
        cust_open_date,
        cust_phone,
        cust_back_phone,
        cust_emp_tel1,
        cust_emp_tel2,
        cust_emp_tel3,
        cust_gl_rln1,
        cust_gl_nam1,
        cust_gl_tel1,
        cust_gl_rln2,
        cust_gl_nam2,
        cust_gl_tel2,
        cust_home_city_addr,
        cust_addr,
        cust_g_emp_a,
        custm_addr,
        bill_addr,
        update_user,
        update_time
        FROM wcs_cas_state
        WHERE 1=1
        <if test="id != null">
            AND ID = #{id}
        </if>
        <if test="caseCode != null and caseCode != ''">
            AND CASE_CODE = #{caseCode}
        </if>
        <if test="orgCustNbr != null and orgCustNbr != ''">
            AND ORG_CUST_NBR = #{orgCustNbr}
        </if>
        <if test="custName != null and custName != ''">
            AND CUST_NAME LIKE CONCAT('%', #{custName}, '%')
        </if>
        <if test="icType != null and icType != ''">
            AND IC_TYPE = #{icType}
        </if>
        <if test="custIc != null and custIc != ''">
            AND CUST_IC = #{custIc}
        </if>
        <if test="chargoffFlag != null and chargoffFlag != ''">
            AND CHARGOFF_FLAG = #{chargoffFlag}
        </if>
        <if test="countOdue != null">
            AND COUNT_ODUE = #{countOdue}
        </if>
        <if test="currState != null and currState != ''">
            AND CURR_STATE = #{currState}
        </if>
        <if test="caseState != null and caseState != ''">
            AND CASE_STATE = #{caseState}
        </if>
        <if test="teamCode != null and teamCode != ''">
            AND TEAM_CODE = #{teamCode}
        </if>
        <if test="lawFlag != null and lawFlag != ''">
            AND LAW_FLAG = #{lawFlag}
        </if>
        <if test="contactCode != null and contactCode != ''">
            AND CONTACT_CODE = #{contactCode}
        </if>
        <if test="model != null and model != ''">
            AND MODEL = #{model}
        </if>
    </select>

    <insert id="insert" parameterType="com.collect.entity.WcsCasState">
        insert into WCS_CAS_STATE (
        CASE_CODE, ORG_CUST_NBR, CUST_NAME, IC_TYPE, CUST_IC, CHARGOFF_FLAG, COUNT_ODUE,
        DTE_INTO_COLLECTION, DTE_OUT_COLLECTION, FIRST_DATE, LAST_CASE_CODE, LAST_CASE_DATE,
        LAW_DATE, LAW_FLAG, MODEL, CURR_STATE, CASE_STATE, TEAM_CODE, LAST_OPE_TIME,
        IN_TEAM_DATE, REASON_CODE, NONE_CONTACT_RULE_CODE, CONTACT_CODE, CONTACT_TIMES,
        RESULT_TIME, RESULT_DIRECTION, ATTEMPT_COUNT, CALL_TOTAL,update_time,update_user,CLASS_I_BALANCE
        )
        values (
        #{caseCode},#{orgCustNbr},#{custName},#{icType},#{custIc},#{chargoffFlag},#{countOdue},
        #{dteIntoCollection},#{dteOutCollection},#{firstDate},#{lastCaseCode},#{lastCaseDate},
        #{lawDate},#{lawFlag},#{model}, #{currState}, #{caseState}, #{teamCode}, #{lastOpeTime},
        #{inTeamDate}, #{reasonCode}, #{noneContactRuleCode}, #{contactCode}, #{contactTimes},
        #{resultTime}, #{resultDirection}, #{attemptCount}, #{callTotal},now(),#{updateUser},#{classIBalance}
        )
    </insert>

    <!-- 更新案件状态信息 -->
    <update id="update" parameterType="com.collect.entity.WcsCasState">
        UPDATE WCS_CAS_STATE
        <set>
            <if test="caseCode != null">CASE_CODE = #{caseCode},</if>
            <if test="orgCustNbr != null">ORG_CUST_NBR = #{orgCustNbr},</if>
            <if test="custName != null">CUST_NAME = #{custName},</if>
            <if test="icType != null">IC_TYPE = #{icType},</if>
            <if test="custIc != null">CUST_IC = #{custIc},</if>
            <if test="chargoffFlag != null">CHARGOFF_FLAG = #{chargoffFlag},</if>
            <if test="countOdue != null">COUNT_ODUE = #{countOdue},</if>
            <if test="dteIntoCollection != null">DTE_INTO_COLLECTION = #{dteIntoCollection},</if>
            <if test="dteOutCollection != null">DTE_OUT_COLLECTION = #{dteOutCollection},</if>
            <if test="firstDate != null">FIRST_DATE = #{firstDate},</if>
            <if test="lastCaseCode != null">LAST_CASE_CODE = #{lastCaseCode},</if>
            <if test="lastCaseDate != null">LAST_CASE_DATE = #{lastCaseDate},</if>
            <if test="lawDate != null">LAW_DATE = #{lawDate},</if>
            <if test="lawFlag != null">LAW_FLAG = #{lawFlag},</if>
            <if test="model != null">MODEL = #{model},</if>
            <if test="currState != null">CURR_STATE = #{currState},</if>
            <if test="caseState != null">CASE_STATE = #{caseState},</if>
            <if test="teamCode != null">TEAM_CODE = #{teamCode},</if>
            <if test="lastOpeTime != null">LAST_OPE_TIME = #{lastOpeTime},</if>
            <if test="inTeamDate != null">IN_TEAM_DATE = #{inTeamDate},</if>
            <if test="reasonCode != null">REASON_CODE = #{reasonCode},</if>
            <if test="noneContactRuleCode != null">NONE_CONTACT_RULE_CODE = #{noneContactRuleCode},</if>
            <if test="contactCode != null">CONTACT_CODE = #{contactCode},</if>
            <if test="contactTimes != null">CONTACT_TIMES = #{contactTimes},</if>
            <if test="resultTime != null">RESULT_TIME = #{resultTime},</if>
            <if test="resultDirection != null">RESULT_DIRECTION = #{resultDirection},</if>
            <if test="attemptCount != null">ATTEMPT_COUNT = #{attemptCount},</if>
            <if test="callTotal != null">CALL_TOTAL = #{callTotal},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="caseState != null">CASE_STATE = #{caseState},</if>
            <if test="custTags != null">CUST_TAGS = #{custTags},</if>
            <if test="currState != null">CURR_STATE = #{currState},</if>
            <if test="prvBlockcode != null">PRV_BLOCKCODE = #{prvBlockcode},</if>
            <if test="blockcode != null">BLOCKCODE = #{blockcode},</if>
            <if test="prvBlockDate != null">PRV_BLOCK_DATE = #{prvBlockDate},</if>
            <if test="classIBalance != null">CLASS_I_BALANCE = #{classIBalance},</if>
            update_time = NOW(),
            BLOCK_DATE = CURDATE()
        </set>
        WHERE ID = #{id}
    </update>


</mapper>