<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.collect.mapper.WcsActionSmsMapper">

    <!-- 新增 -->
    <insert id="insertWcsActionSms" parameterType="com.collect.entity.WcsActionSmsPO">
        INSERT INTO wcs_action_sms
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="createUser != null">create_user,</if>
            <if test="updateUser != null">update_user,</if>
            <if test="orgCustNbr != null">org_cust_nbr,</if>
            <if test="caseCode != null">case_code,</if>
            <if test="templateCode != null">template_code,</if>
            <if test="templateName != null">template_name,</if>
            <if test="telNum != null">tel_num,</if>
            <if test="sendContent != null">send_content,</if>
            <if test="receiver != null">receiver,</if>
            <if test="sender != null">sender,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="actionDate != null">action_date,</if>
            <if test="jobDay != null">job_day,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="createUser != null">#{createUser},</if>
            <if test="updateUser != null">#{updateUser},</if>
            <if test="orgCustNbr != null">#{orgCustNbr},</if>
            <if test="caseCode != null">#{caseCode},</if>
            <if test="templateCode != null">#{templateCode},</if>
            <if test="templateName != null">#{templateName},</if>
            <if test="telNum != null">#{telNum},</if>
            <if test="sendContent != null">#{sendContent},</if>
            <if test="receiver != null">#{receiver},</if>
            <if test="sender != null">#{sender},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="actionDate != null">#{actionDate},</if>
            <if test="jobDay != null">#{jobDay},</if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteWcsActionSmsById" parameterType="java.lang.Long">
        DELETE FROM wcs_action_sms WHERE id = #{id}
    </delete>

    <!-- 查询 -->
    <select id="selectWcsActionSmsById" parameterType="java.lang.Long" resultType="com.collect.entity.WcsActionSmsPO">
        SELECT * FROM wcs_action_sms WHERE id = #{id}
    </select>

    <!-- 查询 -->
    <select id="selectWcsActionSmsByCaseCode" resultType="com.collect.entity.WcsActionSmsPO">
        SELECT * FROM wcs_action_sms WHERE case_code = #{caseCode}
    </select>

    <!-- 更新 -->
    <update id="updateWcsActionSms" parameterType="com.collect.entity.WcsActionSmsPO">
        UPDATE wcs_action_sms
        <set>
            <if test="createUser != null">create_user = #{createUser},</if>
            <if test="updateUser != null">update_user = #{updateUser},</if>
            <if test="orgCustNbr != null">org_cust_nbr = #{orgCustNbr},</if>
            <if test="caseCode != null">case_code = #{caseCode},</if>
            <if test="templateCode != null">template_code = #{templateCode},</if>
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="telNum != null">tel_num = #{telNum},</if>
            <if test="sendContent != null">send_content = #{sendContent},</if>
            <if test="receiver != null">receiver = #{receiver},</if>
            <if test="sender != null">sender = #{sender},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="actionDate != null">action_date = #{actionDate},</if>
            <if test="jobDay != null">job_day = #{jobDay},</if>
        </set>
        WHERE id = #{id}
    </update>

</mapper>