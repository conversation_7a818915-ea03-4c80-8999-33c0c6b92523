<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		  xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
	<id>vmpackage</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<baseDirectory>${artifactId}</baseDirectory>

	<fileSets>
		<fileSet>
			<directory>${project.build.directory}</directory>
			<includes>
				<include>${project.build.finalName}.jar</include>
			</includes>
			<outputDirectory></outputDirectory>
		</fileSet>

		<fileSet>
			<directory>logs</directory>
			<outputDirectory>/logs</outputDirectory>
			<excludes>
				<exclude>**.md</exclude>
				<exclude>/*.log</exclude>
				<exclude>/*.gz</exclude>
				<exclude>/backup/*.log</exclude>
			</excludes>
			<useDefaultExcludes>true</useDefaultExcludes>
		</fileSet>
		<fileSet>
			<fileMode>755</fileMode>
			<!-- 打包前将该目录下的文件换行符替换成LF -->
			<lineEnding>unix</lineEnding>
			<directory>${project.basedir}/bin</directory>
			<outputDirectory>bin</outputDirectory>
			<includes>
				<include>/*</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/scripts</directory>
			<outputDirectory>scripts</outputDirectory>
			<includes>
				<include>/*</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/target/classes</directory>
			<outputDirectory>config</outputDirectory>
			<includes>
				<include>**.md</include>
				<include>**.yml</include>
				<include>**.txt</include>
				<include>**.xml</include>
				<include>**.json</include>
				<include>**.properties</include>
			</includes>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<outputDirectory>lib</outputDirectory>
			<unpack>false</unpack>
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>
</assembly>
