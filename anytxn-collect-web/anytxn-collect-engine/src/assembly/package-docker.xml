<assembly>
	<id>make-assembly</id>
	<formats>
		<format>tar.gz</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<fileSets>
		<fileSet>
			<directory>${project.basedir}</directory>
			<includes>
				<include>README.md</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>logs</directory>
			<outputDirectory>/logs</outputDirectory>
			<excludes>
				<exclude>**.md</exclude>
				<exclude>/*.log</exclude>
				<exclude>/*.gz</exclude>
				<exclude>/backup/*.log</exclude>
			</excludes>
			<useDefaultExcludes>true</useDefaultExcludes>
		</fileSet>
		<fileSet>
			<fileMode>755</fileMode>
			<!-- 打包前将该目录下的文件换行符替换成LF -->
			<lineEnding>unix</lineEnding>
			<directory>${project.basedir}/bin</directory>
			<outputDirectory>bin</outputDirectory>
			<includes>
				<include>docker_entrypoint.sh</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/scripts</directory>
			<outputDirectory>scripts</outputDirectory>
			<includes>
				<include>/*</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>${project.basedir}/src/main/resources</directory>
			<outputDirectory>config</outputDirectory>
			<includes>
				<include>/**</include>
				<include>**.md</include>
				<include>**.yml</include>
				<include>**.yaml</include>
				<include>**.txt</include>
				<include>**.xml</include>
				<include>**.properties</include>
				<include>/static/**</include>
				<include>/function/**</include>
			</includes>
		</fileSet>
	</fileSets>
	<dependencySets>
		<dependencySet>
			<useProjectArtifact>true</useProjectArtifact>
			<outputDirectory>/lib</outputDirectory>
			<scope>runtime</scope>
		</dependencySet>
	</dependencySets>
</assembly>
