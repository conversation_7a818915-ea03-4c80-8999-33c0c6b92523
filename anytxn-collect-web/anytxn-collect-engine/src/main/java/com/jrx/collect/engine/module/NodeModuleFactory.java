package com.jrx.collect.engine.module;

import com.anytxn.base.utils.StringUtils;
import com.collect.entity.NodeInfoDO;
import com.collect.entity.NodeProgramConfigInfoPO;
import com.google.common.collect.Lists;
import com.jrx.collect.engine.node.NodeCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class NodeModuleFactory {

    private static final Logger log = LoggerFactory.getLogger(StrategyFlowService.class);

    @Autowired
    public NodeCacheService nodeCacheService;

    @Autowired
    public Map<String,NodeModuleProcessor> nodeModuleProcessorMap;


    public List<NodeModuleProcessor> findModule(String nodeCode) {
        NodeInfoDO nodeInfo = nodeCacheService.findNodeInfo(nodeCode);
        if (nodeInfo == null){
            log.error("查询不到该节点{}信息,跳过节点处理程序处理",nodeCode);
            return Lists.newArrayList();
        }
        List<NodeProgramConfigInfoPO> nodeProgramConfigInfoList = nodeInfo.getNodeManageList();
        nodeProgramConfigInfoList.sort(Comparator.comparingInt(NodeProgramConfigInfoPO::getNodePriority));
        return nodeProgramConfigInfoList.stream().map(code-> {
            if (StringUtils.isBlank(code.getNodeAttriId())) {
                return null;
            }
            return nodeModuleProcessorMap.get(code.getNodeAttriId());
        }).filter(Objects::nonNull).toList();
    }
}
