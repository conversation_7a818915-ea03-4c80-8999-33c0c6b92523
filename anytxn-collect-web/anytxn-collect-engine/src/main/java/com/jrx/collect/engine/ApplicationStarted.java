package com.jrx.collect.engine;

import com.anytxn.base.cloud.EnableSpringCloud;
import com.anytxn.base.config.EnableConfigCenter;
import com.anytxn.base.discovery.DiscoveryManager;
import com.anytxn.base.spring.ApplicationRun;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

/**
 * 参数服务启动类
 *
 * <AUTHOR>
 * @date 2024/10/16
 */
@EnableConfigCenter
@EnableSpringCloud
@Import({DiscoveryManager.class})
@MapperScan(basePackages = {"com.anytxn", "com.collect"}, annotationClass = Mapper.class)
@ComponentScan(basePackages = {"com.anytxn.base.discovery","com.anytxn", "com.jrx.collect","com.collect"})
public class ApplicationStarted {

    public static void main(String[] args) {
        ApplicationRun.run(ApplicationStarted.class, args);
    }

}

