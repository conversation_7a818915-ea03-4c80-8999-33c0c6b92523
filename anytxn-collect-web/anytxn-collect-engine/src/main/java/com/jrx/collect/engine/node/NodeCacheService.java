package com.jrx.collect.engine.node;

import com.collect.entity.NodeInfoDO;
import com.collect.mapper.NodeInfoMapper;
import com.jrx.collect.engine.util.CaffeineRefreshUtil;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.LinkedList;
import java.util.List;

@Service
public class NodeCacheService implements InitializingBean {

    @Autowired
    private NodeInfoMapper nodeInfoMapper;

    private CaffeineRefreshUtil<String,NodeInfoDO> cache;

    public NodeInfoDO findNodeInfo(String nodeCode) {
        return cache.get(nodeCode);
    }



    @Override
    public void afterPropertiesSet() throws Exception {
        cache = new CaffeineRefreshUtil<>(
                key -> {
                    return nodeInfoMapper.selectAllWithConfigsByNodeCode(key);
                },
                Duration.ofMinutes(10),  // 写入10分钟后过期
                Duration.ofMinutes(1),    // 每分钟定时刷新
                1000                      // 最大缓存1000个条目
        );
    }
}
