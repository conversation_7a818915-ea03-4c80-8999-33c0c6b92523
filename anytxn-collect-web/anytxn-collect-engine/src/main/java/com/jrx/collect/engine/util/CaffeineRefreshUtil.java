package com.jrx.collect.engine.util;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;

import java.time.Duration;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

public class CaffeineRefreshUtil<K, V> {
    private final LoadingCache<K, V> cache;
    private final ScheduledExecutorService scheduler;
    private final Function<K, V> dataLoader;
    private final Duration refreshInterval;

    public CaffeineRefreshUtil(Function<K, V> dataLoader,
                               Duration expireAfterWrite,
                               Duration refreshInterval,
                               int maximumSize) {
        this.dataLoader = dataLoader;
        this.refreshInterval = refreshInterval;
        this.scheduler = Executors.newSingleThreadScheduledExecutor();

        // 初始化缓存
        this.cache = Caffeine.newBuilder()
                .expireAfterWrite(expireAfterWrite)
                .refreshAfterWrite(refreshInterval.dividedBy(2))
                .maximumSize(maximumSize)
                .executor(Executors.newFixedThreadPool(5))
                .build(new CacheLoader<>() {
                    @Override
                    public V load(K key) {
                        return dataLoader.apply(key);
                    }

                    @Override
                    public V reload(K key, V oldValue) {
                        return dataLoader.apply(key);
                    }
                });

        // 启动定时刷新任务
        startScheduledRefresh();
    }

    private void startScheduledRefresh() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                // 获取当前所有key的近似集合（适配Caffeine 3.1.6）
                cache.policy().eviction().ifPresent(eviction -> {
                    eviction.hottest(Math.toIntExact(eviction.getMaximum()))
                            .forEach((key,value) -> {
                                if (key != null) {
                                    cache.refresh(key);
                                }
                            });
                });
            } catch (Exception e) {
                System.err.println("定时刷新任务异常: " + e.getMessage());
            }
        }, refreshInterval.toMillis(), refreshInterval.toMillis(), TimeUnit.MILLISECONDS);
    }

    public V get(K key) {
        return cache.get(key);
    }

    public void put(K key, V value) {
        cache.put(key, value);
    }

    public void invalidate(K key) {
        cache.invalidate(key);
    }

    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        cache.cleanUp();
    }


}
