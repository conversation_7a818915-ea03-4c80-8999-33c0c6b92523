package com.jrx.collect.engine.common.enums;

public enum PriorityEnum {

    HIGH(6,"event-high"),
    MEDIUM(3,"event-medium"),
    LOW(1,"event-low");

    private final int weight;
    private final String topic;

    PriorityEnum(int weight, String topic) {
        this.weight = weight;
        this.topic = topic;
    }

    public int getWeight() {
        return weight;
    }

    public String getTopic() {
        return topic;
    }
}
