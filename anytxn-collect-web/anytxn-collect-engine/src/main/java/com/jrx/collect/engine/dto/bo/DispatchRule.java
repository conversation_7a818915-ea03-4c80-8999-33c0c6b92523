package com.jrx.collect.engine.dto.bo;

import com.jrx.collect.engine.common.enums.OperatorEnum;

public class DispatchRule {

    private String id;

    private String nodeRule;

    private String nodeRuleFactor;

    private OperatorEnum operator;

    private String nodeRuleValue;

    private String nextNode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNodeRule() {
        return nodeRule;
    }

    public void setNodeRule(String nodeRule) {
        this.nodeRule = nodeRule;
    }

    public String getNodeRuleFactor() {
        return nodeRuleFactor;
    }

    public void setNodeRuleFactor(String nodeRuleFactor) {
        this.nodeRuleFactor = nodeRuleFactor;
    }

    public OperatorEnum getOperator() {
        return operator;
    }

    public void setOperator(OperatorEnum operator) {
        this.operator = operator;
    }

    public String getNodeRuleValue() {
        return nodeRuleValue;
    }

    public void setNodeRuleValue(String nodeRuleValue) {
        this.nodeRuleValue = nodeRuleValue;
    }

    public String getNextNode() {
        return nextNode;
    }

    public void setNextNode(String nextNode) {
        this.nextNode = nextNode;
    }
}
