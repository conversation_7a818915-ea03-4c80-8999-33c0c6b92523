package com.jrx.collect.engine.module;

import com.collect.entity.NodeInfoDO;
import com.collect.entity.TaskNodeInfo;
import com.collect.service.TaskNodeInfoService;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.node.NodeCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.Instant;

@Service
public class TaskService {

    @Autowired
    private TaskNodeInfoService taskNodeInfoService;


    public void execute(EventContext eventContext) {
        NodeInfoDO nodeInfo = eventContext.getNodeInfo();
        TaskNodeInfo taskNodeInfo = new TaskNodeInfo();
        taskNodeInfo.setTaskId(System.currentTimeMillis()+"");
        taskNodeInfo.setNodeCode(nodeInfo.getNodeCode());
        taskNodeInfo.setCaseCode(eventContext.getEventRequest().getEvent().getCaseSummary().getCaseId());
        taskNodeInfo.setNodeExecStatus("正常");
        taskNodeInfo.setFlowchartName(nodeInfo.getNodeCallName());
        taskNodeInfo.setLastExecDuration(1L);
        taskNodeInfo.setLastExecTime(Timestamp.from(Instant.now()));
        taskNodeInfoService.add(taskNodeInfo);

    }
}
