package com.jrx.collect.engine.api;

import com.anytxn.base.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jrx.collect.engine.common.enums.PriorityEnum;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BatchEventRequest {
    private List<EventRequest> requests;

    private Map<PriorityEnum, List<EventRequest>> requestMap;

    public static BatchEventRequest parse(List<String> requestStr) {
        BatchEventRequest batchEventRequest = new BatchEventRequest();

        batchEventRequest.setRequests(requestStr.stream().map(request-> JsonUtils.toObject(request, EventRequest.class)).toList());
        return batchEventRequest;
    }

    public List<EventRequest> getRequests() {
        return requests;
    }

    public void setRequests(List<EventRequest> requests) {
        this.requests = requests;
    }

    public Map<PriorityEnum, List<EventRequest>> getRequestMap() {
        return requestMap;
    }

    public void setRequestMap(Map<PriorityEnum, List<EventRequest>> requestMap) {
        this.requestMap = requestMap;
    }

    public void group() {
        Map<PriorityEnum, List<EventRequest>> all = Maps.newHashMap();
        requests.forEach(eventRequest -> {
            List<EventRequest> events = all.getOrDefault(eventRequest.getHead().getPriority(), Lists.newArrayList());
            events.add(eventRequest);
            all.put(eventRequest.getHead().getPriority(),events);
        });
        requestMap = all;
    }
}
