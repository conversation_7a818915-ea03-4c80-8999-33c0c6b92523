package com.jrx.collect.engine.service;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.context.NodeContext;
import com.jrx.collect.engine.node.NodeProcessor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

@Service
public class NodeProcessService implements InitializingBean {

    ThreadPoolExecutor normalExecutor;


    public void process(NodeProcessor nodeProcessor, EventContext eventContext) {
        nodeProcessor.execute(eventContext);
    }


    public Future processAsync(NodeProcessor nodeProcessor, EventContext eventContext) {
        return normalExecutor.submit(()->processAsync(nodeProcessor,eventContext));
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("node-process-pool-%d").build();
        normalExecutor = new ThreadPoolExecutor(
                4, 8, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
    }
}
