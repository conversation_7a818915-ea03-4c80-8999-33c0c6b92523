package com.jrx.collect.engine.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jrx.collect.engine.api.BatchEventRequest;
import com.jrx.collect.engine.api.EventRequest;
import com.jrx.collect.engine.common.enums.PriorityEnum;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.node.NodeProcessor;
import com.jrx.collect.engine.util.WeightedRoundRobin;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Service
public class NodeScheduleService implements InitializingBean {

    private WeightedRoundRobin<PriorityEnum> weightedRoundRobin;

    @Autowired
    public Map<String,NodeProcessor> nodeProcessorMap;

    /**
     * 分组分拣
     *
     * @return
     */
    public Map<PriorityEnum, List<EventRequest>> groupSorting(BatchEventRequest batchEventRequest,int batchSize) {
        Map<PriorityEnum, List<EventRequest>> result = Maps.newHashMap();
        for (int i = 0; i < batchSize; i++) {
            WeightedRoundRobin.RobinItem<PriorityEnum> robinItem = weightedRoundRobin.getNextRobinItem();
            List<EventRequest> availableEvents = batchEventRequest.getRequestMap().get(robinItem.getItem());
            if (!CollectionUtils.isEmpty(availableEvents)){
                EventRequest eventRequest = availableEvents.remove(0);
                List<EventRequest> events = result.getOrDefault(eventRequest.getHead().getPriority(), Lists.newArrayList());
                events.add(eventRequest);
                result.put(eventRequest.getHead().getPriority(),events);
            }
        }
        return result;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        weightedRoundRobin = new WeightedRoundRobin(new WeightedRoundRobin.RobinItem<>(PriorityEnum.HIGH, PriorityEnum.HIGH.getWeight()),
                new WeightedRoundRobin.RobinItem<>(PriorityEnum.MEDIUM, PriorityEnum.MEDIUM.getWeight()),
                new WeightedRoundRobin.RobinItem<>(PriorityEnum.LOW, PriorityEnum.LOW.getWeight()));
    }

    public NodeProcessor chooseProcessor(EventContext eventContext) {
        return nodeProcessorMap.get("simple");
    }

}
