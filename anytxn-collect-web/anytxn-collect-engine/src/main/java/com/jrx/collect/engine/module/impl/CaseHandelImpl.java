package com.jrx.collect.engine.module.impl;

import com.collect.entity.WcsCasState;
import com.collect.service.WcsCasStateService;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.dto.bo.CaseSummary;
import com.jrx.collect.engine.module.NodeModuleProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;

@Service("CaseInfoUpdateProcessing")
//@DependsOn("wcsCasStateService")
public class CaseHandelImpl implements NodeModuleProcessor {

    @Autowired
    private WcsCasStateService wcsCasStateService;

    @Override
    public void execute(EventContext flowContext) {
        //
        WcsCasState query = new WcsCasState();
        CaseSummary caseSummary = flowContext.getEventRequest().getEvent().getCaseSummary();
        boolean inFlag = caseSummary.getDebtAmount() != null && caseSummary.getDebtAmount().compareTo(BigDecimal.ZERO) > 0;
        query.setOrgCustNbr(caseSummary.getCustomerId());
        query.setCurrState("P");
        List<WcsCasState>  exists = wcsCasStateService.getByCondition(query);
        if (inFlag){
            if (CollectionUtils.isEmpty(exists)){
                WcsCasState newCase = new WcsCasState();
                newCase.setCaseCode(generateCaseCode());
                baseUpdate(newCase,caseSummary);
                newCase.setCurrState("P");
                newCase.setCaseState("1-在催");
                newCase.setModel("X");
                newCase.setCallTotal(BigDecimal.valueOf(0));
                newCase.setDteIntoCollection(LocalDate.now());
                newCase.setUpdateTime(LocalDateTime.now());
                newCase.setUpdateUser("__system");
                //TODO 补全字段
                wcsCasStateService.add(newCase);
                caseSummary.setCaseId(newCase.getCaseCode());
            }else{
                WcsCasState existCase = exists.get(0);
                baseUpdate(existCase,caseSummary);
                wcsCasStateService.update(existCase);
                caseSummary.setCaseId(existCase.getCaseCode());
            }
        }else{
            if (!CollectionUtils.isEmpty(exists)){
                WcsCasState existCase = exists.get(0);
                caseSummary.setCaseId(existCase.getCaseCode());
                baseUpdate(existCase,caseSummary);
                existCase.setCurrState("C");
                existCase.setCaseState("0-出催");
                existCase.setDteOutCollection(LocalDate.now());
                existCase.setUpdateTime(LocalDateTime.now());
                existCase.setUpdateUser("__system");
                wcsCasStateService.update(existCase);
                flowContext.setStrategyEnable(false);
            }
        }

    }

    private void baseUpdate(WcsCasState newCase, CaseSummary caseSummary) {
        newCase.setOrgCustNbr(caseSummary.getCustomerId());
        newCase.setCustName(caseSummary.getCustomerName());
        newCase.setIcType(caseSummary.getIdType());
        newCase.setCustIc(caseSummary.getIdNbr());
        newCase.setClassIBalance(caseSummary.getDebtAmount());
        newCase.setBadnessCode(caseSummary.getBigSign());
        newCase.setVipcode(caseSummary.getVipSign());
    }

    private String generateCaseCode() {
        return "CS"+LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMd"))+"_"+ String.format("%5d",new Random().nextInt(10000));

    }
}
