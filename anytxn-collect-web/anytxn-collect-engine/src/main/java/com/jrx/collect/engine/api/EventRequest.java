package com.jrx.collect.engine.api;

import com.jrx.collect.engine.common.enums.PriorityEnum;
import com.jrx.collect.engine.dto.bo.Event;

public class EventRequest {

    private Head head;

    private Event event;

    public Head getHead() {
        return head;
    }

    public void setHead(Head head) {
        this.head = head;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public static class Head {
        private String currentNode;
        private PriorityEnum priority;

        public String getCurrentNode() {
            return currentNode;
        }

        public void setCurrentNode(String currentNode) {
            this.currentNode = currentNode;
        }

        public PriorityEnum getPriority() {
            return priority;
        }

        public void setPriority(PriorityEnum priority) {
            this.priority = priority;
        }
    }
}
