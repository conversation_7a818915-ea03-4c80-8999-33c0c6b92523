package com.jrx.collect.engine.context;

import com.collect.entity.NodeInfoDO;
import com.jrx.collect.engine.api.EventRequest;

public class EventContext {

    private EventRequest eventRequest;

    private String nodeCode;

    private NodeInfoDO nodeInfo;

    private boolean strategyEnable = true;

    public EventRequest getEventRequest() {
        return eventRequest;
    }

    public void setEventRequest(EventRequest eventRequest) {
        this.eventRequest = eventRequest;
    }

    public String getNodeCode() {
        return nodeCode;
    }

    public void setNodeCode(String nodeCode) {
        this.nodeCode = nodeCode;
    }

    public NodeInfoDO getNodeInfo() {
        return nodeInfo;
    }

    public void setNodeInfo(NodeInfoDO nodeInfo) {
        this.nodeInfo = nodeInfo;
    }

    public boolean isStrategyEnable() {
        return strategyEnable;
    }

    public void setStrategyEnable(boolean strategyEnable) {
        this.strategyEnable = strategyEnable;
    }
}
