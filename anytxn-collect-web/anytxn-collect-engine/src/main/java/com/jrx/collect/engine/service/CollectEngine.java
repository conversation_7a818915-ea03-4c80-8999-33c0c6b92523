package com.jrx.collect.engine.service;

import com.anytxn.base.constant.BaseResponse;
import com.collect.entity.NodeInfoDO;
import com.jrx.collect.engine.api.BatchEventRequest;
import com.jrx.collect.engine.api.EventRequest;
import com.jrx.collect.engine.common.enums.PriorityEnum;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.node.NodeCacheService;
import com.jrx.collect.engine.node.NodeProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class CollectEngine {

    @Autowired
    private NodeScheduleService nodeScheduleService;

    @Autowired
    private NodeProcessService nodeProcessService;

    @Autowired
    public NodeCacheService nodeCacheService;


    public BaseResponse process(BatchEventRequest batchEventRequest) {

        batchEventRequest.group();

        do {
            Map<PriorityEnum, List<EventRequest>> groupSortingResult = nodeScheduleService.groupSorting(batchEventRequest, 10);
            groupSortingResult.forEach((k,v)->{
                v.forEach(eventRequest -> {
                    EventContext eventContext = buildContext(eventRequest);
                    innerProcess(eventContext);
                });
            });
        } while (batchEventRequest.getRequests().isEmpty());

        //TODO
        return BaseResponse.success();
    }

    private void innerProcess(EventContext eventContext) {
        NodeProcessor nodeProcessor = nodeScheduleService.chooseProcessor(eventContext);
        nodeProcessService.process(nodeProcessor,eventContext);

    }

    private EventContext buildContext(EventRequest eventRequest) {
        EventContext eventContext = new EventContext();
        eventContext.setEventRequest(eventRequest);
        eventContext.setNodeCode(eventRequest.getHead().getCurrentNode());
        String nodeCode = eventContext.getNodeCode();
        NodeInfoDO nodeInfo = nodeCacheService.findNodeInfo(nodeCode);
        eventContext.setNodeInfo(nodeInfo);
        return eventContext;
    }

}
