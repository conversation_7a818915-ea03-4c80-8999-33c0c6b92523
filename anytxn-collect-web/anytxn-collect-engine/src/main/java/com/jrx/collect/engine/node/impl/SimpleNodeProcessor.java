package com.jrx.collect.engine.node.impl;

import com.collect.entity.NodeInfoDO;
import com.google.common.collect.Lists;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.module.NodeModuleFactory;
import com.jrx.collect.engine.module.NodeModuleProcessor;
import com.jrx.collect.engine.module.TaskService;
import com.jrx.collect.engine.module.StrategyFlowService;
import com.jrx.collect.engine.node.NodeCacheService;
import com.jrx.collect.engine.node.NodeProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("simple")
public class SimpleNodeProcessor implements NodeProcessor {

    private static final Logger log = LoggerFactory.getLogger(SimpleNodeProcessor.class);

    @Autowired
    private NodeModuleFactory nodeModuleFactory;

    @Autowired
    private TaskService taskService;

    @Autowired
    private StrategyFlowService strategyFlowService;

    @Autowired
    public NodeCacheService nodeCacheService;


    @Override
    public void execute(EventContext eventContext) {
        log.info("开始执行节点,nodeCode:{}",eventContext.getNodeCode());
        if (!baseCheck(eventContext)){
            log.error("查询不到该节点{}信息,中断处理",eventContext.getNodeCode());
            return;
        }
        List<NodeModuleProcessor> nodeModuleProcessorList = nodeModuleFactory.findModule(eventContext.getNodeCode());
        for (NodeModuleProcessor nodeModuleProcessor : nodeModuleProcessorList) {
            nodeModuleProcessor.execute(eventContext);
        }
        //任务处理
        taskService.execute(eventContext);
        //最后流转处理
        try {
            strategyFlowService.execute(eventContext);
        }catch (Exception e){
            log.error("流转处理异常,nodeCode:{}",eventContext.getNodeCode(),e);
        }
    }

    private boolean baseCheck(EventContext eventContext) {
        NodeInfoDO nodeInfo = nodeCacheService.findNodeInfo(eventContext.getNodeCode());
        if (nodeInfo == null){
            return false;
        }
        return true;
    }
}
