package com.jrx.collect.engine.runner;

import com.anytxn.base.constant.BaseResponse;
import com.google.common.collect.Lists;
import com.jrx.collect.engine.service.CollectEngine;
import com.jrx.collect.engine.api.BatchEventRequest;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class KafkaEventRunner{

    private static final Logger log = LoggerFactory.getLogger(KafkaEventRunner.class);

    private CollectEngine collectEngine;

    public KafkaEventRunner(CollectEngine collectEngine) {
        this.collectEngine = collectEngine;
    }


    @KafkaListener(topics = {"event-high", "event-medium","event-low"})
    public void listen(ConsumerRecords<String, String> records, Acknowledgment ack) {
        log.info("拉取kafka消息,size:{}",records.count());
        List<String> msg = Lists.newArrayList();
        records.iterator().forEachRemaining(record->{
            msg.add(record.value());
        });
        BatchEventRequest batchEventRequest = BatchEventRequest.parse(msg);
        BaseResponse eventResponse = collectEngine.process(batchEventRequest);
        int index = figureMaxSuccessIndex(eventResponse);
        ack.acknowledge();
    }

    private int figureMaxSuccessIndex(BaseResponse eventResponse) {
        //TODO
        return 0;
    }

}
