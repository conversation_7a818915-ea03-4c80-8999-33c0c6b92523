package com.jrx.collect.engine.util;

import java.util.Arrays;
import java.util.List;

public class WeightedRoundRobin<T> {

    private List<WeightedRoundRobin.RobinItem<T>> items;

    private int totalWeight;

    public WeightedRoundRobin(RobinItem<T>... items) {
        this.items = Arrays.stream(items).toList();
        this.totalWeight = this.items.stream().mapToInt(RobinItem::getWeight).sum();
    }

    public WeightedRoundRobin.RobinItem<T> getNextRobinItem() {
        if (items.isEmpty()) {
            return null;
        }

        WeightedRoundRobin.RobinItem<T> selectItem = null;
        int maxCurrentWeight = Integer.MIN_VALUE;

        // 遍历所有服务器，更新当前权重并选择最大的
        for (WeightedRoundRobin.RobinItem<T> item : items) {
            int newWeight = item.getCurrentWeight() + item.getWeight();
            item.setCurrentWeight(newWeight);

            if (newWeight > maxCurrentWeight) {
                maxCurrentWeight = newWeight;
                selectItem = item;
            }
        }

        if (selectItem != null) {
            selectItem.setCurrentWeight(selectItem.getCurrentWeight() - totalWeight);
        }

        return selectItem;
    }

    public static class RobinItem<T> {
        private T item;
        private int weight;
        private int currentWeight;

        public RobinItem(T item, int weight) {
            this.item = item;
            this.weight = weight;
        }

        public T getItem() {
            return item;
        }

        public void setItem(T item) {
            this.item = item;
        }

        public int getWeight() {
            return weight;
        }

        public void setWeight(int weight) {
            this.weight = weight;
        }

        public int getCurrentWeight() {
            return currentWeight;
        }

        public void setCurrentWeight(int currentWeight) {
            this.currentWeight = currentWeight;
        }
    }
}
