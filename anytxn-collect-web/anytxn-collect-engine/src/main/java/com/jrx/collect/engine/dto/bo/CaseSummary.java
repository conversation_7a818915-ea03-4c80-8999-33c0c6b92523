package com.jrx.collect.engine.dto.bo;

import java.math.BigDecimal;

public class CaseSummary {

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号
     */
    private String idNbr;

    /**
     * 模块
     */
    private String model;

    /**
     * 案件id
     */
    private String caseId;

    /**
     * 延滞天数
     */
    private int delayDay;

    /**
     * 欠款金额
     */
    private BigDecimal debtAmount;

    /**
     * 不良标识
     */
    private String bigSign;

    /**
     * VIP标识域
     */
    private String vipSign;

    /**
     * 卡产品
     */
    private String cardProduct;

    /**
     * true-入催
     * false-出催
     */
    private boolean inFlag;


    public String getCaseId() {
        return caseId;
    }

    public void setCaseId(String caseId) {
        this.caseId = caseId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public int getDelayDay() {
        return delayDay;
    }

    public void setDelayDay(int delayDay) {
        this.delayDay = delayDay;
    }

    public BigDecimal getDebtAmount() {
        return debtAmount;
    }

    public void setDebtAmount(BigDecimal debtAmount) {
        this.debtAmount = debtAmount;
    }

    public String getBigSign() {
        return bigSign;
    }

    public void setBigSign(String bigSign) {
        this.bigSign = bigSign;
    }

    public String getVipSign() {
        return vipSign;
    }

    public void setVipSign(String vipSign) {
        this.vipSign = vipSign;
    }

    public String getCardProduct() {
        return cardProduct;
    }

    public void setCardProduct(String cardProduct) {
        this.cardProduct = cardProduct;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNbr() {
        return idNbr;
    }

    public void setIdNbr(String idNbr) {
        this.idNbr = idNbr;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public boolean isInFlag() {
        return inFlag;
    }

    public void setInFlag(boolean inFlag) {
        this.inFlag = inFlag;
    }
}
