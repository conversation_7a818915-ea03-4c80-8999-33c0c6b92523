package com.jrx.collect.engine.module;

import com.anytxn.base.utils.JsonUtils;
import com.anytxn.base.utils.StringUtils;
import com.anytxn.rule.dto.RuleExecByRuIdReqDTO;
import com.anytxn.rule.dto.RuleExecRespDTO;
import com.anytxn.rule.dto.RuleInputDTO;
import com.anytxn.rule.service.RuleExecuteService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jrx.collect.engine.api.EventRequest;
import com.jrx.collect.engine.common.config.KafkaProducer;
import com.jrx.collect.engine.common.enums.PriorityEnum;
import com.jrx.collect.engine.context.EventContext;
import com.jrx.collect.engine.dto.bo.CaseSummary;
import com.jrx.collect.engine.dto.bo.DispatchRule;
import com.jrx.collect.engine.dto.bo.Event;
import com.jrx.collect.engine.node.NodeCacheService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

@Component
public class StrategyFlowService {

    private static final Logger log = LoggerFactory.getLogger(StrategyFlowService.class);

    @Autowired
    public NodeCacheService nodeCacheService;

    @Autowired
    public RuleExecuteService ruleExecuteService;

    @Autowired
    private KafkaProducer kafkaProducer;

    public void execute(EventContext eventContext) {
        if (!eventContext.isStrategyEnable()){
            log.info("结束流转");
            return;
        }
        String nodeCode = eventContext.getNodeCode();

        List<DispatchRule> dispatchRuleList = JsonUtils.toArray(eventContext.getNodeInfo().getDispatchManageList(), DispatchRule.class);
        if (CollectionUtils.isEmpty(dispatchRuleList)){
            return;
        }
        for (DispatchRule dispatchRule : dispatchRuleList) {
            try {
                if (StringUtils.isBlank(dispatchRule.getNodeRule())){
                    sendNext(dispatchRule.getNextNode(),eventContext.getEventRequest().getEvent());
                }else{
                    RuleExecByRuIdReqDTO ruleExecByRuIdReq = new RuleExecByRuIdReqDTO();
                    ruleExecByRuIdReq.setRuleId(dispatchRule.getNodeRule());
                    ruleExecByRuIdReq.setRuleType("node");
                    ruleExecByRuIdReq.setLocalSysJrnNo(System.currentTimeMillis()+"");
                    ruleExecByRuIdReq.setRuleInputData(generateInputData(eventContext));
                    RuleExecRespDTO ruleExecResp = ruleExecuteService.executeByRuleId(ruleExecByRuIdReq);
                    if (ruleExecResp.getResult() == null){
                        log.warn("规则不命中输出,nodeId:{}",dispatchRule.getNodeRule());
                        continue;
                    }
                    Map<String, Object> result = Maps.newHashMap();
                    ruleExecResp.getResult().forEach(result::putAll);
                    if (dispatchRule.getNodeRuleValue().equals(result.get(dispatchRule.getNodeRuleFactor()))){
                        sendNext(dispatchRule.getNextNode(),eventContext.getEventRequest().getEvent());
                        break;
                    }
                }

            } catch (Exception e) {
                log.error("流转处理异常,nodeCode:{},dispatchRule:{}",eventContext.getNodeCode(),JsonUtils.toJson(dispatchRule),e);
            }
        }
    }


    private void sendNext(String nextNode, Event event) {
        PriorityEnum priority = figurePriority(nextNode);

        EventRequest eventRequest = new EventRequest();

        EventRequest.Head head = new EventRequest.Head();
        head.setCurrentNode(nextNode);
        head.setPriority(priority);
        eventRequest.setHead(head);

        eventRequest.setEvent(event);

        kafkaProducer.sendMessage(priority.getTopic(), JsonUtils.toJson(eventRequest));
    }

    private List<RuleInputDTO> generateInputData(EventContext eventContext) {
        CaseSummary caseSummary = eventContext.getEventRequest().getEvent().getCaseSummary();
        List<RuleInputDTO> ruleInputList = Lists.newArrayList();

        ruleInputList.add(buildRule("key1",caseSummary.getDebtAmount()));
        ruleInputList.add(buildRule("key2",caseSummary.getDelayDay()));
        ruleInputList.add(buildRule("key3",caseSummary.getBigSign()));
        ruleInputList.add(buildRule("key4",caseSummary.getVipSign()));
        ruleInputList.add(buildRule("key5",caseSummary.getCardProduct()));
        ruleInputList.add(buildRule("key6",eventContext.getNodeCode()));

        return ruleInputList;
    }

    private RuleInputDTO buildRule(String key, Object o) {
        RuleInputDTO ruleInputKey = new RuleInputDTO();
        ruleInputKey.setRuleKey(key);
        ruleInputKey.setObj(o);
        return ruleInputKey;
    }

    /**
     * 识别节点优先级
     * @param nodeCode
     * @return
     */
    private PriorityEnum figurePriority(String nodeCode) {
        return PriorityEnum.MEDIUM;
    }
}
