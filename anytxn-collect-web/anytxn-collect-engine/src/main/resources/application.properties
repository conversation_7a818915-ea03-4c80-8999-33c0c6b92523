#app.config-url=local://localhost?filename=local.properties
app.config-url=nacos://*************:8848?dataId=anytxn-collect-pro&namespace=dev&group=DEFAULT_GROUP
app.app-name=anytxn-collect-engine
app.server-port=8080
app.unit-no=unit00
app.node-type=NORMAL
mybatis.mapper-locations=classpath*:mapper/*.xml
app.datasource.max-pool-size=20
app.runtime.server.use-pg-inc-id=false
mybatis.configuration.map-underscore-to-camel-case=true
spring.cloud.nacos.discovery.namespace=dev
spring.cloud.nacos.discovery.server-addr=*************



spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=15
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=CollectionHikariCP
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.connection-test-query=SELECT 1


spring.datasource.type=com.zaxxer.hikari.HikariDataSource


spring.kafka.bootstrap-servers = *************:9092,172.16.70.104:9092,172.16.70.105:9092
spring.kafka.producer.key-serializer = org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer = org.apache.kafka.common.serialization.StringSerializer

spring.kafka.consumer.group-id = my-group
spring.kafka.consumer.key-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer = org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.auto-offset-reset = earliest
spring.kafka.consumer.enable-auto-commit = false
spring.kafka.consumer.max-poll-records = 50
spring.kafka.listener.type = batch
spring.kafka.listener.ack-mode = manual