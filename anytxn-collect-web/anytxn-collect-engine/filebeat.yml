filebeat.inputs:
  # 定义一个日志输入源
  - type: log
    enabled: true
    paths:
      # 监控容器内的日志文件路径
      - /app/logs/*.log
    # 针对每条日志的标签，可以用于区分日志来源
    tags:
      - "${APP_NAME}"
    # 日志编码格式（如果需要）
    encoding: utf-8
    # 是否忽略旧日志
    ignore_older: 48h
    # 添加日志来源元数据
    fields:
      environment: ${NAMESPACE}  # 使用环境变量传入环境名称
    fields_under_root: true  # 将 fields 添加到日志根节点

# Filebeat 自动识别容器和 Kubernetes 元数据（如果在容器或K8S环境中使用）
processors:
  - add_host_metadata: ~
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

# 如果需要输出到 Logstash，可以用以下配置
#output.logstash:
#  hosts: ["*************:5044","*************:5044","*************:5044"]
output.kafka:
  hosts: ["${KAFKA_HOST1}", "${KAFKA_HOST2}", "${KAFKA_HOST3}"]
  topic: T_FILEBEAT_LOG
  version: 2.8.0

# 定义如何处理文件偏移量，确保重启时能从上次位置继续读取日志
filebeat.registry.path: /var/lib/filebeat/registry

# 设置日志级别和输出位置（可选）
logging:
  level: info  # 设置日志级别：error, warning, info, debug
  to_files: true
  files:
    path: /var/log/filebeat
    name: filebeat.log
    keepfiles: 7
    permissions: 0644
