<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.anytxn.base</groupId>
        <artifactId>anytxn-collect-web</artifactId>
        <version>2.2.0-CUB-SNAPSHOT</version>
    </parent>

    <artifactId>anytxn-collect-admin</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.anytxn.base</groupId>
            <artifactId>anytxn-collect-entity</artifactId>
            <version>2.2.0-CUB-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.anytxn.base</groupId>
                <artifactId>anytxn-collect-common</artifactId>
            <version>2.2.0-CUB-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.anytxn.base</groupId>
            <artifactId>anytxn-started</artifactId>
        </dependency>
       <!-- <dependency>
            <groupId>com.anytxn.param</groupId>
            <artifactId>anytxn-param-sdk</artifactId>
        </dependency>-->
     <!--   <dependency>
            <groupId>com.anytxn.common</groupId>
            <artifactId>anytxn-rule</artifactId>
        </dependency>-->
        <!-- MySQL连接器 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>


        <!-- Spring JDBC -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

    </dependencies>


    <build>
        <finalName>anytxn-collect-admin</finalName>
        <plugins>
            <!-- smart-doc插件 -->
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <configuration>
                    <!--指定生成文档的使用的配置文件-->
                    <configFile>${basedir}/src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>CUB</projectName>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                    </execution>
                </executions>
            </plugin>
            <!--打包插件-->
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <skipAssembly>false</skipAssembly>
                            <finalName>${project.artifactId}-${project.version}</finalName>
                            <descriptors>
                                <descriptor>src/assembly/package.xml</descriptor> <!-- Assembly 描述符文件 -->
                            </descriptors>
                        </configuration>
                    </execution>
                    <execution>
                        <id>make-assembly-docker</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <skipAssembly>false</skipAssembly>
                            <finalName>${project.artifactId}</finalName>
                            <descriptors>
                                <descriptor>src/assembly/package-docker.xml</descriptor> <!-- Assembly 描述符文件 -->
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 代码不发布到maven仓库 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>