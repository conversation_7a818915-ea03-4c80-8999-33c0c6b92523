#!/usr/bin/env bash

# 设置基本目录路径
SHELL_DIR=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source ${SHELL_DIR}/check.sh
. ~/.bash_profile

# 定义日志目录和类路径
LOG_DIR="${PROJECT_HOME}/logs"
CLASSPATH="${PROJECT_HOME}/config:${PROJECT_HOME}/lib/*"

# 设置默认的 Profile，如果启动命令中提供则覆盖
PROFILE=${1:-""}

# GC 日志配置 (Java 17)
GC_OPTS="-XX:+DisableExplicitGC \
         -Xlog:gc*:file=${LOG_DIR}/gc.log:time,level,tags:filecount=10,filesize=10M \
         -XX:ErrorFile=${LOG_DIR}/hs_err_pid<pid>.log"

# 内存溢出记录 Dump 文件路径
HEAP_DUMP="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${LOG_DIR}/"

# Java 基本配置
JAVA_OPT="-Dspring.profiles.active=${PROFILE} \
          -Duser.timezone=GMT+8 \
          -XX:+PrintCommandLineFlags \
          -XX:+ParallelRefProcEnabled"

# JMX 配置，生产环境关闭
if [[ "${JMX_ENABLED}" == "true" ]]; then
  JMX_OPTS="-Dcom.sun.management.jmxremote.port=${JMX_PORT} \
            -Djava.rmi.server.hostname=${JMX_HOSTNAME} \
            -Dcom.sun.management.jmxremote=true \
            -Dcom.sun.management.jmxremote.ssl=false \
            -Dcom.sun.management.jmxremote.authenticate=false"
  JAVA_OPT="${JAVA_OPT} ${JMX_OPTS}"
fi

# 根据环境模式设置 JVM 配置
if [[ "${MODE}" == "ADMIN" ]]; then
  JAVA_OPT="${JAVA_OPT} -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled"
else
  JAVA_OPT="${JAVA_OPT} -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:MaxMetaspaceSize=512m -XX:MetaspaceSize=512m"
fi

# 默认内存配置
JAVA_MEM_OPTS=${JAVA_MEM_OPTS:-"-Xms128m -Xmx256m -Xss256k"}

# 完整的 JVM 配置
JAVA_OPT="${JAVA_MEM_OPTS} ${JAVA_OPT} ${GC_OPTS} ${HEAP_DUMP}"

# 输出环境变量信息
echo -e "\033[31m 注意：当前使用的是${PROFILE}环境，请根据系统实际情况设置最大最小内存参数, 当前默认值：${JAVA_MEM_OPTS} \033[0m"
echo "工程目录: ${PROJECT_HOME}"
echo "配置文件变量：\$PROFILE=${PROFILE}"
echo "服务模式：\$MODE=${MODE}"
echo "日志目录：\$LOG_DIR=${LOG_DIR}"
echo "JMX启用：\$JMX_ENABLED=${JMX_ENABLED}"
echo "\$JAVA_OPT=${JAVA_OPT}"

# 启动命令
LOG_FILE="start.out"
TID_FILE="${PROJECT_HOME}/tpid"
JAVA_CMD="${JAVA_OPT} -cp ${CLASSPATH} ${APPLICATION_MAIN}"

# 创建日志文件和目录
function logDir() {
    mkdir -p "${LOG_DIR}"
    touch "${LOG_DIR}/${LOG_FILE}"
}

# 查找日志文件
function findLogFile() {
    if [[ "${PROFILE}" == "prd" || "${PROFILE}" == "poc" ]]; then
        local ftime=1
        echo -n "查找日志文件."
        while [[ ${ftime} -lt 7 ]]; do
            for logFile in $(ls "${LOG_DIR}"); do
                if [[ ${logFile} =~ \.log$ ]]; then
                    LOG_FILE=${logFile}
                    ftime=8
                    break
                fi
            done
            echo -n "."
            ((ftime++))
            sleep 1
        done
        echo -n ", 日志文件: ${LOG_FILE}"
    fi
}

# 执行主启动命令
function executeMain() {
    if [[ "${STARTUP_LOG}" == "true" ]]; then
        echo "执行命令: ${JAVA_HOME}/bin/java ${JAVA_CMD}"
        nohup ${JAVA_HOME}/bin/java ${JAVA_CMD} --NAMESPACE=${NAMESPACE} --SERVER_ADDR=${SERVER_ADDR} > "${LOG_DIR}/${LOG_FILE}" 2>&1 &
    else
        echo "执行命令: ${JAVA_HOME}/bin/java ${JAVA_CMD}"
        nohup ${JAVA_HOME}/bin/java ${JAVA_CMD} --NAMESPACE=${NAMESPACE} --SERVER_ADDR=${SERVER_ADDR} > /dev/null 2>&1 &
    fi
}

# 记录 PID 到文件
function writePid() {
    echo "记录 PID 文件: $!"
    echo $! > "${TID_FILE}"
}

# 移除 PID 文件
function rmPid() {
    [[ -e ${TID_FILE} ]] && rm -f "${TID_FILE}"
}

# 启动服务
function startup() {
    getPID
    logDir
    echo "================================================================================================================"
    if [[ ${PROFILE} -ne 0 ]]; then
        echo "${APPLICATION_MAIN} 已启动 (PID=${TPID})"
        echo "================================================================================================================"
    else
        echo "启动 ${APPLICATION_MAIN}"
        executeMain
        sleep 1
        getPID
        findLogFile
        writePid
        if [[ ${TPID} -ne 0 ]]; then
            echo "${APPLICATION_MAIN} (PID=${TPID})...[成功]"
            echo "================================================================================================================"
            echo "tail 日志文件 : ${LOG_DIR}/${LOG_FILE}"
            tail -f "${LOG_DIR}/${LOG_FILE}"
        else
            echo -e "\033[31m ${APPLICATION_MAIN} 启动失败. [失败] \033[0m"
            echo "================================================================================================================"
            tail -200 "${LOG_DIR}/${LOG_FILE}"
            rmPid
        fi
    fi
}

rmPid
startup
