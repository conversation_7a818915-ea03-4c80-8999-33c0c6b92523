#!/usr/bin/env bash

# 基本目录设置
SHELL_DIR=$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )
source "${SHELL_DIR}/check.sh"

# 停止服务的超时配置
TIMEOUT=20
TIMES=0
TPID=0

# 从 tpid 文件中获取 PID
getPidFromFile() {
    TPID=0
    if [[ -e "${PROJECT_HOME}/tpid" ]]; then
        TPID=$(< "${PROJECT_HOME}/tpid")
        echo "PID from file: ${TPID}"
    else
        echo "PID file not found."
    fi
}

# 获取当前运行的 PID
getRunningPid() {
    local pid_info=$(pgrep -f "${APPLICATION_MAIN}")
    TPID=${pid_info:-0}
    echo "Running PID: ${TPID}"
}

# 停止服务
shutdown() {
    local force_pid_file=${2:-""}

    # 使用指定方法获取 PID
    if [[ ${force_pid_file} == "c" ]]; then
        getRunningPid
    else
        getPidFromFile
    fi

    if [[ ${TPID} -ne 0 ]]; then
        echo "=========================Stopping Service=============================="
        kill "${TPID}"

        if [[ $? -ne 0 ]]; then
            echo "[Service Stop Failed]"
            return
        fi

        echo -n "Stopping ${APPLICATION_MAIN} (PID=${TPID})..."

        # 循环检测 PID 是否仍存在
        while [[ ${TPID} -ne 0 && ${TIMES} -lt ${TIMEOUT} ]]; do
            getRunningPid
            ((TIMES++))
            sleep 1
            echo -n "."
        done

        # 根据检测结果输出
        if [[ ${TPID} -eq 0 ]]; then
            [[ -e "${PROJECT_HOME}/tpid" ]] && rm -f "${PROJECT_HOME}/tpid"
            echo "Service Stop Success."
        elif [[ ${TIMES} -ge ${TIMEOUT} ]]; then
            echo "Service Stop Timeout, please kill -9 ${TPID}"
        fi
    else
        echo "${APPLICATION_MAIN} is not running"
    fi
}

# 执行停止命令，传入自定义超时时间和模式
shutdown 3 "$1"
