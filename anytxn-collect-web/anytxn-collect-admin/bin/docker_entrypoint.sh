#!/usr/bin/env bash

# 设置项目目录
PROJECT_HOME="/app"
APPLICATION_MAIN="${APP_MAIN}"

# JVM 内存设置，默认值可以被不同环境覆盖
JAVA_MEM_OPTS="${JVM_MEM_OPTS:-"-Xms128m -Xmx4g -Xss256k"}"

# JMX 设置，仅在非生产环境启用
JMX_ENABLED="false"
JMX_HOSTNAME="127.0.0.1"
JMX_PORT="18703"

# 启动日志输出配置 默认值为0
STARTUP_LOG="${STARTUP_LOG:-2}"

# 默认 Profile 设置为 "dev"
PROFILE="${1:-dev}"

# 日志目录设置
LOG_DIR="${PROJECT_HOME}/logs"
if [[ ${CLASSPATH} ]]; then
    CLASSPATH="${CLASSPATH}:${PROJECT_HOME}/config:${PROJECT_HOME}/lib/*"
else
    CLASSPATH="${PROJECT_HOME}/config:${PROJECT_HOME}/lib/*"
fi

# GC 日志参数，使用多行格式，增强可读性
GC_OPTS="-XX:+DisableExplicitGC \
         -Xlog:gc*:file=${LOG_DIR}/gc.log:utctime,level,tags:filecount=10,filesize=10M \
         -XX:ErrorFile=${LOG_DIR}/hs_err_pid$$.log"


# 内存溢出记录配置
HEAP_DUMP="-XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${LOG_DIR}/"

# 其他 JVM 参数
JAVA_OPT="-Dspring.profiles.active=${PROFILE} -Duser.timezone=GMT+8 -XX:+PrintCommandLineFlags"
JAVA_OPT="${JAVA_OPT} -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:MaxMetaspaceSize=512m -XX:MetaspaceSize=512m -XX:+ParallelRefProcEnabled"

# JMX 参数，仅在启用时设置
if [[ "${JMX_ENABLED}" = "true" ]]; then
    JMX_OPTS="-Dcom.sun.management.jmxremote.port=${JMX_PORT} -Djava.rmi.server.hostname=${JMX_HOSTNAME}"
    JMX_OPTS="${JMX_OPTS} -Dcom.sun.management.jmxremote=true -Dcom.sun.management.jmxremote.ssl=false"
    JMX_OPTS="${JMX_OPTS} -Dcom.sun.management.jmxremote.authenticate=false"
    JAVA_OPT="${JAVA_OPT} ${JMX_OPTS}"
fi

# SkyWalking 和 JaCoCo Agent 配置
JAVA_OPT="${JAVA_OPT} -javaagent:/skywalking-agent/skywalking-agent.jar -Dskywalking.agent.service_name=${APP_NAME}"
JAVA_OPT="${JAVA_OPT} -Dskywalking.collector.backend_service=${SKYWALKING_ADDR}"
JAVA_OPT="${JAVA_OPT} -DSW_AGENT_TRACE_IGNORE_PATH=${SW_AGENT_TRACE_IGNORE_PATH:/actuator/health/**,/eureka/**,HikariCP/**,UndertowDispatch,*/healthCheck,/actuator/**}"
#nacos 配置
JAVA_OPT="${JAVA_OPT} -DSERVER_ADDR=${SERVER_ADDR} -DNAMESPACE=${NAMESPACE} -DUNIT_NO=${UNIT_NO} -DIDC=${IDC}"

# 打印当前配置，便于调试
echo -e "\033[31m注意：当前使用的是 ${PROFILE} 环境，请根据系统实际情况设置最大最小内存参数，当前默认值：${JAVA_MEM_OPTS}\033[0m"
JAVA_OPT="${JAVA_MEM_OPTS} ${JAVA_OPT} ${GC_OPTS} ${HEAP_DUMP}"

# 输出环境变量信息
echo "工程目录: ${PROJECT_HOME}"
echo "配置文件变量：\$PROFILE=${PROFILE}"
echo "日志目录：\$LOG_DIR=${LOG_DIR}"
echo "控制台日志：\$STARTUP_LOG=${STARTUP_LOG} ,說明:0 寫檔, 1 遺棄, 2 終端與寫檔"
echo "JMX启用：\$JMX_ENABLED=${JMX_ENABLED}"
echo "\$JAVA_OPT=${JAVA_OPT}"

# 日志文件设置，生产环境通常关闭控制台日志
LOG_FILE="start.out"
FILEBEAT_LOG_FILE="filebeat.out"

# Java 命令组装
JAVA_CMD="${JAVA_OPT} -cp ${CLASSPATH} ${APPLICATION_MAIN}"

# 创建日志目录和文件
logDir() {
    mkdir -p "${LOG_DIR}"
    [[ ! -f "${LOG_DIR}/${LOG_FILE}" ]] && touch "${LOG_DIR}/${LOG_FILE}"
    [[ ! -f "${LOG_DIR}/${FILEBEAT_LOG_FILE}" ]] && touch "${LOG_DIR}/${FILEBEAT_LOG_FILE}"
}

# 根据日志模式生成命令
setLogMode() {
    case "${STARTUP_LOG}" in
        0)  # 写日志到文件
            logDir
            JAVA_CMD="${JAVA_CMD} > ${LOG_DIR}/${LOG_FILE} 2>&1"
            ;;
        1)  # 日志丢弃
            JAVA_CMD="${JAVA_CMD} > /dev/null 2>&1"
            ;;
        2)  # 同时写日志到文件和显示在终端
            logDir
            JAVA_CMD="${JAVA_CMD} 2>&1 | tee -a ${LOG_DIR}/${LOG_FILE}"
            ;;
        *)
            echo "错误: 无效的 STARTUP_LOG 值 (${STARTUP_LOG})。有效值为: 0, 1, 2"
            exit 1
            ;;
    esac
}

# 执行主命令，控制台或日志输出控制
executeMain() {
    echo "执行命令: ${JAVA_HOME}/bin/java ${JAVA_CMD}"
    eval "${JAVA_HOME}/bin/java ${JAVA_CMD}"
}

# 启动服务函数
startup() {
    setLogMode
    echo "================================================================================================================"
    echo "Starting ${APPLICATION_MAIN}"
    executeMain
    sleep 1
}

# 启动服务
startup
