package com.collect.service;

import com.collect.entity.WcsActionTdcPO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 17:35
 **/
public interface WcsActionTdcService {

    WcsActionTdcPO selectById(Integer id);

    List<WcsActionTdcPO> selectByCaseCode(String caseCode);

    void insert(WcsActionTdcPO wcsActionTdcPO);

    void updateById(WcsActionTdcPO wcsActionTdcPO);

    void deleteById(Integer id);
}
