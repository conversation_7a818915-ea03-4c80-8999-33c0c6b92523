package com.collect.service.impl;

import com.collect.entity.WorkBenchConfigPO;
import com.collect.mapper.WorkBenchConfigMapper;
import com.collect.service.WorkBenchConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class WorkBenchConfigServiceImpl implements WorkBenchConfigService {
    
    @Autowired
    private WorkBenchConfigMapper workBenchConfigMapper;

    @Override
    public List<WorkBenchConfigPO> selectAll(WorkBenchConfigPO record) {
        return workBenchConfigMapper.selectAll(record);
    }

    @Override
    public int insertSelective(WorkBenchConfigPO record) {
        record.setCreateUser("admin");
        record.setUpdateUser("admin");
        return workBenchConfigMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(WorkBenchConfigPO record) {
        return workBenchConfigMapper.updateByPrimaryKeySelective(record);
    }

    // 其他方法实现类似...
}