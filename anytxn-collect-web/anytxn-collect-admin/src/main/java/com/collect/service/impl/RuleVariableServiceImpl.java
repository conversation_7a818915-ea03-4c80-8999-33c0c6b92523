package com.collect.service.impl;


import com.anytxn.base.error.CommonError;
import com.anytxn.base.error.ServiceException;
import com.anytxn.base.utils.CollUtils;
import com.anytxn.base.utils.ObjectUtils;
import com.anytxn.base.utils.StringUtils;
import com.collect.assembly.RuleVariableAssembler;
import com.collect.dto.WebResponseDO;
import com.collect.error.WebErrorCode;
import com.collect.service.RuleVariableService;
import com.collect.constant.PageInfo;
import com.collect.constant.ResponseDTO;
import com.collect.entity.RuleVariableDO;
import com.collect.entity.WebRuleVariablePO;
import com.collect.enums.RuleValueType;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.collect.mapper.WebRuleVariableMapper;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.EnumSet;
import java.util.List;

/**
 * 规则因子的业务实现.
 *
 * <AUTHOR>
 * @date 2024 /12/17 14:02
 */
@Component
public class RuleVariableServiceImpl implements RuleVariableService {

    private final WebRuleVariableMapper webRuleVariableMapper;

    private final EnumSet<RuleValueType> ruleValueTypes = EnumSet.of(RuleValueType.AMOUNT, RuleValueType.DOUBLE_X, RuleValueType.LONG_X);

    /**
     * Instantiates a new Rule variable service.
     *
     * @param webRuleVariableMapper the web rule variable mapper
     */
    public RuleVariableServiceImpl(final WebRuleVariableMapper webRuleVariableMapper) {
        this.webRuleVariableMapper = webRuleVariableMapper;
    }

    @Override
    public WebResponseDO<List<RuleVariableDO>> queryAll(final RuleVariableDO req, final PageInfo webPage) {
        if (webPage == null) {
            throw ServiceException.create(WebErrorCode.B1300008);
        }
        return queryByCondition(req, webPage);
    }

    @Override
    public WebResponseDO<List<RuleVariableDO>> queryByRuleType(final RuleVariableDO req, final PageInfo webPage) {
        if (StringUtils.isBlank(req.getRuleType())) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "ruleType", req.getRuleType());
        }
        return queryByCondition(req, webPage);
    }

    private ResponseDTO<RuleVariableDO> queryByKey(final RuleVariableDO req) {
        return ResponseDTO.success(getByKey(req));
    }

    private RuleVariableDO getByKey(final RuleVariableDO req) {
        if (StringUtils.isBlank(req.getKey())) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "key", req.getKey());
        }
        RuleVariableDO keyDo = new RuleVariableDO();
        keyDo.setKey(req.getKey());
        List<RuleVariableDO> list = queryByCondition(keyDo, null).getData();
        if (CollUtils.isNotEmpty(list)) {
            return list.get(0);
        } else {
            return null;
        }
    }

    @Override
    public void save(final RuleVariableDO req) {
        if (StringUtils.isBlank(req.getKey())) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "key", req.getKey());
        }
        if (StringUtils.isBlank(req.getRuleType())) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "ruleType", req.getRuleType());
        }
        if (req.getRuleExecuteType() == null) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "ruleExecuteType", req.getRuleExecuteType());
        }
        if (req.getRuleType() == null) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "valueType", req.getValueType());
        }
        if (StringUtils.isBlank(req.getName())) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "name", req.getName());
        }
        checkRuleType(req);
        RuleVariableDO oldDo = getByKey(req);
        if (oldDo != null) {
            throw ServiceException.create(WebErrorCode.B1300008);
        }
        webRuleVariableMapper.insert(RuleVariableAssembler.of().do2poObject(req));
    }

    private void checkRuleType(final RuleVariableDO req) {
        if (ruleValueTypes.contains(req.getValueType())) {
            if (req.getMinLength() == null || req.getMaxLength() == null) {
                throw ServiceException.create(CommonError.PARAM_FAILURE, "minLength | maxLength");
            }
            if (req.getValueType() == RuleValueType.LONG_X) {
                return;
            }
            if (req.getDigits() == null) {
                throw ServiceException.create(CommonError.PARAM_FAILURE, "digits");
            }
        }
    }

    @Override
    public void update(final RuleVariableDO req) {
        if (StringUtils.isBlank(req.getKey())) {
            throw ServiceException.create(CommonError.PARAM_FAILURE, "key", req.getKey());
        }
        //根据key 查询.
        RuleVariableDO value = getByKey(req);
        if (value == null) {
            throw ServiceException.create(WebErrorCode.B1300009);
        }
        value.setName(ObjectUtils.getAnother(req.getName(), value.getName()));
        value.setValueType(ObjectUtils.getAnother(req.getValueType(), value.getValueType()));
        value.setRuleExecuteType(ObjectUtils.getAnother(req.getRuleExecuteType(), value.getRuleExecuteType()));
        value.setRuleType(ObjectUtils.getAnother(req.getRuleType(), value.getRuleType()));
        value.setDigits(ObjectUtils.getAnother(req.getDigits(), value.getDigits()));
        value.setMinLength(ObjectUtils.getAnother(req.getMinLength(), value.getMinLength()));
        value.setMaxLength(ObjectUtils.getAnother(req.getMaxLength(), value.getMaxLength()));
        webRuleVariableMapper.update(RuleVariableAssembler.of().do2poObject(value));
    }

    @NotNull
    private WebResponseDO<List<RuleVariableDO>> queryByCondition(final RuleVariableDO req, final PageInfo webPage) {
        RuleVariableAssembler ruleVariableAssembler = RuleVariableAssembler.of();
        if (webPage != null) {
            try (Page<?> page = PageMethod.startPage(webPage.getPageNo(), webPage.getPageSize())) {
                List<WebRuleVariablePO> poList = webRuleVariableMapper.queryAll(ruleVariableAssembler.do2poObject(req));
                List<RuleVariableDO> doList = poList.stream().map(ruleVariableAssembler::po2doObject).toList();
                return WebResponseDO.of(PageInfo.page(page.getPageNum(), page.getPageSize(), page.getTotal()), doList);
            }
        } else {
            List<WebRuleVariablePO> poList = webRuleVariableMapper.queryAll(ruleVariableAssembler.do2poObject(req));
            List<RuleVariableDO> doList = poList.stream().map(ruleVariableAssembler::po2doObject).toList();
            return WebResponseDO.of(doList);
        }

    }
}
