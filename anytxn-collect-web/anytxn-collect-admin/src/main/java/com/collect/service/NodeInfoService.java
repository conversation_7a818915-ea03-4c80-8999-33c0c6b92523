package com.collect.service;

import com.collect.constant.PageInfo;
import com.collect.dto.NodeInfoReqDTO;
import com.collect.dto.WebResponseDO;
import com.collect.entity.NodeInfoDO;
import com.collect.entity.NodeInfoPO;

import java.util.List;

public interface NodeInfoService {
    NodeInfoPO getByNodeCode(String nodeCode);
    void save(NodeInfoPO nodeInfoPO);

    void update(NodeInfoPO nodeInfoPO);
    void deleteByNodeCode(String nodeCode);
    List<NodeInfoPO> getAll();
    WebResponseDO<List<NodeInfoDO>> selectAllWithConfigs(NodeInfoReqDTO nodeInfoReqDTO, PageInfo pageInfo);
    WebResponseDO<NodeInfoDO> selectAllWithConfigsByNodeCode(String nodeCode);
}
