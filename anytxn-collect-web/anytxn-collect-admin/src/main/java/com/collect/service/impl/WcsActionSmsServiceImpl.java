package com.collect.service.impl;

import com.collect.entity.WcsActionSmsPO;
import com.collect.mapper.WcsActionSmsMapper;
import com.collect.service.WcsActionSmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 20:31
 **/
@Service
public class WcsActionSmsServiceImpl implements WcsActionSmsService {

    @Autowired
    private WcsActionSmsMapper wcsActionSmsMapper;

    @Override
    public int insertWcsActionSms(WcsActionSmsPO wcsActionSmsPO) {
        return wcsActionSmsMapper.insertWcsActionSms(wcsActionSmsPO);
    }

    @Override
    public int deleteWcsActionSmsById(Long id) {
        return wcsActionSmsMapper.deleteWcsActionSmsById(id);
    }

    @Override
    public WcsActionSmsPO selectWcsActionSmsById(Long id) {
        return wcsActionSmsMapper.selectWcsActionSmsById(id);
    }

    @Override
    public int updateWcsActionSms(WcsActionSmsPO wcsActionSmsPO) {
        return wcsActionSmsMapper.updateWcsActionSms(wcsActionSmsPO);
    }

    @Override
    public List<WcsActionSmsPO> selectWcsActionSmsByCaseCode(String caseCode) {
        return wcsActionSmsMapper.selectWcsActionSmsByCaseCode(caseCode);
    }

}
