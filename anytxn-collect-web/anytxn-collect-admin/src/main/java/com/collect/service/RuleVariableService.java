package com.collect.service;

import com.collect.dto.WebResponseDO;
import com.collect.constant.PageInfo;
import com.collect.entity.RuleVariableDO;

import java.util.List;

/**
 * 处理规则因子数据.
 *
 * <AUTHOR>
 * @date 2024 /12/17 13:58
 */
public interface RuleVariableService {

    /**
     * 根据条件查询数据.
     *
     * @param req     the req
     * @param webPage the web page
     * @return the page response dto
     */
    WebResponseDO<List<RuleVariableDO>> queryAll(final RuleVariableDO req, final PageInfo webPage);

    /**
     * Query by rule type response dto.
     *
     * @param req     the req
     * @param webPage the web page
     * @return the response dto
     */
    WebResponseDO<List<RuleVariableDO>> queryByRuleType(final RuleVariableDO req, final PageInfo webPage);

    /**
     * Save response dto.
     *
     * @param req the req
     * @return the response dto
     */
    void save(final RuleVariableDO req);

    /**
     * Update response dto.
     *
     * @param req the req
     * @return the response dto
     */
    void update(final RuleVariableDO req);
}
