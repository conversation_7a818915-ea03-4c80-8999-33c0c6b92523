package com.collect.service;


import com.collect.constant.PageInfo;
import com.collect.dto.WebResponseDO;
import com.collect.entity.BaseRuleInfoParamPO;

import java.util.List;

public interface BaseRuleInfoParamService {
    WebResponseDO<List<BaseRuleInfoParamPO>> getAll(BaseRuleInfoParamPO baseRuleInfoParamPO, PageInfo pageInfo);

    void add(BaseRuleInfoParamPO baseRuleInfoParamPO);

    void update(BaseRuleInfoParamPO baseRuleInfoParamPO);

    void delete(Long incId);
}
