package com.collect.service;

import com.collect.entity.NodeProgramConfigInfoPO;

import java.util.List;

/**
 * 节点程序配置信息服务接口
 */
public interface NodeProgramConfigInfoService {
    
    /**
     * 根据节点代码查询节点程序配置信息
     * @param nodeCode 节点代码
     * @return 节点程序配置信息
     */
    NodeProgramConfigInfoPO getByNodeCode(String nodeCode);
    
    /**
     * 查询所有节点程序配置信息
     * @return 节点程序配置信息列表
     */
    List<NodeProgramConfigInfoPO> getAll();
    
    /**
     * 根据节点调用名称查询节点程序配置信息
     * @param nodeCallName 节点调用名称
     * @return 节点程序配置信息列表
     */
    List<NodeProgramConfigInfoPO> getByNodeCallName(String nodeCallName);
    
    /**
     * 根据节点代码名称查询节点程序配置信息
     * @param nodeCodeName 节点代码名称
     * @return 节点程序配置信息列表
     */
    List<NodeProgramConfigInfoPO> getByNodeAttriId(String nodeCodeName);
    
    /**
     * 添加节点程序配置信息
     * @param nodeProgramConfigInfoPO 节点程序配置信息
     * @return 是否成功
     */
    boolean add(NodeProgramConfigInfoPO nodeProgramConfigInfoPO);
    
    /**
     * 更新节点程序配置信息
     * @param nodeProgramConfigInfoPO 节点程序配置信息
     * @return 是否成功
     */
    boolean update(NodeProgramConfigInfoPO nodeProgramConfigInfoPO);
    
    /**
     * 根据节点代码删除节点程序配置信息
     * @param nodeCode 节点代码
     * @return 是否成功
     */
    boolean deleteByNodeCode(String nodeCode);
    
    /**
     * 批量添加节点程序配置信息
     * @param nodeInfoList 节点程序配置信息列表
     * @return 成功添加的记录数
     */
    int batchAdd(List<NodeProgramConfigInfoPO> nodeInfoList);

    int batchUpdate(List<NodeProgramConfigInfoPO> nodeInfoList);
    /**
     * 根据条件查询节点程序配置信息
     * @param condition 查询条件，如果为null或不传任何属性则查询全部
     * @return 查询结果列表
     */
    List<NodeProgramConfigInfoPO> getByCondition(NodeProgramConfigInfoPO condition);
} 