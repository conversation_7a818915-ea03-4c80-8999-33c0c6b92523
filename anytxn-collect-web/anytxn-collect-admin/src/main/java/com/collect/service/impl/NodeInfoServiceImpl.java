package com.collect.service.impl;

import com.collect.assembly.NodeInfoAssembler;
import com.collect.constant.PageInfo;
import com.collect.dto.NodeInfoReqDTO;
import com.collect.dto.WebResponseDO;
import com.collect.entity.NodeInfoDO;
import com.collect.entity.NodeInfoPO;
import com.collect.mapper.NodeInfoMapper;
import com.collect.service.NodeInfoService;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 10:30
 **/
@Service
public class NodeInfoServiceImpl implements NodeInfoService {

    @Autowired
    private NodeInfoMapper nodeInfoMapper;
    @Override
    public NodeInfoPO getByNodeCode(String nodeCode) {
        return nodeInfoMapper.selectByNodeCode(nodeCode);
    }

    @Override
    public void save(NodeInfoPO nodeInfoPO) {
        nodeInfoMapper.insert(nodeInfoPO);
    }

    @Override
    public void update(NodeInfoPO nodeInfoPO) {
        nodeInfoMapper.update(nodeInfoPO);
    }

    @Override
    public void deleteByNodeCode(String nodeCode) {
        nodeInfoMapper.deleteByNodeCode(nodeCode);
    }


    @Override
    public List<NodeInfoPO> getAll() {
        return nodeInfoMapper.selectAll();
    }

    @Override
    public WebResponseDO<List<NodeInfoDO>> selectAllWithConfigs(NodeInfoReqDTO nodeInfoReqDTO, PageInfo webPage) {
        return queryByCondition(nodeInfoReqDTO, webPage);
    }

    @Override
    public WebResponseDO<NodeInfoDO> selectAllWithConfigsByNodeCode(String nodeCode) {
        NodeInfoDO nodeInfoDO = nodeInfoMapper.selectAllWithConfigsByNodeCode(nodeCode);
        return WebResponseDO.of(nodeInfoDO);
    }

    public WebResponseDO<List<NodeInfoDO>> queryByCondition(NodeInfoReqDTO nodeInfoReqDTO, PageInfo webPage){
        NodeInfoPO nodeInfoPO = NodeInfoAssembler.of().do2poObject(nodeInfoReqDTO);
        try (Page<?> page = PageMethod.startPage(webPage.getPageNo(), webPage.getPageSize())) {
            List<NodeInfoDO> list = nodeInfoMapper.selectAllWithConfigs(nodeInfoPO);
            return WebResponseDO.of(PageInfo.page(page.getPageNum(), page.getPageSize(), page.getTotal()), list);
        }
    }
}
