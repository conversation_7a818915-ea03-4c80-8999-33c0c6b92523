package com.collect.service.impl;

import com.anytxn.base.utils.ObjectUtils;
import com.collect.entity.NodeProgramConfigInfoPO;
import com.collect.mapper.NodeProgramConfigInfoMapper;
import com.collect.service.NodeProgramConfigInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 节点程序配置信息服务实现类
 */
@Service
public class NodeProgramConfigInfoServiceImpl implements NodeProgramConfigInfoService {

    @Autowired
    private NodeProgramConfigInfoMapper nodeProgramConfigInfoMapper;

    @Override
    public NodeProgramConfigInfoPO getByNodeCode(String nodeCode) {
        return nodeProgramConfigInfoMapper.selectByNodeCode(nodeCode);
    }

    @Override
    public List<NodeProgramConfigInfoPO> getAll() {
        return nodeProgramConfigInfoMapper.selectAll();
    }

    @Override
    public List<NodeProgramConfigInfoPO> getByNodeCallName(String nodeCallName) {
        return nodeProgramConfigInfoMapper.selectByNodeCallName(nodeCallName);
    }

    @Override
    public List<NodeProgramConfigInfoPO> getByNodeAttriId(String nodeAttriId) {
        return nodeProgramConfigInfoMapper.selectByNodeAttriId(nodeAttriId);
    }

    @Override
    public boolean add(NodeProgramConfigInfoPO nodeProgramConfigInfoPO) {
        // 如果未设置节点代码，则自动生成
        if (nodeProgramConfigInfoPO.getNodeCode() == null || nodeProgramConfigInfoPO.getNodeCode().isEmpty()) {
            nodeProgramConfigInfoPO.setNodeCode(UUID.randomUUID().toString().replace("-", "").substring(0, 32));
        }
        return nodeProgramConfigInfoMapper.insert(nodeProgramConfigInfoPO) > 0;
    }

    @Override
    public boolean update(NodeProgramConfigInfoPO nodeProgramConfigInfoPO) {
        return nodeProgramConfigInfoMapper.update(nodeProgramConfigInfoPO) > 0;
    }

    @Override
    public boolean deleteByNodeCode(String nodeCode) {
        return nodeProgramConfigInfoMapper.deleteByNodeCode(nodeCode) > 0;
    }

    @Override
    public int batchAdd(List<NodeProgramConfigInfoPO> nodeInfoList) {
        if (nodeInfoList == null || nodeInfoList.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (NodeProgramConfigInfoPO info : nodeInfoList) {
            if (add(info)) {
                count++;
            }
        }
        return count;
    }

    @Override
    public int batchUpdate(List<NodeProgramConfigInfoPO> nodeInfoList) {
        if (nodeInfoList == null || nodeInfoList.isEmpty()) {
            return 0;
        }
        int count = 0;
        for (NodeProgramConfigInfoPO info : nodeInfoList) {
            if(ObjectUtils.isNull(getByNodeAttriId(info.getNodeAttriId()))){
                if (add(info)) {
                    count++;
                }
            }else {
                if (update(info)) {
                    count++;
                }
            }
        }
        return count;
    }

    @Override
    public List<NodeProgramConfigInfoPO> getByCondition(NodeProgramConfigInfoPO condition) {
        if (condition == null) {
            condition = new NodeProgramConfigInfoPO();
        }
        return nodeProgramConfigInfoMapper.selectByCondition(condition);
    }
} 