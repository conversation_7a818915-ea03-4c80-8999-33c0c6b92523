package com.collect.service.impl;

import com.collect.entity.WcsActionTdcPO;
import com.collect.mapper.WcsActionTdcMapper;
import com.collect.service.WcsActionTdcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 17:36
 **/
@Service
public class WcsActionTdcServiceImpl implements WcsActionTdcService {

    @Autowired
    private WcsActionTdcMapper wcsActionTdcMapper;

    @Override
    public WcsActionTdcPO selectById(Integer id) {
        return wcsActionTdcMapper.selectById(id);
    }

    @Override
    public List<WcsActionTdcPO> selectByCaseCode(String caseCode) {
        return wcsActionTdcMapper.selectByCaseCode(caseCode);
    }

    @Override
    public void insert(WcsActionTdcPO wcsActionTdcPO) {
        if (wcsActionTdcPO != null) {
            wcsActionTdcMapper.insert(wcsActionTdcPO);
        }
    }

    @Override
    public void updateById(WcsActionTdcPO wcsActionTdcPO) {
        if (wcsActionTdcPO != null) {
            wcsActionTdcMapper.update(wcsActionTdcPO);
        }
    }

    @Override
    public void deleteById(Integer id) {
        if (id != null) {
            wcsActionTdcMapper.deleteById(id);
        }
    }
}
