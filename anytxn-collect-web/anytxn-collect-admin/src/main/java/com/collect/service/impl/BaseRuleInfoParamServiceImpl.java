package com.collect.service.impl;


import com.collect.constant.PageInfo;
import com.collect.dto.WebResponseDO;
import com.collect.entity.BaseRuleInfoParamPO;
import com.collect.mapper.BaseRuleInfoParamMapper;
import com.collect.service.BaseRuleInfoParamService;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BaseRuleInfoParamServiceImpl implements BaseRuleInfoParamService {

    @Autowired
    private BaseRuleInfoParamMapper baseRuleInfoParamMapper;

    @Override
    public WebResponseDO<List<BaseRuleInfoParamPO>> getAll(BaseRuleInfoParamPO baseRuleInfoParamPO, PageInfo pageInfo) {
        return queryByCondition(baseRuleInfoParamPO, pageInfo);
    }

    @Override
    public void add(BaseRuleInfoParamPO baseRuleInfoParamPO) {
        baseRuleInfoParamPO.setCreateUser("system");
        baseRuleInfoParamPO.setUpdateUser("system");
        baseRuleInfoParamPO.setRuleVerNo("1");
        baseRuleInfoParamPO.setCrcdOrgNo("013");
        baseRuleInfoParamPO.setVersion(0);
        baseRuleInfoParamPO.setParamSts("0");
        baseRuleInfoParamMapper.insert(baseRuleInfoParamPO);
    }

    @Override
    public void update(BaseRuleInfoParamPO baseRuleInfoParamPO) {
        baseRuleInfoParamMapper.update(baseRuleInfoParamPO);
    }

    @Override
    public void delete(Long incId) {
        if (incId != null) {
            baseRuleInfoParamMapper.deleteById(incId);
        }
    }

    public WebResponseDO<List<BaseRuleInfoParamPO>> queryByCondition(BaseRuleInfoParamPO baseRuleInfoParamPO, PageInfo webPage) {
        try (Page<?> page = PageMethod.startPage(webPage.getPageNo(), webPage.getPageSize())) {
            List<BaseRuleInfoParamPO> list = baseRuleInfoParamMapper.selectAll(baseRuleInfoParamPO);
            return WebResponseDO.of(PageInfo.page(page.getPageNum(), page.getPageSize(), page.getTotal()), list);
        }
    }
}
