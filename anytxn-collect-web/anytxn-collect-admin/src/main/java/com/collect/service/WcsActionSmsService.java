package com.collect.service;

import com.collect.entity.WcsActionSmsPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 20:30
 **/
public interface WcsActionSmsService {
    int insertWcsActionSms(WcsActionSmsPO wcsActionSmsPO);

    int deleteWcsActionSmsById(Long id);

    WcsActionSmsPO selectWcsActionSmsById(Long id);

    int updateWcsActionSms(WcsActionSmsPO wcsActionSmsPO);

    List<WcsActionSmsPO> selectWcsActionSmsByCaseCode(String caseCode);
}
