package com.collect;

import com.anytxn.base.config.EnableConfigCenter;
import com.anytxn.base.spring.ApplicationRun;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 参数服务启动类
 *
 * <AUTHOR>
 * @date 2024/10/16
 */
@EnableConfigCenter
@SpringBootApplication
@MapperScan(basePackages = {"com.anytxn", "com.collect"}, annotationClass = Mapper.class)
@ComponentScan(basePackages = {"com.anytxn.base","com.anytxn", "com.collect"})
public class ApplicationStarted {

    public static void main(String[] args) {
        ApplicationRun.run(ApplicationStarted.class, args);
    }

}

