package com.collect.common;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * API标准响应格式
 * @param <T> 数据类型
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    /**
     * 响应头信息
     */
    private Header header;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 头部信息类
     */
    public static class Header {
        /**
         * 错误码
         */
        private String errorCode;
        
        /**
         * 错误消息
         */
        private String errorMsg;
        
        public Header() {
        }
        
        public Header(String errorCode, String errorMsg) {
            this.errorCode = errorCode;
            this.errorMsg = errorMsg;
        }
        
        public String getErrorCode() {
            return errorCode;
        }
        
        public void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }
        
        public String getErrorMsg() {
            return errorMsg;
        }
        
        public void setErrorMsg(String errorMsg) {
            this.errorMsg = errorMsg;
        }
    }
    
    /**
     * 创建成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setHeader(new Header("000000", "操作成功"));
        response.setData(data);
        return response;
    }
    
    /**
     * 创建空数据成功响应
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> success() {
        return success(null);
    }
    
    /**
     * 创建错误响应
     * @param errorCode 错误码
     * @param errorMsg 错误消息
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(String errorCode, String errorMsg) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setHeader(new Header(errorCode, errorMsg));
        return response;
    }
    
    /**
     * 创建错误响应
     * @param errorMsg 错误消息
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> error(String errorMsg) {
        return error("999999", errorMsg);
    }
    
    /**
     * 从Result转换为ApiResponse
     * @param result Result对象
     * @param <T> 数据类型
     * @return API响应
     */
    public static <T> ApiResponse<T> fromResult(Result<T> result) {
        if (result.getCode() == 200) {
            return success(result.getData());
        } else {
            return error(result.getCode().toString(), result.getMessage());
        }
    }
    
    public Header getHeader() {
        return header;
    }
    
    public void setHeader(Header header) {
        this.header = header;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
} 