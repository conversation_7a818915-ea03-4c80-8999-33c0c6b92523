package com.collect.common;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 通用响应结果类
 * @param <T> 数据类型
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> {
    /**
     * 状态码
     */
    private Integer code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 数据
     */
    private T data;
    
    /**
     * 成功结果
     * @param <T> 数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> success() {
        return success(null);
    }
    
    /**
     * 成功结果
     * @param data 数据
     * @param <T> 数据类型
     * @return 成功的响应结果
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("操作成功");
        result.setData(data);
        return result;
    }
    
    /**
     * 失败结果
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> error(String message) {
        return error(500, message);
    }
    
    /**
     * 失败结果
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败的响应结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }


    /**
     * 获取 状态码
     *
     * @return code 状态码
     */
    public Integer getCode() {
        return this.code;
    }

    /**
     * 设置 状态码
     *
     * @param code 状态码
     */
    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 获取 消息
     *
     * @return message 消息
     */
    public String getMessage() {
        return this.message;
    }

    /**
     * 设置 消息
     *
     * @param message 消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取 数据
     *
     * @return data 数据
     */
    public T getData() {
        return this.data;
    }

    /**
     * 设置 数据
     *
     * @param data 数据
     */
    public void setData(T data) {
        this.data = data;
    }
}