package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.NodeFlowchartInfo;
import com.collect.service.NodeFlowchartInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 节点流程图信息控制器
 * 所有接口都使用POST方法
 */
@RestController
@RequestMapping(value = "/api/nodeFlowchart", produces = MediaType.APPLICATION_JSON_VALUE)
public class NodeFlowchartInfoController {

    @Autowired
    private NodeFlowchartInfoService nodeFlowchartInfoService;


    /**
     * 添加节点流程图信息
     * @param wrapper 节点流程图信息包装体
     * @return 添加结果
     */
    @PostMapping("/add")
    public ApiResponse<Void> add(@RequestBody RequestWrapper<NodeFlowchartInfo> wrapper) {
        NodeFlowchartInfo nodeFlowchartInfo = wrapper.getBody();
        boolean success = nodeFlowchartInfoService.add(nodeFlowchartInfo);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("添加节点流程图失败");
        }
    }

    /**
     * 更新节点流程图信息
     * @param wrapper 节点流程图信息包装体(包含id)
     * @return 更新结果
     */
    @PostMapping("/update")
    public ApiResponse<Void> update(@RequestBody RequestWrapper<NodeFlowchartInfo> wrapper) {
        NodeFlowchartInfo nodeFlowchartInfo = wrapper.getBody();
        boolean success = nodeFlowchartInfoService.update(nodeFlowchartInfo);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("更新节点流程图失败");
        }
    }

    /**
     * 根据ID删除节点流程图信息
     * @param wrapper 包含id的请求包装体
     * @return 删除结果
     */
    @PostMapping("/deleteById")
    public ApiResponse<Void> deleteById(@RequestBody RequestWrapper<NodeFlowchartInfo> wrapper) {
        NodeFlowchartInfo nodeFlowchartInfo = wrapper.getBody();
        Long id = nodeFlowchartInfo.getId();
        boolean success = nodeFlowchartInfoService.deleteById(id);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除节点流程图失败");
        }
    }

    /**
     * 根据流程图ID删除节点流程图信息
     * @param wrapper 包含flowchartId的请求包装体
     * @return 删除结果
     */
    @PostMapping("/deleteByFlowchartId")
    public ApiResponse<Void> deleteByFlowchartId(@RequestBody RequestWrapper<NodeFlowchartInfo> wrapper) {
        NodeFlowchartInfo nodeFlowchartInfo = wrapper.getBody();
        String flowchartId = nodeFlowchartInfo.getFlowchartId();
        boolean success = nodeFlowchartInfoService.deleteByFlowchartId(flowchartId);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除节点流程图失败");
        }
    }

    /**
     * 批量添加节点流程图信息
     * @param wrapper 节点流程图信息列表包装体
     * @return 添加结果
     */
    @PostMapping("/batchAdd")
    public ApiResponse<Void> batchAdd(@RequestBody RequestWrapper<List<NodeFlowchartInfo>> wrapper) {
        List<NodeFlowchartInfo> nodeFlowchartInfoList = wrapper.getBody();
        boolean allSuccess = true;
        for (NodeFlowchartInfo nodeFlowchartInfo : nodeFlowchartInfoList) {
            boolean success = nodeFlowchartInfoService.add(nodeFlowchartInfo);
            if (!success) {
                allSuccess = false;
            }
        }
        if (allSuccess) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("部分节点流程图添加失败");
        }
    }

    /**
     * 通用查询接口，根据条件查询节点流程图信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * 对于flowchartName属性，将使用模糊匹配(LIKE)
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的节点流程图信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<NodeFlowchartInfo>> query(@RequestBody(required = false) RequestWrapper<NodeFlowchartInfo> wrapper) {
        NodeFlowchartInfo condition = wrapper != null ? wrapper.getBody() : null;
        List<NodeFlowchartInfo> result = nodeFlowchartInfoService.getByCondition(condition);
        return ApiResponse.success(result);
    }
} 