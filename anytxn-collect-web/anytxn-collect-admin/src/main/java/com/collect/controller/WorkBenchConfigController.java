package com.collect.controller;

import com.collect.assembly.WcsActionTdcAssembler;
import com.collect.assembly.WorkBenchConfigAssembler;
import com.collect.constant.ResponseDTO;
import com.collect.dto.WcsActionTdcReqDTO;
import com.collect.dto.WebRequest;
import com.collect.dto.WorkBenchConfigReqDTO;
import com.collect.entity.WcsActionTdcPO;
import com.collect.entity.WorkBenchConfigPO;
import com.collect.service.WorkBenchConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/workBenchConfig")
public class WorkBenchConfigController {

    @Autowired
    private WorkBenchConfigService workBenchConfigService;

    @RequestMapping("/query")
    public ResponseDTO<List<WorkBenchConfigPO>> getAll(@RequestBody WebRequest<WorkBenchConfigReqDTO> webRequest) {
        WorkBenchConfigReqDTO config = webRequest.getBody();
        WorkBenchConfigPO configPO = WorkBenchConfigAssembler.of().do2poObject(config);
        List<WorkBenchConfigPO> workBenchConfigPOList = workBenchConfigService.selectAll(configPO);
        return ResponseDTO.success(workBenchConfigPOList);
    }

    @RequestMapping("/add")
    public ResponseEntity<Integer> add(@RequestBody WebRequest<WorkBenchConfigReqDTO> webRequest) {
        WorkBenchConfigReqDTO config = webRequest.getBody();
        WorkBenchConfigPO configPO = WorkBenchConfigAssembler.of().do2poObject(config);
        return ResponseEntity.ok(workBenchConfigService.insertSelective(configPO));
    }

    @RequestMapping("/edit")
    public ResponseEntity<Integer> edit(@RequestBody WebRequest<WorkBenchConfigReqDTO> webRequest) {
        WorkBenchConfigReqDTO config = webRequest.getBody();
        WorkBenchConfigPO configPO = WorkBenchConfigAssembler.of().do2poObject(config);
        return ResponseEntity.ok(workBenchConfigService.updateByPrimaryKeySelective(configPO));
    }

    // 其他端点实现...
}