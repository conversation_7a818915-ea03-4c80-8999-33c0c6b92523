package com.collect.controller;

import com.alibaba.nacos.api.naming.pojo.Instance;
import com.collect.util.HttpUtil;
import com.collect.util.NacosServiceUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:49
 **/
@RestController
public class ParamController {

    // 服务名称
    private final String serviceName = "anytxn-param";
    private final String maintainUrl = "/anytxn-param/nm/param/maintain";
    private final String queryUrl = "/anytxn-param/nq/param/query";
    private final String pageQueryUrl = "/anytxn-param/nq/param/pageQuery";

    @PostMapping("/param/rules/create")
    public Map<String, Object> create(@RequestBody String param) {
        return comHandler(param, maintainUrl);
    }

    @PostMapping("/param/rules/update")
    public Map<String, Object> update(@RequestBody String param) {
        return comHandler(param, maintainUrl);
    }

    @PostMapping("/param/rules/query")
    public Map<String, Object> query(@RequestBody String param) {
        return comHandler(param, queryUrl);
    }

    @PostMapping("/param/rules/pageQuery")
    public Map<String, Object> pageQueryUrl(@RequestBody String param) {
        return comHandler(param, pageQueryUrl);
    }

    /**
     * 通用处理方法
     * @param param
     * @param endpoint
     * @return
     */
    private Map<String, Object> comHandler(String param, String endpoint) {
        // 获取服务的第一个实例，如果不存在，则抛出异常
        Optional<Instance> optionalInstance = Optional.ofNullable(NacosServiceUtil.getFirstInstance(serviceName));
        Instance instance = optionalInstance.orElseThrow(() -> new RuntimeException("No instance found for service: " + serviceName));
        // 根据实例信息和维护URL构建完整的请求URL
        String url = buildUrl(instance, endpoint);
        // 发送POST请求到构建的URL，并期望返回一个String类型的响应
        ResponseEntity<String> response = HttpUtil.sendPostRequest(url, param, String.class);
        // 处理响应，将其转换为一个包含响应数据的Map对象并返回
        return handleResponse(response);
    }

    /**
     * 构建请求的 URL
     *
     * @param instance
     * @param endpoint
     * @return
     */
    private String buildUrl(Instance instance, String endpoint) {
        return "http://" + instance.getIp() + ":" + instance.getPort() + endpoint;
    }

    /**
     * 处理响应
     *
     * @param response
     * @return
     */
    private Map<String, Object> handleResponse(ResponseEntity<String> response) {
        if (response.getStatusCode().is2xxSuccessful()) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 将响应的 body 解析为 Map
                Map<String, Object> responseBody = objectMapper.readValue(response.getBody(), Map.class);
                return responseBody;
            } catch (Exception e) {
                // 处理解析异常
                return Map.of("error", "Failed to parse response body");
            }
        } else {
            // 处理错误响应
            return Map.of("error", "Request failed with status code: " + response.getStatusCode());
        }
    }
}
