package com.collect.controller;

import com.collect.assembly.NodeInfoAssembler;
import com.collect.constant.PageResponseDTO;
import com.collect.constant.ResponseDTO;
import com.collect.dto.NodeInfoReqDTO;
import com.collect.dto.WebRequest;
import com.collect.dto.WebResponseDO;
import com.collect.entity.NodeInfoDO;
import com.collect.entity.NodeInfoPO;
import com.collect.entity.NodeProgramConfigInfoPO;
import com.collect.service.NodeInfoService;
import com.collect.service.NodeProgramConfigInfoService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 10:37
 **/
@RestController
@RequestMapping(value = "/param/nodeManage")
public class NodeInfoController {

    static {
        System.out.println("NodeInfoController is loaded");
    }

    private final NodeInfoService nodeInfoService;

    private final NodeProgramConfigInfoService nodeProgramConfigInfoService;

    public NodeInfoController(final NodeInfoService nodeInfoService, NodeProgramConfigInfoService nodeProgramConfigInfoService) {
        this.nodeInfoService = nodeInfoService;
        this.nodeProgramConfigInfoService = nodeProgramConfigInfoService;
    }

    @PostMapping("/pageQuery")
    public ResponseDTO<?> getAll(@RequestBody WebRequest<NodeInfoReqDTO> webRequest) {
        WebResponseDO<List<NodeInfoDO>> response = nodeInfoService.selectAllWithConfigs(webRequest.getBody(), webRequest.getPageInfo());
        return PageResponseDTO.success(response.getPageInfo(), response.getData());
    }

    @RequestMapping("/create")
    @Transactional
    public ResponseDTO<?> create(@RequestBody WebRequest<NodeInfoReqDTO> webRequest) {
        NodeInfoReqDTO nodeInfoReqDTO = webRequest.getBody();
        List<NodeProgramConfigInfoPO> nodeProgramConfigInfoPOList = nodeInfoReqDTO.getNodeManageList();
        NodeInfoPO nodeInfoPO = NodeInfoAssembler.of().do2poObject(nodeInfoReqDTO);
        nodeInfoService.save(nodeInfoPO);
        nodeProgramConfigInfoService.batchAdd(nodeProgramConfigInfoPOList);
        return ResponseDTO.success();
    }

    @RequestMapping("/update")
    public ResponseDTO<?> update(@RequestBody WebRequest<NodeInfoReqDTO> webRequest) {
        NodeInfoReqDTO nodeInfoReqDTO = webRequest.getBody();
        NodeInfoPO nodeInfoPO = NodeInfoAssembler.of().do2poObject(nodeInfoReqDTO);
        nodeInfoService.update(nodeInfoPO);
        nodeProgramConfigInfoService.batchUpdate(nodeInfoReqDTO.getNodeManageList());
        return ResponseDTO.success();
    }

    @RequestMapping("/delete")
    public ResponseDTO<?> delete(@RequestBody WebRequest<NodeInfoReqDTO> webRequest) {
        NodeInfoReqDTO nodeInfoReqDTO = webRequest.getBody();
        NodeInfoPO nodeInfoPO = NodeInfoAssembler.of().do2poObject(nodeInfoReqDTO);
        nodeInfoService.deleteByNodeCode(nodeInfoPO.getNodeCode());
        nodeProgramConfigInfoService.deleteByNodeCode(nodeInfoPO.getNodeCode());
        return ResponseDTO.success();
    }

    @RequestMapping("/query")
    public ResponseDTO<?> getByNodeCode(@RequestBody WebRequest<NodeInfoReqDTO> webRequest) {
        NodeInfoReqDTO nodeInfoReqDTO = webRequest.getBody();
        WebResponseDO<NodeInfoDO> response = nodeInfoService.selectAllWithConfigsByNodeCode(nodeInfoReqDTO.getNodeCode());
        return PageResponseDTO.success(response.getData());
    }
}
