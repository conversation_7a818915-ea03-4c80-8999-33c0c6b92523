package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.WcsCasState;
import com.collect.service.WcsCasStateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 案件状态信息控制器
 * 所有接口都使用POST方法
 */
@RestController
@RequestMapping(value = "/api/wcsCasState", produces = MediaType.APPLICATION_JSON_VALUE)
public class WcsCasStateController {

    @Autowired
    private WcsCasStateService wcsCasStateService;

    /**
     * 添加案件状态信息
     * @param wrapper 案件状态信息包装体
     * @return 添加结果
     */
    @PostMapping("/add")
    public ApiResponse<Void> add(@RequestBody RequestWrapper<WcsCasState> wrapper) {
        WcsCasState wcsCasState = wrapper.getBody();
        boolean success = wcsCasStateService.add(wcsCasState);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("添加案件状态信息失败");
        }
    }

    /**
     * 更新案件状态信息
     * @param wrapper 案件状态信息包装体(包含id)
     * @return 更新结果
     */
    @PostMapping("/update")
    public ApiResponse<Void> update(@RequestBody RequestWrapper<WcsCasState> wrapper) {
        WcsCasState wcsCasState = wrapper.getBody();
        boolean success = wcsCasStateService.update(wcsCasState);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("更新案件状态信息失败");
        }
    }


    /**
     * 通用查询接口，根据条件查询案件状态信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * 对于custName属性，将使用模糊匹配(LIKE)
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的案件状态信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<WcsCasState>> query(@RequestBody(required = false) RequestWrapper<WcsCasState> wrapper) {
        WcsCasState condition = wrapper != null ? wrapper.getBody() : null;
        List<WcsCasState> result = wcsCasStateService.getByCondition(condition);
        return ApiResponse.success(result);
    }
} 