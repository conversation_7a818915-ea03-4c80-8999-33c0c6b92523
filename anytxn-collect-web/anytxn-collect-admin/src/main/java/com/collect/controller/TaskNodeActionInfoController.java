package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.TaskNodeActionInfo;
import com.collect.service.TaskNodeActionInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务节点动作信息控制器
 */
@RestController
@RequestMapping(value = "/api/taskNodeActionInfo", produces = MediaType.APPLICATION_JSON_VALUE)
public class TaskNodeActionInfoController {

    @Autowired
    private TaskNodeActionInfoService taskNodeActionInfoService;

    /**
     * 通用查询接口，根据条件查询任务节点动作信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * 对于nodeAttriName属性，将使用模糊匹配(LIKE)
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的任务节点动作信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<TaskNodeActionInfo>> query(@RequestBody(required = false) RequestWrapper<TaskNodeActionInfo> wrapper) {
        TaskNodeActionInfo condition = wrapper != null ? wrapper.getBody() : null;
        List<TaskNodeActionInfo> result = taskNodeActionInfoService.getByCondition(condition);
        return ApiResponse.success(result);
    }
} 