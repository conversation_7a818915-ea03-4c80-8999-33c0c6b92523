package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.NodeProgramConfigInfoPO;
import com.collect.service.NodeProgramConfigInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 节点程序配置信息控制器
 * 所有接口都使用POST方法
 */
@RestController
@RequestMapping(value = "/api/nodeProgramConfig", produces = MediaType.APPLICATION_JSON_VALUE)
public class NodeProgramConfigInfoController {

    @Autowired
    private NodeProgramConfigInfoService nodeProgramConfigInfoService;


    /**
     * 通用查询接口，根据条件查询节点程序配置信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的节点程序配置信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<NodeProgramConfigInfoPO>> query(@RequestBody(required = false) RequestWrapper<NodeProgramConfigInfoPO> wrapper) {
        NodeProgramConfigInfoPO condition = wrapper != null ? wrapper.getBody() : null;
        List<NodeProgramConfigInfoPO> result = nodeProgramConfigInfoService.getByCondition(condition);
        return ApiResponse.success(result);
    }

    /**
     * 添加节点程序配置信息
     * @param wrapper 节点程序配置信息包装体
     * @return 添加结果
     */
    @PostMapping("/add")
    public ApiResponse<Void> add(@RequestBody RequestWrapper<NodeProgramConfigInfoPO> wrapper) {
        NodeProgramConfigInfoPO nodeProgramConfigInfoPO = wrapper.getBody();
        boolean success = nodeProgramConfigInfoService.add(nodeProgramConfigInfoPO);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("添加节点程序配置失败");
        }
    }

    /**
     * 更新节点程序配置信息
     * @param wrapper 节点程序配置信息包装体(包含nodeCode)
     * @return 更新结果
     */
    @PostMapping("/update")
    public ApiResponse<Void> update(@RequestBody RequestWrapper<NodeProgramConfigInfoPO> wrapper) {
        NodeProgramConfigInfoPO nodeProgramConfigInfoPO = wrapper.getBody();
        boolean success = nodeProgramConfigInfoService.update(nodeProgramConfigInfoPO);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("更新节点程序配置失败");
        }
    }

    /**
     * 删除节点程序配置信息
     * @param wrapper 包含nodeCode的请求包装体
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ApiResponse<Void> delete(@RequestBody RequestWrapper<NodeProgramConfigInfoPO> wrapper) {
        NodeProgramConfigInfoPO nodeProgramConfigInfoPO = wrapper.getBody();
        String nodeCode = nodeProgramConfigInfoPO.getNodeCode();
        boolean success = nodeProgramConfigInfoService.deleteByNodeCode(nodeCode);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除节点程序配置失败");
        }
    }

    /**
     * 批量添加节点程序配置信息
     * @param wrapper 节点程序配置信息列表包装体
     * @return 添加结果
     */
    @PostMapping("/batchAdd")
    public ApiResponse<Void> batchAdd(@RequestBody RequestWrapper<List<NodeProgramConfigInfoPO>> wrapper) {
        List<NodeProgramConfigInfoPO> nodeProgramConfigInfoPOList = wrapper.getBody();
        boolean allSuccess = true;
        for (NodeProgramConfigInfoPO nodeProgramConfigInfoPO : nodeProgramConfigInfoPOList) {
            boolean success = nodeProgramConfigInfoService.add(nodeProgramConfigInfoPO);
            if (!success) {
                allSuccess = false;
            }
        }
        if (allSuccess) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("部分节点程序配置添加失败");
        }
    }
} 