package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.ProcessingProgramManagement;
import com.collect.service.ProcessingProgramManagementService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 处理程序管理控制器
 * 所有接口都使用POST方法
 */
@RestController
@RequestMapping(value = "/api/processingProgram", produces = MediaType.APPLICATION_JSON_VALUE)
public class ProcessingProgramManagementController {

    @Autowired
    private ProcessingProgramManagementService processingProgramManagementService;

    /**
     * 通用查询接口，根据条件查询处理程序信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * 对于nodeAttriName属性，将使用模糊匹配(LIKE)
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的处理程序信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<ProcessingProgramManagement>> query(@RequestBody(required = false) RequestWrapper<ProcessingProgramManagement> wrapper) {
        ProcessingProgramManagement condition = wrapper != null ? wrapper.getBody() : null;
        List<ProcessingProgramManagement> result = processingProgramManagementService.getByCondition(condition);
        return ApiResponse.success(result);
    }

    /**
     * 添加处理程序信息
     * @param wrapper 处理程序信息包装体
     * @return 添加结果
     */
    @PostMapping("/add")
    public ApiResponse<Void> add(@RequestBody RequestWrapper<ProcessingProgramManagement> wrapper) {
        ProcessingProgramManagement processingProgram = wrapper.getBody();
        boolean success = processingProgramManagementService.add(processingProgram);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("添加处理程序失败");
        }
    }

    /**
     * 更新处理程序信息
     * @param wrapper 处理程序信息包装体(包含nodeAttriId)
     * @return 更新结果
     */
    @PostMapping("/update")
    public ApiResponse<Void> update(@RequestBody RequestWrapper<ProcessingProgramManagement> wrapper) {
        ProcessingProgramManagement processingProgram = wrapper.getBody();
        boolean success = processingProgramManagementService.update(processingProgram);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("更新处理程序失败");
        }
    }

    /**
     * 删除处理程序信息
     * @param wrapper 包含nodeAttriId的请求包装体
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ApiResponse<Void> delete(@RequestBody RequestWrapper<ProcessingProgramManagement> wrapper) {
        ProcessingProgramManagement processingProgram = wrapper.getBody();
        String nodeAttriId = processingProgram.getNodeAttriId();
        boolean success = processingProgramManagementService.deleteById(nodeAttriId);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除处理程序失败");
        }
    }

    /**
     * 批量添加处理程序信息
     * @param wrapper 处理程序信息列表包装体
     * @return 添加结果
     */
    @PostMapping("/batchAdd")
    public ApiResponse<Void> batchAdd(@RequestBody RequestWrapper<List<ProcessingProgramManagement>> wrapper) {
        List<ProcessingProgramManagement> processingProgramList = wrapper.getBody();
        boolean allSuccess = true;
        for (ProcessingProgramManagement processingProgram : processingProgramList) {
            boolean success = processingProgramManagementService.add(processingProgram);
            if (!success) {
                allSuccess = false;
            }
        }
        if (allSuccess) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("部分处理程序添加失败");
        }
    }

} 