
package com.collect.controller;

import com.collect.assembly.WcsBasRepaymentAssembler;
import com.collect.constant.ResponseDTO;
import com.collect.dto.WcsBasRepaymentReqDTO;
import com.collect.dto.WebRequest;
import com.collect.entity.WcsBasRepaymentPO;
import com.collect.service.WcsBasRepaymentService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/14 11:05
 **/
@RestController
@RequestMapping(value = "/wcs/basRepayment")
public class WcsBasRepaymentController {

    private final WcsBasRepaymentService wcsBasRepaymentService;

    public WcsBasRepaymentController(WcsBasRepaymentService wcsBasRepaymentService) {
        this.wcsBasRepaymentService = wcsBasRepaymentService;
    }

    @RequestMapping("/create")
    public ResponseDTO<?> create(@RequestBody WebRequest<WcsBasRepaymentReqDTO> webRequest) {
        WcsBasRepaymentReqDTO wcsBasRepaymentReqDTO = webRequest.getBody();
        WcsBasRepaymentPO wcsBasRepaymentPO = WcsBasRepaymentAssembler.of().do2poObject(wcsBasRepaymentReqDTO);
        int result = wcsBasRepaymentService.insertSelective(wcsBasRepaymentPO);
        return ResponseDTO.success();
    }

    @RequestMapping("/query")
    public ResponseDTO<?> query(@RequestBody WebRequest<WcsBasRepaymentReqDTO> webRequest) {
        WcsBasRepaymentReqDTO wcsBasRepaymentReqDTO = webRequest.getBody();
        List<WcsBasRepaymentPO> repaymentList = wcsBasRepaymentService.selectByCaseCode(wcsBasRepaymentReqDTO.getCaseCode());
        return ResponseDTO.success(repaymentList);
    }

    @RequestMapping("/queryAll")
    public ResponseDTO<?> queryAll() {
        List<WcsBasRepaymentPO> repaymentList = wcsBasRepaymentService.selectAll();
        return ResponseDTO.success(repaymentList);
    }

    @RequestMapping("/update")
    public ResponseDTO<?> update(@RequestBody WebRequest<WcsBasRepaymentReqDTO> webRequest) {
        WcsBasRepaymentReqDTO wcsBasRepaymentReqDTO = webRequest.getBody();
        WcsBasRepaymentPO wcsBasRepaymentPO = WcsBasRepaymentAssembler.of().do2poObject(wcsBasRepaymentReqDTO);
        int result = wcsBasRepaymentService.updateByPrimaryKeySelective(wcsBasRepaymentPO);
        return ResponseDTO.success();
    }

    @RequestMapping("/delete")
    public ResponseDTO<?> delete(@RequestBody WebRequest<Long> webRequest) {
        Long id = webRequest.getBody();
        int result = wcsBasRepaymentService.deleteByPrimaryKey(id);
        return ResponseDTO.success();
    }
}