package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.TagInfo;
import com.collect.entity.TaskNodeInfo;
import com.collect.service.TaskNodeInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 任务节点信息控制器
 */
@RestController
@RequestMapping(value = "/api/taskNodeInfo", produces = MediaType.APPLICATION_JSON_VALUE)
public class TaskNodeInfoController {

    @Autowired
    private TaskNodeInfoService taskNodeInfoService;

    /**
     * 通用查询接口，根据条件查询任务节点信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * 对于flowchartName属性，将使用模糊匹配(LIKE)
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的任务节点信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<TaskNodeInfo>> query(@RequestBody(required = false) RequestWrapper<TaskNodeInfo> wrapper) {
        TaskNodeInfo condition = wrapper != null ? wrapper.getBody() : null;
        List<TaskNodeInfo> result = taskNodeInfoService.getByCondition(condition);
        return ApiResponse.success(result);
    }

    /**
     * 添加 任务节点 信息
     * @param wrapper 标签信息包装体
     * @return 添加结果
     */
    @PostMapping("/add")
    public ApiResponse<Void> add(@RequestBody RequestWrapper<TaskNodeInfo> wrapper) {
        TaskNodeInfo tagInfo = wrapper.getBody();
        boolean success = taskNodeInfoService.add(tagInfo);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("添加任务节点失败");
        }
    }
} 