package com.collect.controller;

import com.collect.assembly.BaseRuleInfoParamAssembler;
import com.collect.constant.PageResponseDTO;
import com.collect.constant.ResponseDTO;
import com.collect.dto.BaseRuleInfoParamReqDTO;
import com.collect.dto.WebRequest;
import com.collect.dto.WebResponseDO;
import com.collect.entity.BaseRuleInfoParamPO;
import com.collect.service.BaseRuleInfoParamService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/12 14:49
 **/
@RestController
@RequestMapping(value = "/params/rules")
public class ParamsController {

    private final BaseRuleInfoParamService baseRuleInfoParamService;

    public ParamsController(BaseRuleInfoParamService baseRuleInfoParamService) {
        this.baseRuleInfoParamService = baseRuleInfoParamService;
    }
    // 服务名称

    @PostMapping("/create")
    public ResponseDTO<?> create(@RequestBody WebRequest<BaseRuleInfoParamReqDTO> webRequest) {
        BaseRuleInfoParamReqDTO baseRuleInfoParamReqDTO = webRequest.getBody();
        BaseRuleInfoParamPO baseRuleInfoParamPO = BaseRuleInfoParamAssembler.of().do2poObject(baseRuleInfoParamReqDTO);
        baseRuleInfoParamService.add(baseRuleInfoParamPO);
        return ResponseDTO.success();
    }

    @PostMapping("/update")
    public ResponseDTO<?> update(@RequestBody WebRequest<BaseRuleInfoParamReqDTO> webRequest) {
        BaseRuleInfoParamReqDTO baseRuleInfoParamReqDTO = webRequest.getBody();
        BaseRuleInfoParamPO baseRuleInfoParamPO = BaseRuleInfoParamAssembler.of().do2poObject(baseRuleInfoParamReqDTO);
        baseRuleInfoParamService.update(baseRuleInfoParamPO);
        return ResponseDTO.success();
    }

    @PostMapping("/query")
    public ResponseDTO<?> query(@RequestBody WebRequest<BaseRuleInfoParamReqDTO> webRequest) {
        BaseRuleInfoParamReqDTO baseRuleInfoParamReqDTO = webRequest.getBody();
        BaseRuleInfoParamPO baseRuleInfoParamPO = BaseRuleInfoParamAssembler.of().do2poObject(baseRuleInfoParamReqDTO);
        WebResponseDO<List<BaseRuleInfoParamPO>> response = baseRuleInfoParamService.getAll(baseRuleInfoParamPO, webRequest.getPageInfo());
        return PageResponseDTO.success(response.getPageInfo(), response.getData());
    }

    @PostMapping("/pageQuery")
    public ResponseDTO<?> pageQuery(@RequestBody WebRequest<BaseRuleInfoParamReqDTO> webRequest) {
        return ResponseDTO.success();
    }
}
