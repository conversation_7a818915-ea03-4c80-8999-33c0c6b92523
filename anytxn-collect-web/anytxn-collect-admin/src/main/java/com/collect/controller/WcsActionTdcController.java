package com.collect.controller;

import com.collect.assembly.WcsActionTdcAssembler;
import com.collect.constant.ResponseDTO;
import com.collect.dto.NodeInfoReqDTO;
import com.collect.dto.WcsActionTdcReqDTO;
import com.collect.dto.WebRequest;
import com.collect.entity.WcsActionTdcPO;
import com.collect.service.WcsActionTdcService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 17:38
 **/
@RestController
@RequestMapping(value = "/wcs/tdc")
public class WcsActionTdcController {

    private final WcsActionTdcService wcsActionTdcService;

    public WcsActionTdcController(WcsActionTdcService wcsActionTdcService) {
        this.wcsActionTdcService = wcsActionTdcService;
    }

    @RequestMapping("/create")
    public ResponseDTO<?> create(@RequestBody WebRequest<WcsActionTdcReqDTO> webRequest) {
        WcsActionTdcReqDTO wcsActionTdcReqDTO = webRequest.getBody();
        WcsActionTdcPO wcsActionTdcPO = WcsActionTdcAssembler.of().do2poObject(wcsActionTdcReqDTO);
        wcsActionTdcService.insert(wcsActionTdcPO);
        return ResponseDTO.success();
    }

    @RequestMapping("/query")
    public ResponseDTO<?> query(@RequestBody WebRequest<WcsActionTdcReqDTO> webRequest) {
        WcsActionTdcReqDTO wcsActionTdcReqDTO = webRequest.getBody();
        WcsActionTdcPO wcsActionTdcPO = WcsActionTdcAssembler.of().do2poObject(wcsActionTdcReqDTO);
        List<WcsActionTdcPO> queryDate =  wcsActionTdcService.selectByCaseCode(wcsActionTdcPO.getCaseCode());
        return ResponseDTO.success(queryDate);
    }
}
