package com.collect.controller;

import com.collect.assembly.WcsActionSmsAssembler;
import com.collect.constant.ResponseDTO;
import com.collect.dto.WcsActionSmsReqDTO;
import com.collect.dto.WebRequest;
import com.collect.entity.WcsActionSmsPO;
import com.collect.service.WcsActionSmsService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/13 20:33
 **/
@RestController
@RequestMapping(value = "/wcs/sms")
public class WcsActionSmsController {

    private final WcsActionSmsService wcsActionSmsService;

    public WcsActionSmsController(WcsActionSmsService wcsActionSmsService) {
        this.wcsActionSmsService = wcsActionSmsService;
    }

    @RequestMapping("/create")
    public ResponseDTO<?> create(@RequestBody WebRequest<WcsActionSmsReqDTO> webRequest) {
        WcsActionSmsReqDTO wcsActionSmsReqDTO = webRequest.getBody();
        WcsActionSmsPO wcsActionSmsPO = WcsActionSmsAssembler.of().do2poObject(wcsActionSmsReqDTO);
        wcsActionSmsService.insertWcsActionSms(wcsActionSmsPO);
        return ResponseDTO.success();
    }

    @RequestMapping("/query")
    public ResponseDTO<?> query(@RequestBody WebRequest<WcsActionSmsReqDTO> webRequest) {
        WcsActionSmsReqDTO wcsActionSmsReqDTO = webRequest.getBody();
        List<WcsActionSmsPO> wcsActionSmsPOList = wcsActionSmsService.selectWcsActionSmsByCaseCode(wcsActionSmsReqDTO.getCaseCode());
        return ResponseDTO.success(wcsActionSmsPOList);
    }
}
