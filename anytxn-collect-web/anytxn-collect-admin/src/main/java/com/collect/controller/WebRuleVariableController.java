package com.collect.controller;


import com.alibaba.nacos.api.naming.pojo.Instance;
import com.anytxn.base.error.ServiceException;
import com.collect.assembly.WebRuleVariableAssembler;
import com.collect.constant.PageResponseDTO;
import com.collect.constant.ResponseDTO;
import com.collect.dto.RuleVariableReqDTO;
import com.collect.dto.WebRequest;

import com.collect.dto.WebResponseDO;
import com.collect.entity.RuleVariableDO;
import com.collect.service.RuleVariableService;

import com.collect.util.NacosServiceUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 规则因子
 *
 * <AUTHOR>
 * @date 2024 /12/17 11:28
 */
@RestController
@RequestMapping("/rule/variable")
public class WebRuleVariableController {

    static {
        System.out.println("WebRuleVariableController is loaded");
    }
    private final RuleVariableService ruleVariableService;

    /**
     * Instantiates a new Web rule variable controller.
     *
     * @param ruleVariableService the rule variable service
     */
    public WebRuleVariableController(final RuleVariableService ruleVariableService) {
        this.ruleVariableService = ruleVariableService;
    }

    /**
     * 查询所有的规则因子数据.
     *
     * @param webRequest the web request
     * @return the response dto
     */
    @RequestMapping("/queryRuleVariable")
    public ResponseDTO<?> queryRuleVariable(@RequestBody WebRequest<RuleVariableReqDTO> webRequest) {
        RuleVariableDO req = getRuleVariableDO(webRequest);
        WebResponseDO<List<RuleVariableDO>> response = ruleVariableService.queryAll(req, webRequest.getPageInfo());
        return PageResponseDTO.success(response.getPageInfo(), response.getData());
    }

    /**
     * 保存规则因子.
     *
     * @param webRequest the web request
     * @return the response dto
     */
    @RequestMapping("/save")
    public ResponseDTO<?> save(@RequestBody WebRequest<RuleVariableReqDTO> webRequest) {
        RuleVariableDO req = getRuleVariableDO(webRequest);
        try {
            ruleVariableService.save(req);
            return ResponseDTO.success();
        } catch (ServiceException e) {
            return ResponseDTO.fail(e.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 修改规则因子.
     *
     * @param webRequest the web request
     * @return the response dto
     */
    @RequestMapping("/update")
    public ResponseDTO<?> update(@RequestBody WebRequest<RuleVariableReqDTO> webRequest) {
        RuleVariableDO req = getRuleVariableDO(webRequest);
        try {
            ruleVariableService.update(req);
            return ResponseDTO.success();
        } catch (ServiceException e) {
            return ResponseDTO.fail(e.getErrorCode(), e.getMessage());
        }
    }

    /**
     * 根据规则ID 查询.
     *
     * @param webRequest the web request
     * @return the response dto
     */
    @RequestMapping("/queryByRuleType")
    public ResponseDTO<?> queryByRuleType(@RequestBody WebRequest<RuleVariableReqDTO> webRequest) {
        RuleVariableDO req = getRuleVariableDO(webRequest);
        WebResponseDO<List<RuleVariableDO>> response = ruleVariableService.queryByRuleType(req, webRequest.getPageInfo());
        return PageResponseDTO.success(response.getPageInfo(), response.getData());
    }

    @NotNull
    private RuleVariableDO getRuleVariableDO(final WebRequest<RuleVariableReqDTO> webRequest) {
        RuleVariableReqDTO body = webRequest.getBody();
        if (body == null) {
            body = new RuleVariableReqDTO();
        }
        return WebRuleVariableAssembler.of().dtoToDoObject(body);
    }
}
