package com.collect.controller;

import com.collect.common.ApiResponse;
import com.collect.common.RequestWrapper;
import com.collect.entity.TagInfo;
import com.collect.service.TagInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标签信息控制器
 * 所有接口都使用POST方法
 */
@RestController
@RequestMapping(value = "/api/tags", produces = MediaType.APPLICATION_JSON_VALUE)
public class TagInfoController {

    @Autowired
    private TagInfoService tagInfoService;

    
    /**
     * 通用查询接口，根据条件查询标签信息
     * 如果不传任何条件，则返回所有记录
     * 如果传入特定条件，则按条件筛选
     * 对于tagName属性，将使用模糊匹配(LIKE)
     * @param wrapper 包含查询条件的请求包装体
     * @return 符合条件的标签信息列表
     */
    @PostMapping("/query")
    public ApiResponse<List<TagInfo>> query(@RequestBody(required = false) RequestWrapper<TagInfo> wrapper) {
        TagInfo condition = wrapper != null ? wrapper.getBody() : null;
        List<TagInfo> result = tagInfoService.getByCondition(condition);
        return ApiResponse.success(result);
    }

    /**
     * 添加标签信息
     * @param wrapper 标签信息包装体
     * @return 添加结果
     */
    @PostMapping("/add")
    public ApiResponse<Void> add(@RequestBody RequestWrapper<TagInfo> wrapper) {
        TagInfo tagInfo = wrapper.getBody();
        boolean success = tagInfoService.add(tagInfo);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("添加标签失败");
        }
    }

    /**
     * 更新标签信息
     * @param wrapper 标签信息包装体(包含tagId)
     * @return 更新结果
     */
    @PostMapping("/update")
    public ApiResponse<Void> update(@RequestBody RequestWrapper<TagInfo> wrapper) {
        TagInfo tagInfo = wrapper.getBody();
        boolean success = tagInfoService.update(tagInfo);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("更新标签失败");
        }
    }

    /**
     * 删除标签信息
     * @param wrapper 包含tagId的请求包装体
     * @return 删除结果
     */
    @PostMapping("/delete")
    public ApiResponse<Void> delete(@RequestBody RequestWrapper<TagInfo> wrapper) {
        TagInfo tagInfo = wrapper.getBody();
        String tagId = tagInfo.getTagId();
        boolean success = tagInfoService.deleteById(tagId);
        if (success) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("删除标签失败");
        }
    }

    /**
     * 批量添加标签信息
     * @param wrapper 标签信息列表包装体
     * @return 添加结果
     */
    @PostMapping("/batchAdd")
    public ApiResponse<Void> batchAdd(@RequestBody RequestWrapper<List<TagInfo>> wrapper) {
        List<TagInfo> tagInfoList = wrapper.getBody();
        boolean allSuccess = true;
        for (TagInfo tagInfo : tagInfoList) {
            boolean success = tagInfoService.add(tagInfo);
            if (!success) {
                allSuccess = false;
            }
        }
        if (allSuccess) {
            return ApiResponse.success();
        } else {
            return ApiResponse.error("部分标签添加失败");
        }
    }
} 